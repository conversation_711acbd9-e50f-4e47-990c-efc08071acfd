
export default {
  title: {
    title: 'Booking Rules',
    add: 'Add',
    addTitle: 'Adding Rules',
    editTitle: 'Editing Rules',
    deleteTitle: 'Deleting Rules',
    setting: 'Time Settings',
    timePeriod: 'Time Period',
  },
  columns: {
    name: 'Name of rules',
    venueList: 'Appropriate venue',
    time: 'Last operator/operating time',
    status: 'Status',
    operate: 'Operation',
  },
  addRule: {
    remarkPlaceHolder: 'Not exceeding 200 words',
    chRuleName: 'Name of rules',
    enRuleName: 'English name of rules',
    appointmentType: 'Category of booking',
    appointmentRequirements: {
      label: 'Booking demand',
      lessDay: 'In advance of at least',
      lessDayOrder: 'Days to make an appointment',
      lessDayOrderExplain: '(by default 7 days, keying in 0 does not set any restrictions)',
      moreDay: 'In advance of at most',
      moreDayOrder: 'Days to make an appointment',
      moreDayOrderExplain: '(by default 14 days, keying in 0 does not set any restrictions)',
      hour: 'The restriction on total number of hours of booking for one person on the same day at the same venue is:',
      hourOrder: 'hour',
      hourOrderExpalain: '(by default 1 hour, keying in 0 does not set any restrictions)',
      timeOrder: 'Operating time that can be booked',
      weekDay: 'Every Monday to Friday',
      holiday: 'Every Saturday and Sunday',
      range: 'Specific time period',
    },
    scopeAll: 'All',
    whoCanAppointment: 'Who can book',
    canAppointmentPerson: 'Booking person',
    administrator: 'Is there a need for approval',
    isNeedReserve: 'Is there a need for booking',
    approvalIsRequired: 'Does booking require verification',
    approvalSetting: 'Approval time limit settings',
    approvalHours: 'Hours not yet reviewed will be deemed to be not approved',
    approvalHoursExplain: '(by default 2 hours, keying in 0 is by default not approved)',
    pushSettings: 'Reasons',
    pushSettingLabel: 'Push settings',
    pushSettingBefore: 'Before start of booking',
    pushSettingOrder: 'Minutes  give booking person push notification on WeChat',
    pushSettingOrderExplain: '(by default 30 minutes, by keying in 0 no notification will be sent)',
    applicableVenues: 'Appropriate venue',
    remarks: '备注',
    isEnable: 'Enable or not',
    tooltip: 'User may book during this time',
    signinTip: [
      'For different categories of booking, the mode of verification will be different.',
      'For normal category of verification: booking person can open the access control of the venue.。',
      'For student activity category of verification: property staff can sign to verify.',
    ],
    signinValidTime: 'Time for effective verification',
    signinValidTimeTip: [
      'Before booking',
      'minutes till start of booking',
      `minutes within which failure to sign in verification 
      constitutes no-show (if you fill in 0 you can sign to verify on the day of booking)`,
    ],
  },
  form: {
    keyword: 'Name of rule',
    chRuleName: 'Please fill in name of rule',
    enRuleName: 'Please fill in English name of rule',
    appointmentType: 'Please select category of booking',
    appointmentRequirements: 'Please fill in booking demand',
    whoCanAppointment: 'Please fill in who can book',
    administrator: 'Please select if you require approval',
    approvalSetting: 'Please key in settings for time limit of verification',
    approvalIsRequired: 'Please select if booking requires verification',
    pushSettings: 'Please select reasons for cancellation',
    isEnable: 'Please select if you wish to enable',
    lessDay: 'Please fill in the minimum number of days in advance for booking',
    moreDay: 'Please fill in the maximum number of days in advance for booking',
    hourDay: 'Please fill in the restriction on booking hours',
    operateTime: 'Please select the operation time for booking',
    range: 'Please select specific time period',
    number: 'Please key in integers greater than 0',
    fieldRequired: '此项必填',
  },
  action: {
    advancedSearch: 'Advanced Search',
    view: 'Check',
    cancel: 'Cancel',
    edit: 'Edit',
    time: 'Time Settings',
    delete: 'Delete',
    addPerson: 'Select Person',
  },
  msg: {
    success: 'Operation is successful',
    submitSuccess: 'Successfully submitted',
    cancelSuccess: 'Successfully cancelled',
    confirmAdd: 'Please set the “time settings” for this rule',
    assignSucc: 'Successfully assigned',
    handleSucc: 'Successfully completed',
    saveSucc: 'Successfully saved',
    imgType: 'Only {types} of images can be uploaded',
  },
  status: {
    true: 'Enable',
    false: 'Prohibit',
  },
  deleteRule: {
    haveOrder: `{venue} is already booked on {date} {start time}~{end time},
cancellation is required before you can operate!`,
    noOrder: 'Do you confirm cancellation of this rule?',
    deleteNotice: `After cancellation of this rule, you need to provide <span class="name">{name}</span>
set new rules, otherwise user cannot book <span class="name">{name}</span>, do you confirm cancellation?`,
  },
  orderType: {
    common: 'Normal Category',
    activity: 'Student Activity Category',
  },
  isTrueStatus: {
    TRUE: 'Yes',
    FALSE: 'No',
  },
};
