export default {
  title: {
    title: 'Booking Inquiry',
    export: 'Export',
    reserveDetail: 'Booking Details',
    fileName: 'Venue Booking Data',
  },
  columns: {
    number: 'Application number',
    venue: 'Book venue',
    orderTime: 'Booking time',
    orderName: 'Name of booking person',
    no: 'Student ID/Work ID',
    phone: 'Telephone contact',
    applyTime: 'Application time',
    status: 'Status',
    operate: 'Operation',
  },
  cancelModal: {
    title: 'Cancel Booking',
    label: 'Cancellation of booking will simultaneously remove occupancy records in the venue',
    reason: 'Reason',
    remarks: 'Remarks',
  },
  form: {
    advancedSearch: {
      status: 'Status',
      number: 'Application number',
      venue: 'Name of venue booked',
      orderName: 'Name of booking person',
      orderNamePlaceholder: 'Name or student ID/work ID',
      belongTo: 'Organisation',
      activityName: 'Name of activity',
      orderTime: 'Booking timeframe',
      applyTime: 'Application timeframe',
      selectPlaceholder: 'Please select',
    },
    simpleSearch: {
      status: 'Status',
      keyword: 'Application number or name of venue',
    },
  },
  reserveDetail: {
    status: 'Present status',
    time: 'Application time',
    sn: 'Application number',
    venue: 'Book venue',
    orderTime: 'Booking time',
    orderName: 'Name of booking person',
    orderPeople: 'Booking person',
    orderId: 'Work ID of booking person',
    belongTo: 'Department/school',
    organization: 'Name of organisation',
    teacherName: 'Instructing teacher',
    activityName: 'Name of activity',
    activityPeoples: 'Number of participants attending activity',
    activityGuests: 'Guests',
    isStudentReservation: 'Is a student organization booking',
    phone: 'Telephone contact',
    note: 'Description of application',
    remark: 'Remarks:',
    submit: 'Submit application',
    cancel: 'Cancel application',
    pass: 'Agree',
    reject: 'Return',
    signed: 'Signed confirmation',
    autoPass: 'Automatically Passed',
    arrived: 'Arrived',
    stepList: 'Approval record',
    info: 'Booking information',
    offTime: '（ This person {count} number of no-shows for）',
    addToBlackList: 'Addition into blacklist',
    createUserName: 'Operator',
  },
  blacklist: {
    title: 'Addition into blacklist',
    label: `Do you add {name} into blacklist? After addition of
a person into the blacklist, that person cannot book any venues.`,
    reason: 'Reasons',
    reasonPlace: 'Please key in the reason for addition into blacklist!',
  },
  action: {
    advancedSearch: 'Advanced Search',
    cancel: 'Cancel Booking',
    close: 'Close',
    ok: 'Confirm',
  },
  msg: {
    success: 'Operation is successful',
    submitSuccess: 'Successfully submitted',
    cancelSuccess: 'Successfully cancelled',
    confirmCancel: 'Do you confirm you wish to cancel application for repair?',
    assignSucc: 'Successfully assigned',
    handleSucc: 'Successfully completed',
    saveSucc: 'Successfully saved',
    imgType: 'Only {types} of images can be uploaded',
    notHasSearchItems: '请筛选需要导出的数据',
  },
  status: {
    PENDING: 'Pending',
    APPROVING: 'Approving',
    PASS: 'Approved',
    AUTO_PASS: 'Automatically Passed',
    PEDNING_SIGN: 'Pending Signature',
    SIGNED: 'Signed',
    REJECT: 'Rejected',
    CANCEL: 'Cancelled',
    OFF: 'No-show',
    RESERVED: 'Booked',
  },
  timeRangeType: {
    CREATETIME: 'Application timeframe',
    APPROVALTIME: 'Booking time frame',
  },
  cancelType: {
    OFF: 'No-show',
    OTHER: 'Others',
  },
  isStudentReservationStatus: {
    true: 'Yes',
    false: 'No',
  },
};
