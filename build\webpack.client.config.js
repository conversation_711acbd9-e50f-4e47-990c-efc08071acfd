import webpackMerge from 'webpack-merge';
import baseWebpackConfig from './webpack.base.config';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import TerserPlugin from 'terser-webpack-plugin';

const { CleanWebpackPlugin }  = require('clean-webpack-plugin');
const LoadablePlugin = require("@loadable/webpack-plugin");
const VueClientPlugin = require('vue-server-renderer/client-plugin');
const FederationStatsPlugin = require("webpack-federation-stats-plugin");
const FederationModuleIdPlugin = require("webpack-federation-module-id-plugin");
const ModuleFederationPlugin = require('webpack').container.ModuleFederationPlugin;

const isProd = process.env.NODE_ENV === 'production';

const webpackConfig = webpackMerge(baseWebpackConfig, {
  mode: isProd ? 'production' : 'development',
  entry: {
    app: './src/entry-client.js',
  },
  module: {
    rules: [
      {
        test: /\.(c|le)ss$/,
        use: [
          isProd ? {
            loader: MiniCssExtractPlugin.loader,
          } : {
            loader: 'vue-style-loader',
          },
          {
            loader: 'css-loader',
          },
          'postcss-loader',
          {
            loader: 'less-loader',
            options: {
              lessOptions: {
                javascriptEnabled: true,
              },
            },
          },
        ],
      },
    ],
  },
  plugins: isProd ? [
    new CleanWebpackPlugin(),
    new LoadablePlugin(),
    new VueClientPlugin(),
    new FederationStatsPlugin(),
    new FederationModuleIdPlugin(),
    new ModuleFederationPlugin({
      name: 'RemoteUI',
      filename: 'remoteEntry.js',
      remotes: {
        PimaRemoteUI: `PimaRemoteUI@${process.env.PIMA_REMOTE_UI_ENTRY_URL}`,
      },
    }),
  ] : [
    new VueClientPlugin(),
    new FederationStatsPlugin(),
    new FederationModuleIdPlugin(),
    new ModuleFederationPlugin({
      name: 'RemoteUI',
      filename: 'remoteEntry.js',
      remotes: {
        PimaRemoteUI: `PimaRemoteUI@${process.env.PIMA_REMOTE_UI_ENTRY_URL}`,
      },
    }),

  ],
  optimization: (isProd ?
    {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            format: {
              comments: false,
            },
          },
          extractComments: false,
        }),
      ],
    } : {}),
});

export default webpackConfig;
