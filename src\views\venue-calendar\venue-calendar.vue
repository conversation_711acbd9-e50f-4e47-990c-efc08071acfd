<template>
  <div
    v-if="!loadingProfile && hasP((P) => P.VenueCalendar.View)"
    class="page-panel clearfix"
  >
    <div class="content-panel">
      <!-- <a-page-header
        class="page-header-vaenue-calendar"
        :ghost="false"
      >
        <template #title>
          {{ t('title') }}
        </template>
      </a-page-header> -->

      <div class="panel-body">
        <div class="filter">
          <span class="filter-label">{{ t('simpleSearch.form.label.appointmentDate') }}</span>
          <div class="date-wrap">
            <div class="prev-next-wrap">
              <PrevNext
                v-if="today"
                v-model="today"
                @prev="onPrevNextChange"
                @next="onPrevNextChange"
              />
            </div>
            <a-date-picker
              v-model="datePickerDate"
              dropdown-class-name="date-picker-no-input"
              input-read-only
              @change="onDatePickerChange"
            >
              <a-button
                type="link"
                class="btn-calendar"
              >
                <i class="ico-calendar" />
              </a-button>
            </a-date-picker>
          </div>
          <span class="filter-label">{{ t('simpleSearch.form.label.venueCate') }}</span>
          <a-select
            v-model="venueCategoryIds"
            mode="multiple"
            :options="venueCateOpts"
            class="venue-cate-select"
            :placeholder="t('simpleSearch.form.selectPlaceHolder')"
            @change="loadData"
          />
          <a-button
            v-if="canReserve()"
            class="filter-btn"
            type="primary"
            size="small"
            @click="handleHignReserve"
          >
            {{ t('btn.reserve') }}
          </a-button>
        </div>

        <TableList
          :columns="columns"
          :data-source="dataSource"
          :venues="venues"
          :selected-date="today"
          :is-loading="isLoading"
          :selected-time-list="selectedTimeList"
        />
      </div>
    </div>
    <ModalReserve
      v-model="isShowModalReserve"
      :payload="payload"
    />
  </div>
</template>


<script>
import moment from 'moment';
import { mapState } from 'vuex';
import OpreationMixin from '@/mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';
import PrevNext from './components/prev-next.vue';
import TableList from './components/table-list.vue';
import ModalReserve from './components/modal-reserve.vue';

export default {
  name: 'VenueCalendar',

  components: {
    PrevNext,
    TableList,
    ModalReserve,
  },

  mixins: [
    OpreationMixin,
    nsI18n('t', 'venueCalendar'),
  ],

  provide() {
    return {
      reloadCalendar: this.loadData,
      canReserve: this.canReserve,
    };
  },

  data() {
    return {
      today: null,
      datePickerDate: moment(),
      columns: [],
      selectedTimeList: [],
      isShowModalReserve: false,
      venueCategoryIds: [],
      selectedVenueTimeList: [],
    };
  },

  computed: {
    ...mapState({
      loadingProfile: (state) => state.user.loadingProfile,
      dataSource: (state) => state.venueCalendar.periods,
      venues: (state) => state.venueCalendar.venues,
      isLoading: (state) => state.venueCalendar.isLoadingCalendar,
      venueCate: (state) => state.basicDataCategory.dataSource,
    }),

    venueCateOpts() {
      return this.venueCate.map((cate) => ({
        ...cate,
        key: cate.id,
        value: cate.id,
        title: cate.name,
      }));
    },

    canReserve() {
      return () => this.hasP((P) => P.VenueCalendar.Reserve);
    },

    payload() {
      return this.getPayloadData().map((item) => ({
        ...item,
        selectedDate: moment(this.datePickerDate).format('yyyy-MM-DD'),
      }));
    },
  },

  watch: {
    loadingProfile(value) {
      if (value === false) {
        this.loadUserData();
      }
    },

    today(value) {
      this.datePickerDate = moment(value);
    },

    selectedTimeList(val) {
      if (!val.length) {
        this.selectedVenueTimeList = [];
      }
    },
  },

  mounted() {
    this.$store.dispatch('basicDataCategory/fetchCategoryList', {
      limit: -1,
      isEnabled: true,
    });
    this.today = new Date();
    this.loadData();
    this.loadUserData();
  },

  beforeMount() {
    if (this.$eventBus) {
      // 监听场馆预约
      this.$eventBus.$on('venue-reserve-select', this.handleVenueReserveSelect);
    }
  },

  beforeDestroy() {
    this.$eventBus.$off('venue-reserve-select');
  },

  methods: {
    handleHignReserve() {
      if (this.selectedTimeList.length) {
        this.isShowModalReserve = true;
      } else {
        this.$message.error(this.t('msg.noTimeError'));
      }
    },
    handleVenueReserveSelect({ time, venue, hasSelected }) {
      if (hasSelected) {
        const { reservationRuleTimeId } = time;
        const { venueId } = venue;
        // eslint-disable-next-line arrow-body-style
        const idxs = this.selectedVenueTimeList.findIndex((item) => {
          return item.time.reservationRuleTimeId === reservationRuleTimeId
            && item.venue.venueId === venueId;
        });
        this.selectedVenueTimeList.splice(idxs, 1);
      } else {
        this.selectedVenueTimeList.push({
          time,
          venue,
        });
      }
      const idx = this.selectedTimeList
        .findIndex((item) => item.venueReservationTimeId === time.venueReservationTimeId);
      if (idx > -1) {
        this.selectedTimeList.splice(idx, 1);
      } else {
        this.selectedTimeList.push(time);
      }
    },
    async loadUserData() {
      await this.$store.dispatch('venueCalendar/fetchCurrentUserInfo');
    },

    async loadData() {
      const payload = { reservationDate: moment(this.today).format('yyyy-MM-DD') };
      if (this.venueCategoryIds.length) {
        payload.venueCategoryIds = this.venueCategoryIds.join(',');
      }
      await this.$store.dispatch('venueCalendar/fetchVenueCalendar', payload);
      const columns = this.venues.map((item) => item.name);
      this.columns = columns;
      this.selectedTimeList = [];
      this.selectedVenueTimeList = [];
    },

    onPrevNextChange(value) {
      this.today = value;
      this.loadData();
    },

    onDatePickerChange(value) {
      this.today = value.toDate();
      this.loadData();
    },

    mergeVenueData(list) {
      const res = [];
      list.forEach(({ time, venue }) => {
        const { venueId } = venue;
        const idx = res.findIndex((item) => item.venueId === venueId);
        if (idx === -1) {
          res.push({
            venueId,
            venueChnName: venue.name,
            reservationType: venue.reservationType,
            timeList: [
              {
                ruleTimeId: [time.reservationRuleTimeId],
                venueReservationTimeId: [time.venueReservationTimeId],
                startTime: time.startTime,
                endTime: time.endTime,
              },
            ],
            costPoints: time.costPoints,
          });
        } else {
          res[idx].timeList.push({
            ruleTimeId: [time.reservationRuleTimeId],
            venueReservationTimeId: [time.venueReservationTimeId],
            startTime: time.startTime,
            endTime: time.endTime,
            costPoints: time.costPoints,
          });
        }
      });
      return res;
    },

    mergeVenueTimeData(list) {
      const res = [];

      const currentItem = {};
      Object.assign(currentItem, list[0]);

      list.forEach((item, idx) => {
        if (idx > 0) {
          if (item.startTime === currentItem.endTime) {
            currentItem.endTime = item.endTime;
            currentItem.venueReservationTimeId.push(...item.venueReservationTimeId);
            currentItem.ruleTimeId.push(...item.ruleTimeId);
          } else {
            res.push({ ...currentItem });
            Object.assign(currentItem, item);
          }
        }
      });
      res.push({ ...currentItem });

      return res;
    },

    getPayloadData() {
      const list = [...this.selectedVenueTimeList];
      // 按场馆合并时间段
      const mergedVenueData = this.mergeVenueData(list);
      const res = [];
      mergedVenueData.forEach((item) => {
        // 时间段排序
        const sortedList = item.timeList.sort((a, b) => {
          if (a.startTime > b.startTime) {
            return 1;
          }
          return -1;
        });
        // 合并相邻时间段
        const timeList = this.mergeVenueTimeData(sortedList);

        timeList.forEach((timeItem) => {
          res.push({
            ...item,
            ...timeItem,
          });
        });
      });

      return res;
    },
  },
};
</script>


<style lang="less">
.date-picker-no-input {
  padding-top: 40px;

  .ant-calendar-input-wrap {
    display: none;
  }
}

.ant-popover-message-title{
  padding-left: 0;
}
</style>

<style lang="less" scoped>
.filter-btn {
  float: right;
}
::v-deep .ant-calendar-picker {
  height: 40px;
}
.panel-body {
  height: calc(100vh - 40px);
}
.filter {
  margin-top: 12px;
  margin-bottom: 10px;

  .filter-label {
    vertical-align: text-bottom;
  }

  .date-wrap {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    margin-right: 10px;
    // border: 1px solid #ededed;

    .prev-next-wrap {
      display: inline-block;
      padding: 0 6px;
      border: 1px solid #ededed;

      ::v-deep .prev-next {
        height: 38px;
      }
    }

    .btn-calendar {
      padding: 0 14px;
      min-width: auto;
      height: 100%;
      border: 1px solid #8D0306;
      border-radius: 0;
    }

    .ico-calendar {
      display: inline-block;
      vertical-align: middle;
      margin-top: -3px;
      width: 24px;
      height: 24px;
      background: url('~@assets/img/calendar.png') no-repeat center center / auto 100%;
    }
  }

  .status-info{
    float: right;
    margin-top: 20px;
    .item {
      display: inline-block;
      margin-left: 32px;
      .ico {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: #F2F2F2;
        border: 1px solid rgba(0,0,0,0.65);
        vertical-align: -5px;
      }
      .ico-available {
        background: #FFF9EF;
        border-color: #F49B00;
      }
      .ico-signed {
        background: #E8F6E6;
        border-color: #4FAD4E;
      }
      .ico-closed {
        background: #F2F2F2;
        border-color:rgba(0,0,0,0.65);
      }
    }
  }
}
.venue-cate-select {
  width: 300px;
  margin-left: 8px;
  margin-top: -10px;
  border-color: #ededed;
}
</style>
