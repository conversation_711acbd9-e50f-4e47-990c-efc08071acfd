function setChild(data) {
  // console.log(data);
  if (data.children === null) {
    // eslint-disable-next-line
    data.isLeaf = true;
  } else {
    data.children.forEach((citem) => {
      setChild(citem);
    });
  }
}

export function getTree(tree) {
  const data = JSON.parse(JSON.stringify(tree));
  data.forEach((item, k) => {
    if (item.children === null) {
      // eslint-disable-next-line
      data[k].isLeaf = true;
    } else {
      item.children.forEach((citem) => {
        setChild(citem);
      });
    }
  });
  return data;
}

export function getSidebarMenuItemList(sidebarTree) {
  let items = [];
  if (Array.isArray(sidebarTree)) {
    sidebarTree.forEach((item) => {
      if (Array.isArray(item.children) && item.children.length > 0) {
        items = items.concat(getSidebarMenuItemList(item.children));
      } else {
        items.push(item);
      }
    });
  }
  return items;
}

export function isInRangeOfFileSize(file, byte = 1024 * 1024) {
  const { size } = file;
  return size <= byte;
}
