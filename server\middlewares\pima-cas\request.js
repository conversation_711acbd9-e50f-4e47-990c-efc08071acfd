const axios = require('axios');
const { v1: uuidv1 } = require('uuid');
const md5 = require('md5');
const qs = require('qs');
const { btoa } = require('./utils');

const HEADERS = Object.freeze({
  Accept: 'application/json',
  'Content-Type': 'application/x-www-form-urlencoded',
});

// 申请Token
// 调用数据中心接口
function loadAccessTokenData(baseURL, clientId, clientSecret, username) {
  const Authorization = btoa(`${clientId}:${clientSecret}`);
  const headers = {
    ...HEADERS,
    Authorization,
  };

  const nonce = md5(uuidv1());
  const timestamp = new Date().valueOf();
  const data = {
    clientId,
    nonce,
    timestamp,
    username,
  };

  const signature = md5(`clientId=${clientId}nonce=${nonce}timestamp=${timestamp}username=${username}${clientSecret}`);
  Object.assign(data, {
    signature,
  });

  const url = `${baseURL}/oauth/username/token`;
  return axios.post(url, qs.stringify(data), { headers });
}

// 刷新Token
// 调用数据中心接口
function loadRefreshTokenData(baseURL, refreshToken) {
  const data = { refreshToken };
  const url = `${baseURL}/oauth/token/refresh`;
  return axios.post(url, qs.stringify(data), { headers: HEADERS });
}

module.exports = {
  loadAccessTokenData,
  loadRefreshTokenData,
};
