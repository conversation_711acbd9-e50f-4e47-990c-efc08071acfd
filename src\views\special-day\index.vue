<template>
  <div
    v-if="hasP(P => P.SpecialDay.View)"
    class="page-panel clearfix"
  >
    <div class="content-panel">
      <a-page-header
        :ghost="false"
      >
        <a-form
          :colon="false"
          :form="form"
          layout="inline"
        >
          <a-form-item
            self-update
          >
            <a-input
              v-decorator="['keyword', {
                initialValue:''
              }]"
              :placeholder="$t('special.form.keywordsPlace')"
              style="width: 300px"
              allow-clear
              @pressEnter="search('search')"
              @change="debounce(onInputChange, 1000)()"
            >
              <span
                slot="prefix"
                class="iconfont icon-all_sousuo"
                @click="search"
              />
            </a-input>
          </a-form-item>
        </a-form>
        <template #extra>
          <a-button
            v-if="hasP(P => P.SpecialDay.Add)"
            type="primary"
            size="small"
            @click="handleAddSpecialDay"
          >
            {{ $t('special.action.add') }}
          </a-button>
        </template>
      </a-page-header>
      <div class="panel-body">
        <TableList
          :data-source="dataSource"
          :total-size="totalSize"
          :is-loading="isLoading"
          :page-info.sync="queryInfo"
          @pageChange="onPageChange"
          @edit="handleEditSpecialDay"
          @update="handleTableUpdated"
        />
      </div>
    </div>
    <a-modal
      v-model="visibles"
      :title="modalTitle"
      :width="900"
      :footer="null"
      :mask-closable="false"
      :destroy-on-close="true"
      :dialog-style="{ left: '10px', top: '20px' }"
      :wrap-class-name="'modal-wrap'"
    >
      <SpecialDayForm
        :id="record.id"
        :type="type"
        @update="handleTableUpdated"
        @close="handleCloseModal"
      />
    </a-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import OpreationMixin from '@/mixins/operation';
import TableList from './table-list.vue';
import SpecialDayForm from './special-day-form.vue';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, ...args);
    }, wait);
  };
}

export default {
  name: 'SpecialDay',

  components: {
    TableList,
    SpecialDayForm,
  },

  mixins: [OpreationMixin],

  data() {
    return {
      form: this.$form.createForm(this),
      queryInfo: {
        limit: 10,
        page: 1,
      },
      type: 'add',
      visibles: false,
      record: {},
    };
  },

  computed: {
    ...mapState({
      dataSource: (state) => state.specialDay.list,
      totalSize: (state) => state.specialDay.total,
      isLoading: (state) => state.specialDay.loading,
    }),
    modalTitle() {
      return this.type === 'add'
        ? this.$t('special.title.add')
        : this.$t('special.title.edit');
    },
  },

  mounted() {
    this.search('search');
  },

  methods: {
    debounce,
    onInputChange() {
      this.$nextTick(() => {
        this.search('search');
      });
    },
    onPageChange(page, limit) {
      this.queryInfo.page = page;
      this.queryInfo.limit = limit;
      this.search('changePage');
    },
    search(isChangePage) {
      if (isChangePage === 'search') {
        this.queryInfo.page = 1;
      }
      const { form } = this;
      form.validateFields((err) => {
        if (!err) {
          const values = form.getFieldsValue();
          this.$store.dispatch('specialDay/fetchSpecialDayList', {
            ...values,
            ...this.queryInfo,
          });
        }
      });
    },
    // 新增事务
    handleAddSpecialDay() {
      this.type = 'add';
      this.visibles = true;
    },
    // 编辑事务
    handleEditSpecialDay(record) {
      this.record = record;
      this.type = 'edit';
      this.visibles = true;
    },
    handleCloseModal() {
      this.visibles = false;
    },
    handleTableUpdated(formType) {
      if (formType === 'delete') {
        /* eslint-disable-next-line */
        this.queryInfo.page = this.queryInfo.page > 1
          ? ((this.queryInfo.page - 1) * this.queryInfo.limit < this.totalSize - 1
            ? this.queryInfo.page
            : this.queryInfo.page - 1)
          : 1;
      }
      this.search();
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-content {
  position: absolute;
  top: 7px;
  padding-top: 0;
  overflow: visible;
}
</style>
