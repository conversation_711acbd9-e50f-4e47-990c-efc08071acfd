<template>
  <div class="status-container">
    <a-popover
      v-if="value.isFixedVenue"
      placement="bottom"
    >
      <template slot="content">
        <div class="fixed-venue-name">
          {{ `${t('cell.fixedVenueName')}${value.fixedVenueName}` }}
        </div>
      </template>
      <div
        class="cell-period"
        :class="className"
      >
        <PeriodTypeIcon
          class="period-type-icon"
          :value="type"
        />
        <div class="selected-icon" />
      </div>
    </a-popover>
    <div
      v-else
      class="cell-period"
      :class="className"
    >
      <PeriodTypeIcon
        class="period-type-icon"
        :value="type"
      />
      <div class="selected-icon" />
    </div>
  </div>
</template>


<script>
import { PeriodType } from '@/constants/venue';
import { nsI18n } from '@/mixins/ns-i18n';
import PeriodTypeIcon from './period-type-icon.vue';
import { convertRecordToType } from './utils';


export default {
  name: 'CellPeriod',

  components: {
    PeriodTypeIcon,
  },

  mixins: [
    nsI18n('t', 'venueTimeSetting.timeSetting.tableList'),
  ],

  props: {
    value: {
      type: Object,
      default() {
        return null;
      },
    },
  },

  computed: {
    type() {
      return convertRecordToType(this.value);
    },

    className() {
      const map = new Map([
        [PeriodType.CLOSED, 'cell-period-closed'],
        [PeriodType.OPENED, 'cell-period-opened'],
        [PeriodType.LOCKED, 'cell-period-locked'],
      ]);
      if (map.get(this.type)) {
        return map.get(this.type);
      }

      return false;
    },
  },
};
</script>


<style lang="less" scoped>
.status-container {
  width: 100%;
  height: 100%;
  border: 2px solid #fff;
}
.cell-period {
  position: relative;
  width: 100%;
  height: 100%;

  .period-type-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }

  &.cell-period-locked {
    background-color: #F4E6E7;
  }
  &.cell-period-closed {
    background-color: #F2F2F2;
  }
}

.fixed-venue-name {
  padding: 8px 16px;
  color: fadeout(#000, 15%);
  font-size: 14px;
  font-weight: 300;
}
</style>
