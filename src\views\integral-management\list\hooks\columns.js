
import { namespaceT } from '@/helps/namespace-t';
// 配置table columns
export function getColumns() {
  const t = namespaceT('integralManagement.columns');

  return [
    {
      title: t('name'),
      dataIndex: 'userName',
    },
    {
      title: t('sn'),
      dataIndex: 'userNo',
    },
    {
      title: t('currentIntegral'),
      dataIndex: 'balancePoints',
    },
    {
      title: t('lastOperatorAndTime'),
      scopedSlots: { customRender: 'lastOperatorAndTime' },
    },
    {
      title: t('operation'),
      scopedSlots: { customRender: 'operation' },
    },
  ];
}
