<template>
  <a-modal
    :title="title"
    :visible="visible"
    :footer="null"
    :mask-closable="false"
    :destroy-on-close="true"
    :width="width"
    :dialog-style="dialogStyle"
    :wrap-class-name="`modal-wrap ${wrapClassName}`"
    @cancel="() => $emit('update:visible', false)"
  >
    <slot />
  </a-modal>
</template>

<script>
export default {
  name: 'PopModal',

  props: {
    title: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 830,
    },
    dialogStyle: {
      type: Object,
      default: () => {},
    },
    wrapClassName: {
      type: String,
      default: '',
    },
  },
};
</script>

<style scoped>

</style>
