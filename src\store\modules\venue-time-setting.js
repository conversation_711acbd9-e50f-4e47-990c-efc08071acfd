/* eslint-disable arrow-body-style */
import {
  getVenueList,
  getVenueDetail,
  updateVenueStatus,
  getVenueTimePeriodList,
  updateVenueTimePeriodStatus,
  openVenueAppointmentPeriod,
  closeVenueAppointmentPeriod,
  lockVenueAppointmentPeriod,
} from '@/api/venue-time-setting';

const initialState = {
  venueList: [],
  isLoadingVenueList: false,
  venueListTotal: 0,
  venueCategoryList: [],
  appointmentRuleList: [],
  isLoadingVenueDetail: false,
  venueDetail: null,
  isSavingVenueStatus: false,
  venueChnName: null,
  venueEngName: null,
  timePeriods: [],
  isLoadingTimePeriods: false,
  savingTimePeriodIds: [], // 正在更新状态的项
  isOpeningVenueAppointmentPeriod: false,
  isClosingVenueAppointmentPeriod: false,
  isLockingVenueAppointmentPeriod: false,
};

const getters = {
  timePeriodsSelected: (state) => {
    const data = [];
    state.timePeriods.forEach((timePeriod) => {
      timePeriod.checkedDays.forEach((dayName) => {
        data.push({
          reservationRuleTimeId: timePeriod.reservationRuleTimeId,
          day: dayName.toLowerCase(),
        });
      });
    });

    return data.length > 0;
  },
};

const mutations = {
  setIsLoadingVenueList(state, payload) {
    state.isLoadingVenueList = payload;
  },

  setVenueList(state, payload) {
    state.venueList = payload.data;
    state.venueListTotal = payload.total;
  },

  setVenueCategoryList(state, payload) {
    state.venueCategoryList = payload;
  },

  setAppointmentRuleList(state, payload) {
    state.appointmentRuleList = payload;
  },

  setIsLoadingVenueDetail(state, payload) {
    state.isLoadingVenueDetail = payload;
  },

  setVenueDetail(state, payload) {
    state.venueDetail = payload;
  },

  setIsSavingVenueStatus(state, payload) {
    state.isSavingVenueStatus = payload;
  },

  setTimePeriods(state, payload) {
    state.timePeriods = payload;
  },

  setTimePeriodStatus(state, payload) {
    const { id, status } = payload;
    state.timePeriods = state.timePeriods.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          status,
        };
      }

      return item;
    });
  },

  setVenueName(state, payload) {
    const { venueChnName, venueEngName } = payload;
    state.venueChnName = venueChnName;
    state.venueEngName = venueEngName;
  },

  setIsLoadingTimePeriods(state, payload) {
    state.isLoadingTimePeriods = payload;
  },

  appendSavingTimePeriodId(state, payload) {
    state.savingTimePeriodIds.push(payload);
  },

  removeSavingTimePeriodId(state, payload) {
    state.savingTimePeriodIds = state.savingTimePeriodIds.filter((id) => id !== payload);
  },

  toggleVenueTimePeriodDay(state, payload) {
    const { reservationRuleTimeId, checkDay } = payload;
    state.timePeriods = state.timePeriods.map((item) => {
      if (item.reservationRuleTimeId === reservationRuleTimeId) {
        let checkedDays;
        if (item.checkedDays.includes(checkDay)) {
          checkedDays = item.checkedDays.filter((d) => d !== checkDay);
        } else {
          checkedDays = [
            ...item.checkedDays,
            checkDay,
          ];
        }

        return {
          ...item,
          checkedDays,
        };
      }

      return item;
    });
  },

  setIsOpeningVenueAppointmentPeriod(state, payload) {
    state.isOpeningVenueAppointmentPeriod = payload;
  },

  setIsClosingVenueAppointmentPeriod(state, payload) {
    state.isClosingVenueAppointmentPeriod = payload;
  },

  setIsLockingVenueAppointmentPeriod(state, payload) {
    state.isLockingVenueAppointmentPeriod = payload;
  },
};

const actions = {
  // 获取场馆列表
  async fetchVenueList({ commit }, payload) {
    commit('setIsLoadingVenueList', true);
    try {
      const params = {
        isAll: 1,
        ...payload,
      };
      const res = await getVenueList(params);
      commit('setVenueList', res);
    } finally {
      commit('setIsLoadingVenueList', false);
    }
  },

  // 获取审批内容详情
  async fetchVenueDetail({ commit }, payload) {
    commit('setVenueDetail', null);
    commit('setIsLoadingVenueDetail', true);
    try {
      const { id } = payload;
      const res = await getVenueDetail(id);
      commit('setVenueDetail', res);
    } finally {
      commit('setIsLoadingVenueDetail', false);
    }
  },

  // 更新场馆状态
  async updateVenueStatus({ commit }, payload) {
    commit('setIsSavingVenueStatus', true);
    try {
      const { id, data: params } = payload;
      await updateVenueStatus(id, params);
    } finally {
      commit('setIsSavingVenueStatus', false);
    }
  },

  // 获取场馆时间设置详情
  async fetchVenueTimePeriodList({ commit }, payload) {
    commit('setIsLoadingTimePeriods', true);
    try {
      const { id } = payload;
      const res = await getVenueTimePeriodList(id);
      const { venueName, venueEnName } = res;
      commit('setVenueName', {
        venueChnName: venueName,
        venueEngName: venueEnName,
      });

      const { venueReservationTimeListVO } = res;
      let timePeriods = [];
      if (Array.isArray(venueReservationTimeListVO)) {
        timePeriods = venueReservationTimeListVO.map((item) => {
          return {
            ...item,
            checkedDays: [],
          };
        });
      }
      commit('setTimePeriods', timePeriods);
    } finally {
      commit('setIsLoadingTimePeriods', false);
    }
  },

  // 更新场馆时间设置时段状态
  async updateVenueTimePeriodStatus({ commit }, payload) {
    const { id, reservationRuleTimeId, isEnable } = payload;
    commit('appendSavingTimePeriodId', id);
    try {
      const params = {
        reservationRuleTimeId,
        isEnable,
      };
      await updateVenueTimePeriodStatus(id, params);
      commit('setTimePeriodStatus', payload);
    } catch (error) {
      commit('setTimePeriodStatus', {
        id,
        status: !isEnable,
      });

      throw error;
    } finally {
      commit('removeSavingTimePeriodId', id);
    }
  },

  // 选中状态（不为接口提交）
  toggleVenueTimePeriodDay({ commit }, payload) {
    commit('toggleVenueTimePeriodDay', payload);
  },

  // 开放预约
  async openVenueAppointmentPeriod({ commit, state }, payload) {
    commit('setIsOpeningVenueAppointmentPeriod', true);
    try {
      const { id } = payload;
      const data = [];
      state.timePeriods.forEach((timePeriod) => {
        timePeriod.checkedDays.forEach((dayName) => {
          data.push({
            reservationRuleTimeId: timePeriod.reservationRuleTimeId,
            day: dayName.toLowerCase(),
          });
        });
      });
      await openVenueAppointmentPeriod(id, data);
    } finally {
      commit('setIsOpeningVenueAppointmentPeriod', false);
    }
  },

  // 不开放预约
  async closeVenueAppointmentPeriod({ commit, state }, payload) {
    commit('setIsClosingVenueAppointmentPeriod', true);
    try {
      const { id } = payload;
      const data = [];
      state.timePeriods.forEach((timePeriod) => {
        timePeriod.checkedDays.forEach((dayName) => {
          data.push({
            reservationRuleTimeId: timePeriod.reservationRuleTimeId,
            day: dayName.toLowerCase(),
          });
        });
      });
      await closeVenueAppointmentPeriod(id, data);
    } finally {
      commit('setIsClosingVenueAppointmentPeriod', false);
    }
  },

  // 固定场馆
  async lockVenueAppointmentPeriod({ commit, state }, payload) {
    commit('setIsLockingVenueAppointmentPeriod', true);
    try {
      const { id, fixedVenueName } = payload;
      const params = {
        fixedVenueName,
      };
      const data = [];
      state.timePeriods.forEach((timePeriod) => {
        timePeriod.checkedDays.forEach((dayName) => {
          data.push({
            reservationRuleTimeId: timePeriod.reservationRuleTimeId,
            day: dayName.toLowerCase(),
          });
        });
      });
      await lockVenueAppointmentPeriod(id, params, data);
    } finally {
      commit('setIsLockingVenueAppointmentPeriod', false);
    }
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  mutations,
  actions,
};
