<template>
  <PopModal
    :title="t('title')"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="drawer-bd">
      <p class="tip">
        {{ t('content') }}
      </p>

      <div class="clearfix drawer-ft">
        <a-button @click="onClose">
          {{ $t('action.close') }}
        </a-button>
        <a-button
          type="primary"
          :loading="isOpeningVenueAppointmentPeriod"
          @click="onSubmit"
        >
          {{ $t('action.ok') }}
        </a-button>
      </div>
    </div>
  </PopModal>
</template>


<script>
import { mapState } from 'vuex';
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';


export default {
  components: {
    PopModal,
  },

  mixins: [
    nsI18n('t', 'venueTimeSetting.timeSetting.modalOpen'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    venueId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      realValue: this.value,
    };
  },

  computed: {
    ...mapState({
      isOpeningVenueAppointmentPeriod: (state) => state.venueTimeSetting.isOpeningVenueAppointmentPeriod,
    }),
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    async onSubmit() {
      try {
        const payload = { id: this.venueId };
        await this.$store.dispatch('venueTimeSetting/openVenueAppointmentPeriod', payload);
        this.realValue = false;
        this.$message.success(this.$t('hint.dataSaved'));
        this.$emit('reload');
      } catch (err) {
        this.$message.error(err.response.data.errorMsg);
      }
    },

    onClose() {
      this.realValue = false;
    },
  },
};
</script>


<style lang="less" scoped>
.tip {
  color: fadeout(#000, 15%);
  font-size: 18px;
  font-weight: 500;
}
</style>
