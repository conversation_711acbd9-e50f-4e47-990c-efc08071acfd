<template>
  <div class="app-panel-header">
    <app-header
      :logo-url="logoUrl"
      :title="serviceName"
      :click-title="handleClickTitle"
      :has-menu="hasHeaderMenu"
      :menu="headerMenu"
      :menu-selected-keys="menuSelectedKeys"
      menu-application-key="Application"
      :click-menu-application="handleClickMenuApplication"
      :support-locales="supportLocales"
      :has-message-link="hasMessageLink"
      :message-badge="displayMessageBadge"
      :user-name="userName"
      :user-account="userAccount"
      :user-avatar-url="userAvatarUrl"
      :loading="loading"
      :click-locale="handelClickLocale"
      :click-message="handelClickMessage"
      :click-change-password="handleClickChangePassword"
      :click-logout="handleClickLogout"
      :user-type="userType"
      :is-pkusz-menu="isPkuszMenu"
      :active-menu="activeMenu"
      :has-msg-dot="hasMsgDot"
      :locale="locale"
    />
    <application-list
      v-if="applicationListShowed"
      :loading="loadApplications"
      :applications="applications"
      @before-mount="handleApplicationListBeforeMount"
      @select="handleSelectApplication"
      @close="handleApplicationListClose"
    />
  </div>
</template>

<script>
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import { Locale } from '@/constants/locale';
import zhCN from '@/locales/lang/zh-CN';
import enUS from '@/locales/lang/en-US';
import zhHK from '@/locales/lang/zh-HK';

import config from '@/config';
import URI from 'urijs';
import {
  getUserData, getApplicationData, getPagesApi, getUnreadMsgApi,
} from '@api';
import { getApiLocale } from '@utils/api-locale';
import AppHeader from './app-header.vue';
import ApplicationList from './application-list';

Vue.use(VueI18n);

export default {
  name: 'PimaAppPanel',
  components: {
    AppHeader,
    ApplicationList,
  },
  provide() {
    return {
      i18n: this.i18n,
    };
  },
  props: {
    // 语言
    locale: {
      type: String,
      default: Locale.zhCN,
    },

    activeMenu: {
      type: String,
      default: '',
    },
    isPkuszMenu: {
      type: Boolean,
      default: false,
    },
    pkuszBasePath: {
      type: String,
      default: '',
    },
    // 消息中心接口
    apiMsgUrl: {
      type: String,
      default: config.bdcMsgApiBaseUrl || '',
    },
    // 信息门户接口地址
    pkuszBaseUrl: {
      type: String,
      default: config.pkuszBaseUrl || '',
    },
    // 信息门户英文接口地址
    pkuszBaseUrlEn: {
      type: String,
      default: config.pkuszBaseUrlEn || '',
    },
    // 服务编码
    serviceCode: {
      type: String,
      default: null,
    },
    // 服务类型
    serviceType: {
      type: String,
      default: null,
    },
    // Logo地址
    logoUrl: {
      type: String,
      default: null,
    },
    // 服务名称
    serviceName: {
      type: String,
      default: null,
    },
    // 获取Token方法
    getAuthorization: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 接口根目录
    apiBaseUrl: {
      type: String,
      default: null,
    },
    // 是否有头部主菜单
    hasHeaderMenu: {
      type: Boolean,
      default: true,
    },
    // 菜单默认选中项键值
    menuSelectedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 是否有侧栏
    hasSidebar: {
      type: Boolean,
      default: true,
    },
    // 侧栏默认展开项键值
    sidebarOpenKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 侧栏默认选中项键值
    sidebarSelectedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 消息列表页地址
    messageUrl: {
      type: String,
      default: null,
    },
    // 消息badge数量，不传默认读取接口数据
    messageBadge: {
      type: Number,
      default: -1,
    },
    // 修改密码页地址
    changePasswordUrl: {
      type: String,
      default: null,
    },
    // 可切换语言
    supportLocales: {
      type: Array,
      default() {
        return ['zh-CN', 'zh-HK', 'en-US'];
      },
    },
  },
  data() {
    return {
      userName: null,
      userAccount: null,
      userAvatarUrl: null,
      headerMenu: [],
      sidebarMenu: [],
      sidebarMenuUrlMapper: {},
      applicationListShowed: false,
      loadApplications: false,
      applications: [],
      messageCount: 0,
      loading: true,
      curService: {},
      userType: '',
      hasMsgDot: false,
      i18n: new VueI18n({
        locale: this.locale,
        messages: {
          [Locale.zhCN]: zhCN,
          [Locale.enUS]: enUS,
          [Locale.zhHK]: zhHK,
        },
      }),
    };
  },

  computed: {
    // 当不配置messageUrl时，不出现链接
    hasMessageLink() {
      return !!this.messageUrl;
    },

    displayMessageBadge() {
      return this.messageBadge === -1 ? this.messageCount : this.messageBadge;
    },
  },

  beforeMount() {
    this.refreshUserData();
    this.refreshUnreadMsgCount();
  },
  methods: {
    getPagesApi,
    fetchUserData() {
      return new Promise((resolve, reject) => {
        this.loading = true;
        const lang = getApiLocale(this.$i18n.locale);
        const params = { lang, code: this.serviceCode, serviceType: this.serviceType };
        getUserData(this.apiBaseUrl, this.getAuthorization(), params).then((resp) => {
          this.loading = false;
          if (resp.status === 200) {
            if (resp.data.success) {
              const {
                userId,
                name,
                username,
                userType,
                thumb,
                fmxServiceMenuDashboardList,
                fmxServiceMenuList,
                webMsgUnReadCount,
                curService,
              } = resp.data.model;
              resolve({
                resModel: resp.data.model || {},
                userInfo: {
                  id: userId,
                  account: username,
                  name,
                  avatarUrl: thumb,
                  userType,
                },
                curService,
                menu: this.makeHeaderMenu(fmxServiceMenuDashboardList),
                sidebar: this.makeSidebarMenu(fmxServiceMenuList),
                sidebarUrlMapper: this.makeSidebarMenuUrlMapper(fmxServiceMenuList),
                operations: this.makeOperations(fmxServiceMenuList),
                messageCount: webMsgUnReadCount || 0,
              });
            } else {
              if (resp.data.model?.userId) {
                const {
                  // userId,
                  name,
                  username,
                  thumb,
                } = resp.data.model;
                this.userName = name;
                this.userAccount = username;
                this.userAvatarUrl = thumb;
                // resolve({
                //  userInfo: {
                //    id: userId,
                //    account: username,
                //    name,
                //    avatarUrl: thumb,
                //  },
                // });
              }
              const error = new Error(resp.data.errorMsg);
              error.errorCode = resp.data.errorCode || 'UNKNOW_ERROR';
              throw error;
            }
          }
        }).catch((err) => {
          this.loading = false;
          reject(err);
        });
      });
    },
    fetchApplicationData() {
      this.loadApplications = true;
      const lang = getApiLocale(this.$i18n.locale);
      const params = { lang };
      getApplicationData(this.apiBaseUrl, this.getAuthorization(), params).then((resp) => {
        this.loadApplications = false;
        if (resp.status === 200) {
          if (resp.data.success) {
            let applications = [];
            if (resp.data && resp.data.model && Array.isArray(resp.data.model)) {
              applications = resp.data.model.map((item) => {
                let children = [];
                if (item.serviceList && Array.isArray(item.serviceList)) {
                  children = item.serviceList.map((subitem) => ({
                    name: subitem.name,
                    enName: subitem.enName,
                    description: subitem.description,
                    icon: subitem.icon,
                    icon1: subitem.icon1,
                    url: subitem.url,
                    target: subitem.target,
                    tags: subitem.tags,
                  }));
                }
                return {
                  key: `category-${item.id}`,
                  name: item.name,
                  enName: item.enName,
                  children,
                };
              });
            }
            this.applications = applications;
          }
        }
      }).catch((err) => {
        this.$message.error(err?.response?.data?.errorList?.message || '网络异常，请检查网络连接后重试');
        this.loadApplications = false;
      });
    },
    makeHeaderMenu(menuItemList) {
      if (menuItemList && Array.isArray(menuItemList)) {
        return menuItemList.map((item) => ({
          key: item.code,
          title: item.name,
          url: item.url,
          target: item.urlTarget,
        }));
      }
      return [];
    },
    makeSidebarMenu(menuItemList) {
      const menuItems = [];
      if (Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          let children;
          if (Array.isArray(item.nextMenuList)) {
            children = this.makeSidebarMenu(item.nextMenuList);
          } else {
            children = [];
          }

          const base = {
            key: item.code,
            icon: item.thumb || null,
            title: item.name,
            link: item.url,
            badge: item.pendingNum || 0,
            target: item.target,
          };
          if (children.length > 0) {
            menuItems.push({
              ...base,
              children,
            });
          } else {
            menuItems.push(base);
          }
        });
      }
      return menuItems;
    },
    makeSidebarMenuUrlMapper(menuItemList) {
      const mapper = {};
      if (menuItemList && Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          mapper[item.code] = item.url;
          let childrenMapper = false;
          if (item.nextMenuList && Array.isArray(item.nextMenuList)) {
            childrenMapper = this.makeSidebarMenuUrlMapper(item.nextMenuList);
          }
          if (childrenMapper !== false) {
            Object.assign(mapper, childrenMapper);
          }
        });
      }
      return mapper;
    },
    makeOperations(menuItemList) {
      let operations = [];
      if (Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          if (Array.isArray(item.menuOperationList)) {
            const currOperations = item.menuOperationList.map((menuOperationItem) => menuOperationItem.code);
            operations = operations.concat(currOperations);
          }
          if (Array.isArray(item.nextMenuList)) {
            const currOperations = this.makeOperations(item.nextMenuList);
            operations = operations.concat(currOperations);
          }
        });
      }
      return operations;
    },
    handleClickTitle() {
      this.$emit('click-service-name');
    },
    handleClickMenuApplication() {
      this.applicationListShowed = !this.applicationListShowed;
      if (this.applicationListShowed) {
        document.querySelector('body > div').style.overflow = 'hidden';
        document.querySelector('body > div').style.height = '100%';
      } else {
        document.querySelector('body > div').style.overflow = 'auto';
        document.querySelector('body > div').style.height = 'auto';
      }
    },
    handleApplicationListBeforeMount() {
      if (this.applications.length === 0) {
        this.fetchApplicationData();
      }
    },
    handleSelectApplication({ url }) {
      if (url) {
        window.location.href = url;
      }
    },
    handleApplicationListClose() {
      this.applicationListShowed = false;
    },

    handelClickLocale(locale) {
      const uri = new URI(window.location.href);
      if (uri.hasQuery('locale')) {
        uri.removeQuery('locale');
      }
      uri.addQuery('locale', locale);
      window.location.href = uri.toString();
    },

    handelClickMessage() {
      if (this.messageUrl) {
        // window.location.href = this.messageUrl;
        window.open(this.messageUrl, '_blank');
      }
    },
    handleClickChangePassword() {
      if (this.changePasswordUrl) {
        window.location.href = this.changePasswordUrl;
      }
    },
    handleClickLogout() {
      this.$emit('logout');
    },
    handleSidebarOpenChange() {
    },
    handleSidebarSelect({ key }) {
      if (this.sidebarMenuUrlMapper[key]) {
        this.$emit('select-sidebar-item', { key, url: this.sidebarMenuUrlMapper[key] });
      }
    },
    refreshUserData() {
      this.fetchUserData().then((data) => {
        const {
          resModel, userInfo, menu, sidebar, sidebarUrlMapper, operations, messageCount, curService,
        } = data;
        this.userName = userInfo.name;
        this.userAccount = userInfo.account;
        this.userAvatarUrl = userInfo.avatarUrl;
        this.userType = userInfo.userType;
        if (!this.isPkuszMenu) {
          this.headerMenu = menu;
        }
        this.sidebarMenu = sidebar;
        this.sidebarMenuUrlMapper = sidebarUrlMapper;
        this.messageCount = messageCount;
        this.curService = curService;

        if (this.isPkuszMenu) {
          const lang = getApiLocale(this.$i18n.locale);
          const baseUrl = lang === 'zh_CN' ? this.pkuszBaseUrl : this.pkuszBaseUrlEn;
          this.getPagesApi(baseUrl, { lang }).then(({ menus }) => {
            const menuList = this.formatMenuList(menus);
            this.headerMenu = menuList;
            this.$emit('user-data-fetched', {
              menu: menuList,
              userInfo,
              sidebar,
              operations,
              messageCount,
              curService,
              resModel,
              refreshUnreadMsgCount: this.refreshUnreadMsgCount,
            });
          });
        }

        this.$emit('user-data-fetched', {
          userInfo,
          menu: this.headerMenu,
          sidebar,
          operations,
          messageCount,
          curService,
          resModel,
          refreshUnreadMsgCount: this.refreshUnreadMsgCount,
        });
      }).catch((error) => {
        this.$emit('user-data-fetched', {
          error,
        });
      });
    },
    formatMenuList(menus) {
      const self = this;
      return menus.map((item) => ({
        ...item,
        title: item.name,
        key: item.id,
        children: item.children && item.children.length ? self.formatMenuList(item.children) : [],
        url: item.url.indexOf('http') === -1 ? this.pkuszBasePath + item.url : item.url,
      }));
    },
    refreshUnreadMsgCount() {
      if (this.isPkuszMenu) {
        const lang = getApiLocale(this.$i18n.locale);
        const params = { lang };
        getUnreadMsgApi(this.apiMsgUrl, params)
          .then((res) => {
            if (res.unreadCount) {
              this.hasMsgDot = true;
            } else {
              this.hasMsgDot = false;
            }
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.app-panel {
  min-height: 100vh;
  background-color: #fff;
}
</style>
