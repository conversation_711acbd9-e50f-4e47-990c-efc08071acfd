import Vue from 'vue';
import Vuex from 'vuex';
import user from './modules/user';
import arch from './modules/arch';
import userManage from './modules/userManage';
import reserveRule from './modules/reserve-rule';
import reserveQuery from './modules/reserve-query';
import basicData from './modules/basic-data';
import basicDataCategory from './modules/basic-data-category';
import uniData from './modules/uniData';
import venueCalendar from './modules/venue-calendar';
import venueTimeSetting from './modules/venue-time-setting';
import specialDay from './modules/specialDay';
import blacklist from './modules/blacklist';
import verify from './modules/verify';
import service from './modules/service';
import userNotes from './modules/user-notes';
import reserveApproval from './modules/reserve-approval';
import integralMgmt from './modules/integral-management';

Vue.use(Vuex);
export default function createStore() {
  const store = new Vuex.Store({
    modules: {
      user,
      arch,
      userManage,
      uniData,
      reserveRule,
      basicData,
      basicDataCategory,
      reserveQuery,
      venueCalendar,
      venueTimeSetting,
      specialDay,
      blacklist,
      verify,
      service,
      userNotes,
      reserveApproval,
      integralMgmt,
    },
  });
  return store;
}
