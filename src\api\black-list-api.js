import request from './request';

// 黑名单列表
export function blackListApi(params) {
  return new Promise((resolve, reject) => {
    request({
      url: 'blacklists',
      method: 'get',
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 新增
export function addBlackListApi(data) {
  return new Promise((resolve, reject) => {
    request({
      url: 'blacklists',
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 修改
export function editBlackListApi({ id, data }) {
  return new Promise((resolve, reject) => {
    request({
      url: `blacklists/${id}`,
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 删除
export function delBlackListApi({ id, data }) {
  return new Promise((resolve, reject) => {
    request({
      url: `blacklists/${id}/remove`,
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 详情
export function blackListsDetailApi({ id }) {
  return new Promise((resolve, reject) => {
    request({
      url: `blacklists/${id}`,
      method: 'get',
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 黑名单规则列表
export function blackRuleListApi(params) {
  return new Promise((resolve, reject) => {
    request({
      url: 'blacklist-rules',
      method: 'get',
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 黑名单规则详情
export function blackRuleDetailApi({ id }) {
  return new Promise((resolve, reject) => {
    request({
      url: `blacklist-rules/${id}`,
      method: 'get',
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 黑名单规则修改
export function editBlackRuleApi({ id, data }) {
  return new Promise((resolve, reject) => {
    request({
      url: `blacklist-rules/${id}`,
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}


// 移除黑名单列表
export function removeBlackListApi(params) {
  return new Promise((resolve, reject) => {
    request({
      url: 'blacklists/removes',
      method: 'get',
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}
