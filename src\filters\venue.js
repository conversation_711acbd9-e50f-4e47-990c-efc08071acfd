import { namespaceT } from '@/helps/namespace-t';
import { VenueStatus, VenueProperty, PeriodProcessStatus } from '@/constants/venue';


export const venueStatusI18n = (value) => {
  const t = namespaceT('constants.venueStatus');
  const mapper = new Map([
    [VenueStatus.ENABLED, t('enabled')],
    [VenueStatus.DISABLED, t('disabled')],
  ]);
  return mapper.get(value);
};

export const venuePropertyI18n = (value) => {
  const t = namespaceT('constants.venueProperty');
  const mapper = new Map([
    [VenueProperty.INDOOR, t('indoor')],
    [VenueProperty.OUTDOOR, t('outdoor')],
  ]);
  return mapper.get(value);
};

export const periodProcessStatusI18n = (value) => {
  const t = namespaceT('constants.periodProcessStatus');
  const mapper = new Map([
    [PeriodProcessStatus.FIXED_VENUE, t('fixedVenue')],
    [PeriodProcessStatus.AVAILABLE, t('available')],
    [PeriodProcessStatus.EXPIRED, t('expired')],
    [PeriodProcessStatus.CLOSED, t('closed')],
    [PeriodProcessStatus.UNAVAILABLE, t('unavailable')],
    [PeriodProcessStatus.RESERVED, t('reserved')],
    [PeriodProcessStatus.UNAVAILABLE, t('unavailable')],
  ]);
  return mapper.get(value);
};
