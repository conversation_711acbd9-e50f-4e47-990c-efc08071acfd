import httpRequest from './request';

export const upload = (formData) => httpRequest({
  url: '/users/import',
  method: 'post',
  data: formData,
});

export function getAuthCheckList(params) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`/roles/${params.id}/permissions`, {
        params,
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postAuthCheckList(params) {
  return new Promise((resolve, reject) => {
    httpRequest
      // eslint-disable-next-line max-len
      .post(`/roles/${params.id}/permissions`, params.data)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getAuthGroup({ params }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get('/roles', {
        params,
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getAuthGroupInfo({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`/roles/${id}`)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postAuthGroup({ form, id }) {
  return new Promise((resolve, reject) => {
    const url = id
      ? `/roles/${id}`
      : '/roles';
    httpRequest
      .post(url, form)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postRemoveAuthGroup({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(
        `/roles/${id}/remove`,
        {},
      )
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function permissionListApp() {
  return new Promise((resolve, reject) => {
    httpRequest
      .get('/services/all')
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function fetchAuthApps({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`/roles/${id}`)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function fetchUserAuth({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`/authority-managements/users-rights/${id}/permissions`)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getAuthGroupOpreats({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`/roles/${id}`)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getUserAuthOpreat({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`/authority-managements/users-rights/${id}/permissions`)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postCustomGrant(payload) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(`/authority-managements/users-rights/${payload.userId}/to-permissions`, {
        roleIds: payload.roleIds,
      }, {
        // paramsSerializer(params) {
        //   return Qs.stringify(params, { indices: false });
        // },
        // params: {
        //   roleIds: payload.roleIds,
        // },
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function setPermissions(payload) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(
        '/authority-managements/users-rights/set-permissions',
        payload,
      )
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

// 设置权限组权限
export function setRightGroupsPermissions(payload) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(
        `/roles/${payload.id}/set-permission`,
        payload.setPermissionServiceDTO,
      )
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getAuthGroupUserList(params) {
  return new Promise((resolve, reject) => {
    const postData = params;
    postData.limit = -1;
    httpRequest
      .get(`/roles/${params.id}/users`, {
        headers: {
          Accept: ' */*',
        },
        params: postData,
      })
      .then((r) => resolve(r.data))
      .catch((e) => reject(e));
  });
}

/**
 * 设置权限组用户
 */
export function setPostAuthGroupUsers({ form, id }) {
  return new Promise((resolve, reject) => {
    const url = `/roles/${id}/users/add`;
    httpRequest
      .post(url, form, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function removeUserByAuthGroup(userIdList, groupId) {
  return new Promise((resolve, reject) => {
    httpRequest
      // .post(`/roles/${groupId}/users/remove?userIds=${userIdList.join(',')}`,
      .post(
        `/roles/${groupId}/users/remove?userIds=${userIdList}`,
        {
          userIds: userIdList,
          id: groupId,
        },
      )
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}
