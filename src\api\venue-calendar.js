import httpRequest from './request';


// 获取场馆日历数据
export async function getVenueCalendar(params) {
  const res = await httpRequest({
    url: 'venues/calendar',
    method: 'get',
    params,
  });
  return res.data.model;
}

// 提交预约
export async function reserveVenue(data) {
  // 新增参数，若是从pc预约，值为true
  const tempData = data.map((item) => ({
    ...item,
    isAdmin: true,
  }));
  await httpRequest({
    url: 'reservations',
    method: 'post',
    data: tempData,
  });
}

// 修改预约
export async function updateReserveInfo({ id, data }) {
  await httpRequest({
    url: `reservations/${id}/update`,
    method: 'post',
    params: data,
  });
}


// 获取用户积分详情
export function getUserIntegralDetail(data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/reservations/points-info',
      method: 'post',
      data,
    }).then((res) => {
      resolve(res.data.model);
    }).catch((err) => {
      reject(err);
    });
  });
}
