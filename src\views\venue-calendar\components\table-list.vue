<template>
  <a-table
    ref="tableRef"
    :data-source="dataSource"
    :loading="isLoading"
    :columns="tableColumns"
    :row-key="record => record.id"
    :scroll="{ x: scrollX, y: scrollY }"
    :pagination="false"
    class="venue-calendar-table"
    bordered
  >
    <template
      slot="period"
      slot-scope="text, record"
    >
      <div class="start-time">
        {{ record.startTime | hourTrimLeft }}
      </div>
      <div class="end-time end-time1">
        ~
      </div>
      <div class="end-time end-time2">
        {{ record.endTime | hourTrimLeft }}
      </div>
    </template>
  </a-table>
</template>


<script>
import OpreationMixin from '@/mixins/operation';
import { WEEK_DAY_LIST } from '@/constants/venue';
import { createTableColumns } from './table-columns';

export default {
  name: 'TimeSettingTableList',

  filters: {
    hourTrimLeft(value) {
      if (typeof value === 'string' && value.indexOf(':') !== -1) {
        const [hour, minute] = value.split(':');
        return `${parseInt(hour, 10)}:${minute}`;
      }

      return value;
    },
  },

  mixins: [
    OpreationMixin,
  ],

  props: {
    selectedTimeList: {
      type: Array,
      default() {
        return [];
      },
    },
    columns: {
      type: Array,
      default() {
        return [];
      },
    },

    dataSource: {
      type: Array,
      default() {
        return [];
      },
    },

    venues: {
      type: Array,
      default() {
        return [];
      },
    },

    selectedDate: {
      type: Date,
      default() {
        return null;
      },
    },

    isLoading: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      scrollX: this.calcScrollX(),
      scrollY: 0,
      tableColumns: createTableColumns(this, this.columns, this.venues, this.selectedDate, this.selectedTimeList),
      detail: {
        show: false,
        id: null,
      },
    };
  },

  watch: {
    columns(value) {
      this.tableColumns = createTableColumns(this, value, this.venues, this.selectedDate, this.selectedTimeList);
      this.scrollX = this.calcScrollX();
    },
  },

  mounted() {
    this.scrollY = this.calcScrollY();
    window.onresize = () => (() => {
      this.scrollY = this.calcScrollY();
    })();
  },

  methods: {
    calcScrollX() {
      // 140为第一列《时段》宽度
      // 170为其他列的宽度
      return 140 + this.columns.length * 170;
    },

    calcScrollY() {
      return Math.floor(this.$refs.tableRef.$el.parentNode.clientHeight - 140);
    },

    getIsSavingCurrentItem(record) {
      const { id } = record;
      return this.savingTimePeriodIds.includes(id);
    },

    onPeriodStatusChange(checked, event, record) {
      const { id } = record;
      const payload = { id, status: checked };
      this.$store.dispatch('venueTimeSetting/updateVenueTimePeriodStatus', payload)
        .then().catch((err) => { this.$message.error(err.response.data.errorMsg); });
    },

    // 在columns里定义
    onClickCell(record, rowIndex, columnIndex) {
      const today = WEEK_DAY_LIST[columnIndex - 2];
      const { id } = record;
      const payload = { id, checkDay: today };
      this.$store.dispatch('venueTimeSetting/toggleVenueTimePeriodDay', payload);
    },
  },
};
</script>


<style lang="less" scoped>
.venue-calendar-table {
  ::v-deep .ant-table-fixed-left {
    .ant-table-row-hover td:first-child {
      border-right: 2px solid #8D0306;
    }
  }

  ::v-deep .ant-table-fixed {
    border: 0;

    .ant-table-thead {
      tr {
        th {
          height: 50px;
          overflow: hidden;
          background: #f8f8f8;
          border-bottom-width: 2px;
          border-bottom-color: #ededed;
          border-right-color: #ededed;

          &.th-period-title-wrap {
            position: relative;

            &.th-column-first {
              border-right-color: #ededed;
            }
          }
          &.bg-hover {
            border-bottom: 2px solid #8D0306 !important;
          }

          .ant-table-column-title {
            display: -webkit-box;
            color: fadeout(#000, 15%);
            font-size: 16px;
            font-weight: 500;
            white-space: normal;
            text-overflow: ellipsis;
            overflow: hidden;
            box-orient: vertical;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
        }
      }
    }
  }

  ::v-deep .ant-table-fixed-left .ant-table-tbody tr td {
    background: #f8f8f8;
  }

  ::v-deep .ant-table-tbody {
    tr {
      td {
        height: 50px;
        padding: 0;
        white-space: normal;
        overflow: hidden;
        background: #fff;

        &.td-period-title-wrap {
          border-right-color: #ededed;
          border-bottom-color: #ededed;
        }

        &.td-period-item-wrap {
          position: relative;
          border-right-color: #ededed;
          border-bottom-color: #ededed;
        }

        .start-time {
          color: fadeout(#000, 35%);
          font-size: 14px;
          font-weight: bold;
        }

        .end-time {
          color: fadeout(#000, 55%);
          font-size: 12px;
          font-weight: bold;
          margin-top: -2px;

          &.end-time1 {
            margin-top: -4px;
          }
        }
      }
      td:first-child {
        border-right-width: 2px;
      }
    }
    .bg-hover {
      background-color: #F8F8F8;
    }
  }

  // ::v-deep .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
  // ::v-deep .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
  // ::v-deep .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
  // ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  //   background: transparent;
  // }
}

</style>
