export default {
  title: {
    title: '预约查询',
    export: '导出',
    reserveDetail: '预约详情',
    fileName: '场馆预约数据',
  },
  columns: {
    number: '申请单号',
    venue: '预约场馆',
    orderTime: '预约时间',
    integralUsed: '使用积分',
    orderName: '预约人姓名',
    no: '学号/工号',
    phone: '联系电话',
    applyTime: '申请时间',
    status: '状态',
    operate: '操作',
  },
  cancelModal: {
    title: '取消预约',
    label: '取消预约会同时清除场馆占用记录',
    reason: '原因',
    remarks: '备注',
  },
  form: {
    advancedSearch: {
      status: '状态',
      number: '申请单号',
      venue: '预约场馆名称',
      orderName: '预约人',
      orderNamePlaceholder: '姓名或学/工号',
      belongTo: '所在组织',
      activityName: '活动名称',
      orderTime: '预约时间范围',
      applyTime: '申请时间范围',
      selectPlaceholder: '请选择',
    },
    simpleSearch: {
      status: '状态',
      keyword: '申请单号或场馆名称',
    },
  },
  reserveDetail: {
    status: '当前状态',
    time: '申请时间',
    sn: '申请单号',
    venue: '预约场馆',
    orderTime: '预约时间',
    integralUsed: '@:reserveQuery.columns.integralUsed',
    orderName: '预约人姓名',
    orderPeople: '预约人',
    orderId: '预约人工号',
    belongTo: '部门/学院',
    organization: '组织名称',
    teacherName: '指导老师',
    activityName: '活动名称',
    activityPeoples: '活动参与人数',
    activityGuests: '外来嘉宾',
    isStudentReservation: '是否学生组织预约',
    phone: '联系电话',
    note: '申请说明',
    remark: '备注:',
    submit: '提交申请',
    cancel: '取消预约',
    pass: '同意',
    reject: '退回',
    signed: '签到确认',
    arrived: '已到场',
    autoPass: '自动通过',
    stepList: '审批记录',
    info: '预约信息',
    offTime: '（此人累计爽约次数{count}次）',
    addToBlackList: '加入黑名单',
    createUserName: '操作人',
    nextApprover: '即将审批',
  },
  blacklist: {
    title: '加入黑名单',
    label: '是否将{name}加入黑名单？加入黑名单后此人无法预约{type}场馆。',
    reason: '原因',
    reasonPlace: '请输入加入黑名单原因！',
  },
  action: {
    advancedSearch: '高级搜索',
    cancel: '取消预约',
    close: '关闭',
    ok: '确定',
    toApproval: '去审批',
  },
  msg: {
    success: '操作成功',
    submitSuccess: '提交成功',
    cancelSuccess: '取消成功',
    confirmCancel: '确定取消报修申请吗？',
    assignSucc: '指派成功',
    handleSucc: '处理成功',
    saveSucc: '保存成功',
    imgType: '只允许上传 {types} 类型图片',
    notHasSearchItems: '请筛选需要导出的数据',
  },
  status: {
    PENDING: '待审批',
    APPROVING: '审批中',
    PASS: '审批通过',
    AUTO_PASS: '自动通过',
    PEDNING_SIGN: '待签到',
    SIGNED: '已签到',
    REJECT: '已退回',
    CANCEL: '已取消',
    OFF: '已爽约',
    RESERVED: '已预定',
  },
  timeRangeType: {
    CREATETIME: '申请时间范围',
    APPROVALTIME: '预约时间范围',
  },
  cancelType: {
    OFF: '爽约',
    OTHER: '其他',
  },
  isStudentReservationStatus: {
    true: '是',
    false: '否',
  },
};
