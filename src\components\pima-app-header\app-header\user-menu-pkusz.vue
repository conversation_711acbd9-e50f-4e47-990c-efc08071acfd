<template>
  <ul
    theme="dark"
    mode="horizontal"
    class="ant-menu ant-menu-horizontal"
    :inline-indent="22"
    :default-selected-keys="defaultSelectedKeys"
    :selected-keys="defaultSelectedKeys"
  >
    <template v-for="(item, index) in menu">
      <li
        :key="item.key"
        :selectable="false"
        :class="{
          'active-menu-item': activeMenu === item.code || (item.children
            ? item.children.map(({ code }) => code).indexOf(activeMenu) > -1
            : false),
          'menu-more-link': Object.values(constantMenuMap.menu).includes(item.key),
        }"
        class="ant-menu-item"
        @click="goPage(item, index)"
      >
        <div
          v-if="item.key === constantMenuMap.menu.depart"
          style="line-height:40px"
          class="title xs-font-14"
        >
          {{ item.title }}
        </div>
        <div
          v-else
          class="title xs-font-14"
        >
          {{ item.title }}
          <span
            v-if="hasMsgDot && item.code==='personalmessage'"
            class="unread-dot"
          />
        </div>

        <div
          v-if="item.key === constantMenuMap.menu.more"
          class="menu-more-wrap"
        >
          <div class="menu-more-content">
            <div
              v-for="(child, ii) in moreMenus"
              :key="ii"
              :title="child.name"
              class="more-item-link"
              @click.stop="goPage(child)"
            >
              <div class="item-logo-wrap">
                <img
                  :src="child.icon"
                  class="icon"
                >
                <img
                  :src="child.icon1"
                  class="icon-hover"
                >
              </div>
              <span
                class="more-item-title"
              >
                {{ child.name }}
              </span>
            </div>
          </div>
        </div>

        <div
          v-if="item.key === constantMenuMap.menu.depart"
          class="menu-more-wrap"
        >
          <div
            class="menu-more-content"
            style="width: auto; height: auto;min-height: 200px"
          >
            <div
              v-for="(dep, ii) in depts"
              :key="ii"
              class="depart-item"
              @click.stop="goDepartmentPage(dep)"
            >
              <a
                style="color: #222"
              >
                {{ dep.title }}
              </a>
            </div>
          </div>
        </div>
      </li>
    </template>
    <!-- <li
      v-for="item in menu"
      :class="item.key === defaultSelectedKeys[0] ? 'ant-menu-item-selected ant-menu-item' : 'ant-menu-item'"
      :key="item.key"
      :selectable="false"
    >
      <a
        :href="item.url"
        :target="item.target || '_self'"
        @click="handleClick($event, item)"
      >
        {{ item.title }}
      </a>
    </li> -->
  </ul>
</template>

<script>
// import Menu from 'ant-design-vue/lib/menu';

const USERTYPE_FOR_SERVICEGUIDE = {
  TEACHER: 'isOrientedStudent',
  STUDENT: 'isOrientedTeacher',
};

const constantMenuMap = {
  menu: {
    depart: 182,
    more: 197,
    business: 191,
  },
  notice: {
    school: { id: 185, titleI18n: 'pkusz.titles.index.schoolNotice' },
    institute: { id: 186, titleI18n: 'pkusz.titles.index.instituteNotice' },
    depart: { id: 187, titleI18n: 'pkusz.titles.index.departNotice' },
  },
};

export default {
  name: 'AppHeaderNavigator',
  components: {
    // [Menu.name]: Menu,
    // [Menu.Item.name]: Menu.Item,
  },
  props: {
    hasMsgDot: {
      type: Boolean,
      default: false,
    },
    activeMenu: {
      type: String,
      default: '',
    },
    userType: {
      type: String,
      default: '',
    },
    // 菜单内容
    menu: {
      type: Array,
      default() {
        return [];
      },
    },
    // 默认选中项
    defaultSelectedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 菜单应用项键值
    applicationKey: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      constantMenuMap,
    };
  },
  computed: {
    depts() {
      const deptList = this.menu.filter((item) => item.code === 'departmentalAgency')[0].children;
      return deptList.map((dept) => ({
        ...dept,
        title: dept.name,
      }));
    },
    moreMenus() {
      const moreMenuItem = this.menu.filter((item) => item.key === this.constantMenuMap.menu.more);
      if (moreMenuItem.length) {
        return moreMenuItem[0].children.filter((child) => child.isIndexable);
      }
      return [];
    },
  },
  methods: {
    handleClick(ev, { key }) {
      if (key === this.applicationKey) {
        ev.preventDefault();
        this.$emit('click-application');
        return false;
      }
      return true;
    },
    goDepartmentPage({ url }) {
      if (url) {
        window.open(url, '_blank');
      }
    },
    goPage({
      url, code, // id, name,
    }, index) {
      if (code === 'more') return;
      if (url) {
        let rawUrl;
        if (code === 'fuwuzhilan') {
          // if (this.hasLogin) {
          rawUrl = `${url}?type=${USERTYPE_FOR_SERVICEGUIDE[this.userType] || ''}`;
          // } else {
          //   rawUrl = url;
          // }
        } else {
          rawUrl = url;
        }
        if (rawUrl.substr(0, 7).toLowerCase() === 'http://' || rawUrl.substr(0, 8).toLowerCase() === 'https://') {
          // window.location.href = rawUrl;
          // this.handleLogStatistic(this.menus, '', {
          //   pageId: id,
          //   url: rawUrl,
          //   pageTitle: name,
          // });
          window.open(rawUrl);
        } else {
          this.$router.push(rawUrl);
        }
      } else {
        // eslint-disable-next-line no-unused-expressions
        index ? null : this.$router.push({ name: 'home' });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.menu {
  line-height: 39px;
  font-size: 16px;
  border-bottom: none;
  overflow: hidden;

  .ant-menu {
    height: 40px;
    line-height: 39px;
    font-size: 14px;
    list-style: none;
    background: none;
    display: flex;
    justify-content: flex-start;
    border-bottom: none;

    ::v-deep li {
      // height: 40px;
      // float: left;
      // padding: 0 25px;
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      color: #fff;
      cursor: pointer;
      // transition: background .2s ease;
      // margin-right: 1px;

      min-width: 80px;
      text-align: center;
      margin: 0;
      padding: 0 20px;
      height: 39px;
      white-space: nowrap;
      // &:hover,
      // &.active-menu-item {
      //   background: linear-gradient(180deg,#fff,#ffe6e6);
      //   color: #8d0405;
      //   border-radius: 3px 3px 0 0;
      // }
      &:hover {
        &.menu-more-link .menu-more-wrap {
          display: block;
        }
      }
      &.menu-more-link {
        position: relative;
        .menu-more-wrap {
          display: none;
          position: absolute;
          top: 100%;
          left: -20px;
          z-index: 99;
          background: transparent;
          padding-top: 15px;
          cursor: default;
          .menu-more-content {
            width: 500px;
            min-height: 230px;
            border: 1px solid #aa0204;
            background-color: #fff;
            background-image: url('../../../assets/img/more-menu-bg.png');
            background-size: 125px 98px;
            background-repeat: no-repeat;
            background-position: 100% 100%;
            box-shadow: 0 0 4px 0 rgba(141,4,5,.36);
            position: relative;
            z-index: 99;
            border-radius: 3px;
            padding: 20px;
            &:after {
              content: "";
              display: block;
              position: absolute;
              z-index: 98;
              width: 10px;
              height: 10px;
              border: 1px solid #aa0204;
              border-right: none;
              border-bottom: none;
              transform: rotate(45deg);
              top: -6px;
              left: 50px;
              background-color: #fff;
            }
            .depart-item {
              text-align: left;
              font-weight: 400;
              color: #222;
              line-height: 22px;
              padding: 2px 10px;
              margin: 4px 0;
              cursor: pointer;
              font-size: 16px;
              transition: all .2s ease;
              border-bottom: 1px solid transparent;
              &:hover {
                color: #8d0405;
                border-bottom-color: #e8e8e8;
                a {
                  color: #8d0405!important;
                }
              }
            }
            .more-item-link {
              display: block;
              float: left;
              text-align: center;
              width: 18%;
              margin: 0 1% 20px;
              cursor: pointer;
              .item-logo-wrap {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                overflow: hidden;
                display: inline-block;
                img {
                  width: 45px;
                  height: 45px;
                }
                .icon-hover {
                  display: none;
                }
              }
              .more-item-title {
                font-weight: 400;
                color: #222;
                line-height: 17px;
                font-size: 16px;
                display: block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              &:hover {
                .item-logo-wrap .icon-hover {
                  display: inline;
                }
                .item-logo-wrap .icon {
                  display: none;
                }
                .more-item-title {
                  color: #8d0405;
                }
              }
            }
          }
        }
      }
      // .title {
      //   font-size: 16px;
      //   font-weight: 600;
      //   line-height: 20px;
      // }
      a {
        color: #fff;
      }
    }
  }
}

.menu .ant-menu ::v-deep li {
  &.ant-menu-item-selected {
    position: relative;
    background-color: #8d0405;

    &::after {
      content: ' ';
      width: 100%;
      height: 2px;
      background-color: #8d0405;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
}
.ant-menu-horizontal > .ant-menu-item:hover {
  border-bottom-color: #C00001;
  background-color: #780506;
}
.active-menu-item {
  background-color: #780506;
}
.unread-dot {
  position: absolute;
  top: 50%;
  right: 7px;
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f5222d;
  border-radius: 100%;
  transform: translateY(-50%);
}
</style>
