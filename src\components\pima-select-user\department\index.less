.pima-select-user {
  max-width: 100%;
  display: block;
  position: relative;
  vertical-align: middle;
  outline: none;

  .hidden{
    display: none;
  }

  //Tag展示区域
  .pima-select-title {
    position: relative;
    padding: 0 10px 0 10px;
    min-height: 68px;
    line-height: 34px;
    min-width: 146px;
    max-height: 100px;
    overflow-y: auto;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 3px;
    display: flex;

    .pima-selected-string{
      flex: 1;
    }

    .select-arrow{
      width: 50px;
      pointer-events: none;
      &.open {
        div{
          transform: rotate(-180deg) scale(0.5);
        }
      }
      div{
        margin: 20px auto 0;
        transform: scale(0.5);
        transition: transform 0.3s;
        text-align: center;
        span {
          font-size: 12px;
          font-style: normal;
          color: #999;
        }
      }
    }
  }

  //下拉框
  .treebox{
    position: absolute;
    width: 100%;
    box-shadow: 0px 5px 10px -3px rgba(0, 0, 0, 0.06);
    .tree-search-wrap{
      padding: 8px 24px;
    }

    .tree-wrap{
      width: 100%;
      height: 100%;
      overflow-y: auto;
      max-height: 370px;
    }
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    transform: scale(0.83333333) rotate(180deg);
  }
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  border-radius: 3px;
  background: rgba(0,0,0,0.06);
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.08);
}
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0,0,0,0.12);
  -webkit-box-shadow: inset 0 0 10px rgba(0,0,0,0.2);
}


.icon-font{
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
