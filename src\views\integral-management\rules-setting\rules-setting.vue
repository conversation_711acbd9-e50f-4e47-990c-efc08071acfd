<template>
  <div
    v-if="hasP((P)=>P.IntegralManagement.Setting)"
    class="page-panel"
  >
    <div class="content-panel">
      <GoBack
        :title="t('title.ruleSetting')"
        @back="handleBack"
      />

      <div class="panel-body pd-80">
        <AddForm
          ref="AddFormRef"
          :submitting="submitting"
          @on-submit="handleSubmit"
        />

        <div class="footer_btns">
          <div class="align-center-btns">
            <a-button
              type="default"
              @click="handleBack"
            >
              {{ ti('action.cancle') }}
            </a-button>
            <a-button
              type="primary"
              @click="$refs.AddFormRef.handleSubmit()"
            >
              {{ ti('action.save') }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { updateServiceSysParams } from '@/api/service-api';
import { namespaceT } from '@/helps/namespace-t';
import operation from '@/mixins/operation';

import GoBack from '@/components/base/go-back-title.vue';
import AddForm from './components/add-form.vue';

export default {
  name: 'IntegralRuleSetting',

  components: {
    GoBack,
    AddForm,
  },

  mixins: [operation],

  data() {
    return {
      submitting: false,
      t: namespaceT('integralManagement'),
      ti: namespaceT(),
    };
  },


  methods: {
    async handleSubmit(payload) {
      try {
        this.submitting = true;
        await updateServiceSysParams(payload);

        this.$message.success(this.t('hint.saveSuccessfully'));
        this.$router.back();
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.submitting = false;
      }
    },

    handleBack() {
      this.$router.back();
    },
  },
};
</script>


<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
}
</style>
