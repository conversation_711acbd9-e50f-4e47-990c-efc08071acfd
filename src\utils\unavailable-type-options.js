import typeStatus from '../constants/unavailable-type';

const typeList = [
  typeStatus.RESERVATION_UNAVAILABLE,
  typeStatus.VENUE_UNAVAILABLE,
];

const typeMapper = {
  [typeStatus.RESERVATION_UNAVAILABLE]: 'special.type.reservation_unavailable',
  [typeStatus.VENUE_UNAVAILABLE]: 'special.type.venue_unavailable',
};

export function i18nSingle(vue, text) {
  return vue.i18n ? vue.i18n.t(text) : vue.$t(text);
}

export function getTypeOptions(vue) {
  return typeList.map((key) => ({
    key: String(key),
    value: String(key),
    title: i18nSingle(vue, typeMapper[key]),
    label: i18nSingle(vue, typeMapper[key]),
  }));
}
