import { mergeOpts } from '../core';

export const TRUE_OR_FALSY_MAP = { YES: 'YES', NO: 'NO' };

export const TRUE_OR_FALSY_KEYS = Object.keys(TRUE_OR_FALSY_MAP);

export const TRUE_OR_FALSY_WITH_BOOLEAN = Object.freeze({
  [TRUE_OR_FALSY_MAP.YES]: true,
  [TRUE_OR_FALSY_MAP.NO]: false,
});

export const TRUE_OR_FALSY_WITH_NUMBER = Object.freeze({
  [TRUE_OR_FALSY_MAP.YES]: 1,
  [TRUE_OR_FALSY_MAP.NO]: 0,
});

const trueOrFalsyLabels = (vm) => {
  const field = {};
  if (!vm) return field;
  // eslint-disable-next-line no-return-assign
  Object.keys(TRUE_OR_FALSY_WITH_BOOLEAN).map((key) => field[key] = vm.$t(`common.${key}`));
  return field;
};

export function getTrueOrFalseOpts(vm, resetLabels = {}) {
  const mergeLabels = mergeOpts(trueOrFalsyLabels(vm), resetLabels);
  return Object.keys(mergeLabels).map((key) => ({
    key,
    label: mergeLabels[key],
    value: null,
  }));
}

export function createTrueFalsyOpts(
  vm,
  opts = {
    resetLabels: {},
    isBoolean: false,
    withAll: true,
  },
) {
  const options = getTrueOrFalseOpts(vm, opts.resetLabels)
    .map((field, index) => ({
      ...field,
      value: opts.isBoolean
        ? `${TRUE_OR_FALSY_WITH_BOOLEAN[TRUE_OR_FALSY_KEYS[index]]}`
        : TRUE_OR_FALSY_WITH_NUMBER[TRUE_OR_FALSY_KEYS[index]],
    }));
  return opts.withAll ? [{
    key: 'all',
    label: vm.$t('common.all'),
    value: '',
  }, ...options] : options;
}
