import {
  fetchServiceSysParams,
  updateServiceSysParams,
} from '../../api/service-api';

const systemRulesCodes = [
  'IS_AUTO_BLACKLIST', // 是否启用自动加入黑名单机制
  'IS_CANCEL_BLACKLIST', // 是否启用解除黑名单机制
  'MONTH', // 多少个自然月加入黑名单
  'COUNT', // 自动加入黑名单的次数
  'DAY', // 多少天后自动移除黑名单
];

const initialState = {
  systemRulesCodes,
  systemRulesMaps: [],
  saving: false,
  loading: false,
};

const getters = {};

const mutations = {
  setSaving(state, payload) {
    state.saving = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setSystemParamsMap(state, model) {
    const fields = {};
    systemRulesCodes.forEach((code) => {
      const codeValue = model.filter((item) => item.code === code);
      fields[code] = codeValue.length ? codeValue[0] : {};
    });
    state.systemRulesMaps = fields;
  },
};

const actions = {
  fetchSystemParams({ commit }) {
    commit('setLoading', true);
    return new Promise((resolve, reject) => {
      fetchServiceSysParams([...systemRulesCodes])
        .then((r) => {
          const { model } = r.data;
          commit('setSystemParamsMap', model);
          resolve(model);
          commit('setLoading', false);
        }).catch((err) => {
          reject(err);
          commit('setLoading', false);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  updateSystemParams({ commit, state }, { paramList }) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      updateServiceSysParams(paramList)
        .then((r) => {
          resolve(r);
          commit('setSaving', false);
        }).catch((err) => {
          reject(err);
          commit('setSaving', false);
        });
    });
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  mutations,
  actions,
};
