<template>
  <div class="application-container">
    <a
      href="#"
      class="link-close"
      @click="handleClose"
    >
      <span
        type="close-circle"
        class="close-icon"
      />
    </a>
    <div class="side">
      &nbsp;
    </div>
    <div class="main">
      <a-form class="search-form">
        <a-form-item class="search-input-wrap">
          <a-input
            class="search-input"
            :placeholder="$t('pimaUI.applicationList.searchInputPlaceholder')"
            allow-clear
            @change="handleChange"
            @pressEnter="handleSearch"
          >
            <span
              slot="prefix"
              class="iconfont icon-all_sousuo"
            />
          </a-input>
        </a-form-item>
      </a-form>
      <a-spin
        class="loading"
        :spinning="loading"
      >
        <div class="application-list">
          <div
            v-for="category in services"
            :key="category.key"
            style="border-bottom: 1px solid #EFEFEF;margin-top: 30px;"
          >
            <div class="category-title">
              <div>
                {{ category.name }}
              </div>
            </div>
            <ul v-if="category.children">
              <li
                v-for="item in category.children"
                :key="item.key"
                class="item"
              >
                <a
                  :href="urlAppendLocale(item.url)"
                  :target="item.target || '_self'"
                >
                  <span class="img">
                    <img
                      class="icon img_1"
                      :src="item.icon"
                      :alt="item.name"
                    >
                  </span>
                  <span class="t">{{ item.name }}</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import URI from 'urijs';

export default {
  name: 'ApplicationList',
  props: {
    // 加载中
    loading: {
      type: Boolean,
      default: false,
    },
    // 应用
    applications: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      timer: undefined,
      keywords: '',
      filterApplications: [],
    };
  },
  computed: {
    services() {
      return this.keywords ? this.filterApplications : this.applications;
    },
  },
  beforeMount() {
    this.$emit('before-mount');
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    filterOut(keywords) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.keywords = keywords;
        const filterApplications = [];
        this.applications.forEach((category) => {
          const children = [];
          if (category.children.length > 0) {
            category.children.forEach((application) => {
              if (application.name.toLowerCase().indexOf(keywords.toLowerCase()) > -1
                  || application.enName?.toLowerCase().indexOf(keywords.toLowerCase()) > -1
                  || application.tags?.toLowerCase().indexOf(keywords.toLowerCase()) > -1
              ) {
                children.push(application);
              }
            });
          }
          if (children.length > 0) {
            filterApplications.push({
              ...category,
              children,
            });
          }
        });
        this.filterApplications = filterApplications;
      }, 500);
    },
    handleChange(ev) {
      this.filterOut(ev.target.value);
    },
    handleSearch(value) {
      this.filterOut(value);
    },
    urlAppendLocale(url) {
      if (url) {
        const uri = URI(url);
        if (uri.hasQuery('locale')) {
          uri.removeQuery('locale');
        }
        uri.addQuery('locale', this.$i18n.locale);
        return uri.toString();
      }
      return '#';
    },
  },
};
</script>

<style lang="less" scoped>
.application-container {
  position: fixed;
  top: 40px;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  background-color: #ffffff;
  //overflow: auto;
  height: calc(100vh - 40px);
  display: flex;
  flex: auto;
}

.side {
  flex: 0 0 200px;
  width: 200px;
  min-height: 100%;
  background-color: #fff;
}

.main{
  width: 100%;
  background-color: #fff;
  padding: 50px 76px;
  height: 100%;
  overflow-y: auto;
}

.link-close {
  display: none;
  position: absolute;
  right: 20px;
  top: 20px;
  padding: 0;
}

.close-icon {
  color: #555;
  font-size: 24px;
}

.search-form {
  zoom: 1;
  padding-bottom: 30px;
  margin: 0 auto 0;

  .search-input-wrap {
    margin: 0;
    text-align: center;
  }
}

.search-input {
  width: 600px;
  height: 54px;
  box-shadow: 0px 8px 15px -7px rgba(220, 229, 238, 0.47);

  ::v-deep .ant-input {
    height: 54px;
    color: #263858;
    border-radius: 2px;
    border: 1px solid #CBCBCB;

    &::placeholder {
      color: #6b778c;
    }
    &::-webkit-input-placeholder {
      color: #6b778c;
    }
    &::-moz-placeholder {
      color: #6b778c;
    }
    &::-ms-input-placeholder {
      color: #6b778c;
    }
  }

  ::v-deep .ant-btn{
    padding: 0 10px;
    height: 40px;
    font-size: 20px;
    border-radius: 0;
  }
  ::v-deep .ant-btn-primary{
    background-color: #0e5fca;
  }
}

.loading {
  ::v-deep .ant-spin-blur {
    opacity: 0;
  }
}

.application-list {
  overflow: hidden;
  zoom: 1;

  .category-title {
    //height: 30px;
    //font-size: 16px;
    //color: #263858;
    font-size: 18px;
    color: #222;
    font-weight: 600;
    margin-bottom: 14px;
    padding: 24px 0;
    line-height: 0;
    // border-bottom: 1px solid #e8e8e8;
    div {
      float: left;
      //height: 30px;
      padding-right: 100px;
    };
  }
  ul {
    margin: 0;
    padding: 0;
  }
  .item {
    display: inline-block;
    position: static;
    vertical-align: top;
    margin: 0 80px 30px 0;
    width: 56px;
    text-align: center;
    // color: #203562;
    font-size: 14px;
    a {
      display: block;
      padding: 0;
      height: auto;
      line-height: 1.5;
      font-size: 12px;
      color: #263858;

      &:hover {
        color: #0d60cb;
      }
    }
    .img{
      display: block;
      margin: 0 auto 10px;
      width: 56px;
      height: 56px;
      // background-color: #fff;
      img {
        width: 56px;
        height: 56px;
      }
    }
    .icon {
      position: absolute;
      display: block;
      width: 50px;
      height: 50px;
      transition:all .5s ease;
    }
    // .img_1{}
    // .img_2{
    //   opacity: 0;
    // }
    // &:hover .img_1{ opacity: 0;}
    // &:hover .img_2{ opacity: 1;}
    &:hover .img_1{
      transform: scale(1.3);
    }
    .t {
      display: block;
      text-align: center;
    }
  }
}

::v-deep .ant-popover .ant-popover-inner{
  padding: 20px 30px;
  border-radius: 0;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
  border: 1px solid #efefef;

  .ant-popover-title,
  .ant-popover-inner-content{
    padding-left: 0;
    padding-right: 0;
  }
}
</style>
