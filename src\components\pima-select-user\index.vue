<template>
  <div
    v-if="visible"
    class="select-user-wrap"
  >
    <div class="select-user-box">
      <!--关闭按钮-->
      <div
        class="close-btn"
        @click="onClose"
      />

      <!--头部-->
      <div class="select-user-header">
        <div class="header-title">
          选择人员
        </div>
        <div class="header-action">
          <!-- <span
            v-if="activeTabKey === SELECT_TYPE.SELECT_PERSONAL"
            @click="visibleImport = true"
          >
            导入名单
          </span> -->
          <span @click="handleSaveUsers">保存</span>
        </div>
      </div>

      <!--身体-->
      <div class="select-user-body">
        <!--左侧菜单-->
        <div class="select-user-sider">
          <ul>
            <li
              class="sider-item open"
              @click="toggleSiderItem"
            >
              <div class="sider-item-title" />
              <ul>
                <li
                  v-for="(key, index) in Object.keys(SELECT_TYPE)"
                  :key="key"
                  class="child-sider-item"
                >
                  <div
                    class="sider-item-title"
                    :class="activeTabKey === key ? 'selected' : ''"
                    @click="changeTabs(key)"
                  >
                    {{ selectLabels[index] }}
                  </div>
                </li>
              </ul>
            </li>
          </ul>
        </div>

        <!--中心内容区-->
        <div class="select-user-content">
          <!--全部-->
          <div v-if="activeTabKey === SELECT_TYPE.SELECT_ALL">
            {{ selectAllNotice }}
          </div>

          <!--部门-->
          <div
            v-if="activeTabKey === SELECT_TYPE.SELECT_DEPARTMENT"
            style="height: 600px;"
          >
            <select-department
              :filter-prop="filterProp"
              :department-data="departmentData"
              :default-department-keys="defaultDepartmentKeys"
              :search-placeholder="searchPlaceholder"
              :search-time-gap="searchTimeGap"
              :loading="loading"
              :checked.sync="SELECT_DEPARTMENT_DATA"
            />
          </div>

          <!--个人-->
          <select-personal
            v-if="activeTabKey === SELECT_TYPE.SELECT_PERSONAL"
            :config="config"
            :import-url="importUrl"
            :template-url="templateUrl"
            :template-file-name="templateFileName"
            :allowed-types="allowedTypes"
            :initial-notices="initialNotices"
            :personal-data="personalData"
            :default-personal-keys="defaultPersonalKeys"
            :loading="loading"
            :visible-import.sync="visibleImport"
            :checked.sync="SELECT_PERSONAL_DATA"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Drawer from 'ant-design-vue/lib/drawer';
import Tabs from 'ant-design-vue/lib/tabs';
import Button from 'ant-design-vue/lib/button';
import Icon from 'ant-design-vue/lib/icon';
import Modal from 'ant-design-vue/lib/modal';
import Tooltip from 'ant-design-vue/lib/tooltip';
import SelectDepartment from './department/index.vue';
import SelectPersonal from './personal/index.vue';
import { SELECT_TYPE } from './utils';

export default {
  name: 'PimaSelectUser',

  components: {
    [Drawer.name]: Drawer,
    [Tabs.name]: Tabs,
    [Tabs.TabPane.name]: Tabs.TabPane,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Modal.name]: Tabs,
    [Tooltip.name]: Tooltip,
    SelectDepartment,
    SelectPersonal,
  },

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    importUrl: {
      type: String,
      default: '',
    },
    templateUrl: {
      type: String,
      default: '',
    },
    templateFileName: {
      type: String,
      default: '',
    },
    allowedTypes: {
      type: Array,
      default: () => ['xls', 'xlsx'],
    },
    initialNotices: {
      type: Array,
      default: () => [
        '下载导入模板，填写用户信息，批量新建用户',
        '上传填好的文件，仅支持xls、xlsx格式文件',
      ],
    },
    config: {
      type: Object,
      default: () => {},
    },
    personalData: {
      type: Array,
      default: () => [],
    },
    defaultPersonalKeys: {
      type: Array,
      default: () => [],
    },
    searchPlaceholder: {
      type: String,
      default: '请输入关键字...',
    },
    searchTimeGap: {
      type: Number,
      default: 600,
    },
    departmentData: {
      type: Array,
      default: () => [],
    },
    defaultDepartmentKeys: {
      type: Array,
      default: () => [],
    },
    selectAllNotice: {
      type: String,
      default: '你已选择状态为启用的所有人员（含：教职工、学生、其他）',
    },
    filterProp: {
      type: String,
      default: 'title',
    },
    selectLabels: {
      type: Array,
      default: () => ['全部', '部门', '个人'],
    },
    title: {
      type: String,
      default: '选择人员',
    },
    selectedTitle: {
      type: String,
      default: '已选人员',
    },
    departmentSuffixNotice: {
      type: String,
      default: '选中一个部门，相当于选择了该部门下所有有效的在职人员',
    },
    switchNotice: {
      type: String,
      default: '切换后，将清空已选数据确认是否切换？',
    },
    width: {
      type: Number,
      default: 800,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    defaultActiveKey: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      SELECT_TYPE,
      isFullScreen: false,
      activeTabKey: SELECT_TYPE.SELECT_ALL,
      SELECT_DEPARTMENT_DATA: [],
      SELECT_ALL_DATA: true,
      SELECT_PERSONAL_DATA: {
        users: [],
        taskIds: [],
      },
      isSubmit: false,
      visibleImport: false,
    };
  },

  beforeMount() {
    const hasVisibleListener = Object.prototype.hasOwnProperty.call(this.$listeners, 'update:visible');
    if (!hasVisibleListener) {
      const error = new Error('Pima-select-user component need a prop of [visible.sync]');
      throw error;
    }
    this.activeTabKey = this.defaultActiveKey || SELECT_TYPE.SELECT_ALL;
  },

  methods: {
    /**
     * 保存已选中的users
     */
    handleSaveUsers() {
      this.$emit('submit', {
        type: this.activeTabKey,
        data: this[`${this.activeTabKey}_DATA`],
      });
      this.isSubmit = true;
      this.onClose();
    },
    /**
     * 关闭组件
     */
    onClose() {
      this.isFullScreen = false;
      const hasCloseListener = Object.prototype.hasOwnProperty.call(this.$listeners, 'close');
      if (hasCloseListener) {
        this.$emit('close');
      } else {
        this.$emit('update:visible', false);
      }
      // if (!this.isSubmit) {
      //   this.activeTabKey = this.SELECT_TYPE.SELECT_ALL;
      // }
    },
    /**
     * 改变Tab选项
     * @param tabKey: {string|number} 点击Tab的key
     */
    changeTabs(tabKey) {
      if (tabKey === this.activeTabKey) return;
      let locked = false;
      if (this.activeTabKey === this.SELECT_TYPE.SELECT_DEPARTMENT) {
        /* eslint-disable-next-line */
        this.SELECT_DEPARTMENT_DATA.length ? (locked = true) : null;
      }
      if (this.activeTabKey === this.SELECT_TYPE.SELECT_PERSONAL) {
        /* eslint-disable-next-line */
        (this.SELECT_PERSONAL_DATA.users.length || this.SELECT_PERSONAL_DATA.taskIds.length) ? (locked = true) : null;
      }
      if (locked) {
        const self = this;
        this.$confirm({
          title: '切换后，将清空已选数据，确认是否切换？',
          class: 'pima-confrim',
          okText: self.$t('action.ok'),
          onOk() {
            self.activeTabKey = tabKey;
          },
        });
      } else {
        this.activeTabKey = tabKey;
      }
    },
    toggleSiderItem(e) {
      let li;
      const { className } = e.target;
      if (className.includes('sider-item-title')) {
        li = e.target.parentNode;
      } else {
        li = e.target;
      }
      const hasOpened = [...li.classList].indexOf('open') > -1;
      if (hasOpened) {
        const oldClassList = [...li.classList];
        const index = [...li.classList].indexOf('open');
        oldClassList.splice(index, 1);
        li.setAttribute('class', oldClassList.join(' '));
      } else {
        const newClassList = [...li.classList, 'open'];
        li.setAttribute('class', newClassList.join(' '));
      }
      const nodeList = Array.from(document.querySelectorAll('.sider-item'));
      nodeList.splice(Array.from(nodeList).indexOf(li), 1);
      nodeList.forEach((elem) => {
        const oldClassList = [...elem.classList];
        const index = [...elem.classList].indexOf('open');
        oldClassList.splice(index, 1);
        elem.setAttribute('class', oldClassList.join(' '));
      });
    },
  },
};
</script>

<style>
@import './index.less';
</style>
