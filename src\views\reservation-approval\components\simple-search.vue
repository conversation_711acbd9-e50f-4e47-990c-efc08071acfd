<template>
  <a-form
    :form="form"
    :label-col="{span: 5}"
    :wrapper-col="{span:15}"
    :layout="'inline'"
    label-align="right"
    :colon="false"
    @submit="handleSearch"
  >
    <a-form-item
      :label="$t('reserveQuery.form.simpleSearch.status')"
    >
      <a-select
        v-decorator="['statusList', {
          initialValue: filter.statusList,
        }]"
        mode="multiple"
        :options="reserveStatusOpts"
        style="width: 150px"
        :placeholder="$t('reserveQuery.form.advancedSearch.selectPlaceholder')"
        @change="onSearchConditionChange"
      />
    </a-form-item>
    <a-form-item>
      <a-select
        v-decorator="['timeRangeType', {
          initialValue: TimeRangeType.APPROVALTIME,
        }]"
        :options="timeRangeTypeOpts"
        style="width: 150px"
        @change="onSearchConditionChange"
      />
    </a-form-item>
    <a-form-item>
      <a-range-picker
        v-decorator="['dateRange', {
          initialValue: [filter.startDate, filter.endDate],
        }]"
        allow-clear
        style="width: 250px;"
        @change="onChangeRange"
      />
    </a-form-item>
    <a-form-item>
      <a-input
        v-decorator="['keyword', {
          initialValue: filter.keyword,
        }]"
        allow-clear
        style="width: 250px;margin-right: 10px"
        :placeholder="$t('reserveQuery.form.simpleSearch.keyword')"
        @pressEnter="handleSearch"
        @change="debounce(onSearchConditionChange, 1000)()"
      >
        <span
          slot="prefix"
          class="iconfont icon-all_sousuo"
          @click="handleSearch"
        />
      </a-input>
    </a-form-item>
    <a-form-item>
      <a-button
        type="link"
        @click="toggleAdvancedSearch"
      >
        {{ $t('reserveQuery.action.advancedSearch') }}
      </a-button>
    </a-form-item>
  </a-form>
</template>
<script>
import { hasOwn } from '@utils/core';
import { formatDate } from '@utils/dateformat';
import { TimeRangeType } from '@/constants/venue';
import { getreserveStatusI18Options, getTimeRangeTypeI18Options } from '../helps/handler';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, ...args);
    }, wait);
  };
}

export default {
  name: 'SimpleSearch',
  data() {
    this.form = this.$form.createForm(this);
    return {
      TimeRangeType,
      filter: {
        keyword: '',
        statusList: [],
        timeRangeType: '',
        applyStartDate: '',
        applyEndDate: '',
        reservationStartDate: '',
        reservationEndDate: '',
      },
    };
  },
  computed: {
    reserveStatusOpts() {
      return getreserveStatusI18Options(this, false);
    },
    timeRangeTypeOpts() {
      return getTimeRangeTypeI18Options(this, true);
    },
  },
  methods: {
    debounce,
    onChangeRange(date, dateString) {
      const [start, end] = dateString;
      if (this.form) {
        this.form.setFieldsValue({ dateRange: [start, end] });
        this.onSearchConditionChange();
      }
    },
    resetSearch() {
      this.form.resetFields();
      Object.keys(this.filter).forEach((k) => {
        if (k === 'timeRangeType') {
          this.filter[k] = this.TimeRangeType.APPROVALTIME;
        } else {
          this.filter[k] = '';
        }
      });
      this.$emit('reset-search', {
        page: 1,
        pageSize: 10,
        filter: {
          keyword: '',
          ...this.filter,
        },
      });
    },
    handleSearch() {
      this.form.validateFields((err, values) => {
        if (!err) {
          Object.keys(values).forEach((k) => {
            if (hasOwn(this.filter, k)) {
              this.filter[k] = values[k];
            }
          });
          const payload = {
            ...this.filter,
            ...values,
            statusList: values.statusList,
          };
          const [start, end] = payload.dateRange;
          if (payload.timeRangeType === 'createTime') {
            payload.applyStartDate = formatDate(start, 'short');
            payload.applyEndDate = formatDate(end, 'short');
          } else {
            payload.reservationStartDate = formatDate(start, 'short');
            payload.reservationEndDate = formatDate(end, 'short');
          }
          // 删除多余参数
          delete payload.dateRange;
          delete payload.timeRangeType;

          this.$emit('handle-search', {
            page: 1,
            filter: payload,
          });
        }
      });
    },
    toggleAdvancedSearch() {
      this.resetSearch();
      this.$emit('toggle-advanced-search', true);
    },
    onSearchConditionChange() {
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
  },
};
</script>
