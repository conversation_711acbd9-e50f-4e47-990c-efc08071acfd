<template>
  <a-time-picker
    :value="momentValue"
    :minute-step="minuteStep"
    :default-open-value="$moment('00:00:00', format)"
    :format="format"
    :disabled="disabled"
    :disabled-hours="disabledhours"
    :bordered="isBordered"
    :get-popup-container="
      triggerNode => {
        return triggerNode.parentNode;
      }"
    style="width: 170px;"
    @change="onChange"
  />
</template>

<script>
export default {
  model: {
    prop: 'defaultVal',
    event: 'change',
  },
  props: {
    isBordered: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    defaultVal: {
      type: String,
      default: '',
    },
    minuteStep: {
      type: Number,
      default: 1,
    },
    format: {
      type: String,
      default: 'HH:mm',
    },
    disabledhours: {
      type: Function,
      // eslint-disable-next-line
      default: function () {
        // return [0, 1, 2, 3, 4, 5, 6];
        return [];
      },
    },
  },
  computed: {
    momentValue() {
      if (!this.defaultVal) {
        return null;
      }
      return this.$moment(this.defaultVal, this.format);
    },
  },
  methods: {
    onChange(time, timeString) {
      this.$emit('change', timeString);
    },
    getDisabledHours() {
      return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
      ];
    },
  },
};
</script>
