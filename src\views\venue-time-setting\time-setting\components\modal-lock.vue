<template>
  <PopModal
    :title="t('title')"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="drawer-bd">
      <a-form-model
        ref="formRef"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 15 }"
        :colon="false"
        :model="formModel"
      >
        <a-form-item
          :label="' '"
        >
          <span class="tip">{{ t('content') }}</span>
        </a-form-item>
        <a-form-model-item
          prop="fixedVenueName"
          :label="tl('fixedVenueName')"
        >
          <a-input
            v-model="formModel.fixedVenueName"
            :placeholder="tp('fixedVenueName')"
            :max-length="20"
          />
        </a-form-model-item>
      </a-form-model>

      <div class="clearfix drawer-ft">
        <a-button @click="onClose">
          {{ $t('action.close') }}
        </a-button>
        <a-button
          type="primary"
          :loading="isLockingVenueAppointmentPeriod"
          @click="onSubmit"
        >
          {{ $t('action.ok') }}
        </a-button>
      </div>
    </div>
  </PopModal>
</template>


<script>
// import _ from 'lodash';
import { mapState } from 'vuex';
import PopModal from '@/components/base/pop-modal.vue';
import { venueStatusI18n, venuePropertyI18n } from '@/filters/venue';
import { nsI18n } from '@/mixins/ns-i18n';


export default {
  components: {
    PopModal,
  },

  filters: {
    venueStatusI18n,
    venuePropertyI18n,
  },

  mixins: [
    nsI18n('t', 'venueTimeSetting.timeSetting.modalLock'),
    nsI18n('tl', 'venueTimeSetting.timeSetting.modalLock.form.label'),
    nsI18n('tp', 'venueTimeSetting.timeSetting.modalLock.form.placeholder'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    venueId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      realValue: this.value,
      formModel: this.createFormModel(),
    };
  },

  computed: {
    ...mapState({
      isLockingVenueAppointmentPeriod: (state) => state.venueTimeSetting.isLockingVenueAppointmentPeriod,
    }),
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }

      if (val) {
        this.formModel = this.createFormModel();
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    createFormModel() {
      return {
        fixedVenueName: '',
      };
    },

    onSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            const payload = {
              id: this.venueId,
              fixedVenueName: this.formModel.fixedVenueName,
            };
            await this.$store.dispatch('venueTimeSetting/lockVenueAppointmentPeriod', payload);
            this.realValue = false;
            this.$message.success(this.$t('hint.dataSaved'));
            this.$emit('reload');
          } catch (err) {
            this.$message.error(err.response.data.errorMsg);
          }
        }
      });
    },

    onClose() {
      this.realValue = false;
    },
  },
};
</script>


<style lang="less" scoped>
.tip {
  color: fadeout(#000, 15%);
  font-size: 18px;
  font-weight: 500;
}
</style>
