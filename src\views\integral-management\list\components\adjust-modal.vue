<template>
  <PopModal
    :title="t('title')"
    :width="500"
    :visible.sync="visible"
    @close="onToggleVisible(false)"
  >
    <a-spin
      class="pima-spin"
      :spinning="loading"
    >
      <div class="drawer-bd">
        <a-form
          :form="form"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 15 }"
          :colon="false"
          @submit="onSubmit"
        >
          <a-form-item
            required
            :label="t('label.name')"
          >
            <span>
              {{ payload?.userName }}
            </span>
          </a-form-item>

          <a-form-item
            required
            :label="t('label.sn')"
          >
            <span>
              {{ payload?.userNo }}
            </span>
          </a-form-item>

          <a-form-item
            required
            :label="t('label.currentIntegral')"
          >
            <span>
              {{ payload?.balancePoints }}
            </span>
          </a-form-item>

          <a-form-item
            :label="t('label.adjustIntegral')"
          >
            <a-input-number
              v-decorator="['diffPoints', {
                initialValue: '',
                rules: [
                  { required: true, message: t('error.integralNum') },
                ]
              }]"
              :min="0"
              :precision="0"
              :step="1"
            />
          </a-form-item>

          <a-form-item
            :label="t('label.remark')"
          >
            <a-textarea
              v-decorator="['reason', {
                initialValue: '',
                rules: [
                  { required: true, message: t('error.remark') },
                ]
              }]"
              :max-length="500"
              :auto-size="{ minRows: 3, maxRows: 3 }"
            />
          </a-form-item>
        </a-form>

        <div class="clearfix drawer-ft">
          <a-button @click="onToggleVisible(false)">
            {{ ti('action.close') }}
          </a-button>

          <a-button
            type="primary"
            :loading="submitting"
            @click="onSubmit"
          >
            {{ ti('action.save') }}
          </a-button>
        </div>
      </div>
    </a-spin>
  </PopModal>
</template>


<script>
import PopModal from '@/components/base/pop-modal.vue';
import { namespaceT } from '@/helps/namespace-t';

export default {
  name: 'ModalAdjust',
  components: {
    PopModal,
  },


  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
    payload: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      t: namespaceT('integralManagement.modalAdjust'),
      ti: namespaceT(),
      visible: false,
      loading: false,

    };
  },

  methods: {
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            data: {
              ...values,
            },
            id: this.payload.id,
          };
          this.$emit('on-submit', payload);
        }
      });
    },

    onToggleVisible(flag) {
      this.visible = flag;
    },


  },
};
</script>
