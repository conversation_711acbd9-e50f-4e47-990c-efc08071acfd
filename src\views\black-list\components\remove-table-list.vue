<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :scroll="scroll"
    :pagination="pagination"
  >
    <template
      slot="reservationType"
      slot-scope="text"
    >
      {{ getReservationType(text) }}
    </template>

    <template
      slot="time"
      slot-scope="text"
    >
      {{ formatDate(text,'middle') }}
    </template>

    <template
      slot="createUserName"
      slot-scope="text, record"
    >
      {{ text }} <br>
      {{ formatDate(record.createTime,'middle') }}
    </template>
  </a-table>
</template>


<script>
import { mapActions, mapState } from 'vuex';

import { getReservationTypeApi } from '@/api/service-query-api';
import { formatDate } from '@utils/dateformat';
import { getColumns } from '../hooks/columns';


export default {
  name: 'RemoveTableList',

  props: {
    scroll: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      columns: getColumns(),
      reservationTypeOptions: [],
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.blacklist.remove.list,
      page: (state) => state.blacklist.remove.page,
      pageSize: (state) => state.blacklist.remove.pageSize,
      total: (state) => state.blacklist.remove.total,
      loading: (state) => state.blacklist.remove.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < 10) {
        return false;
      }
      return {
        current: this.page,
        showQuickJumper: true,
        showSizeChanger: true,
        defaultPageSize: this.pageSize,
        pageSize: this.pageSize,
        total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchRemoveBlackList({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchRemoveBlackList({
            page: current,
            pageSize: size,
          });
        },
      };
    },
  },

  mounted() {
    getReservationTypeApi()
      .then(({ model }) => {
        this.reservationTypeOptions = model.map((item) => ({
          code: item.code,
          value: item.code,
          name: item.name,
          label: item.name,
        }));
      });
  },
  methods: {
    ...mapActions({
      fetchRemoveBlackList: 'backlist/fetchRemoveBlackList',
    }),

    getReservationType(code) {
      const type = this.reservationTypeOptions.filter((item) => item.code === code);
      if (type.length) {
        return type[0].label;
      }
      return '';
    },

    formatDate,
  },
};
</script>
