<template>
  <div class="drawer-bd">
    <a-spin :spinning="submiting">
      <a-form
        :form="form"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        :colon="false"
        @submit="handleSubmit"
      >
        <!-- 中文名 -->
        <a-form-item
          :label="t('chName')"
        >
          <a-input
            v-decorator="['name', {
              initialValue: initData.name,
              rules: [
                { required: true, message: tf('chName') },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 英文名 -->
        <a-form-item
          :label="t('enName')"
        >
          <a-input
            v-decorator="['enName', {
              initialValue: initData.enName,
              rules: [
                { required: true, message: tf('enName') },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 场馆编号 -->
        <a-form-item
          :label="t('number')"
        >
          <a-input
            v-decorator="['sn', {
              initialValue: initData.sn,
              rules: [
                { required: true, message: tf('number') },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 场馆类别 -->
        <a-form-item
          :label="t('category')"
        >
          <a-select
            v-decorator="['category', {
              initialValue: initData.category,
              rules: [
                { required: true, message: tf('category') }
              ]
            }]"
            :get-popup-container="
              triggerNode => {
                return triggerNode.parentNode || document.body;
              }
            "
            :options="venueCategoryOptions"
            :placeholder="t('selectPlaceHolder')"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 场馆属性 -->
        <a-form-item
          :label="t('attribute')"
        >
          <a-select
            v-decorator="['attribute', {
              rules: [
                { required: true, message: tf('attribute') }
              ]
            }]"
            :get-popup-container="
              triggerNode => {
                return triggerNode.parentNode || document.body;
              }
            "
            :options="venueAttributeptions"
            :placeholder="t('selectPlaceHolder')"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 场馆管理人员 -->
        <a-form-item
          :label="t('administrator')"
        >
          <a-select
            v-decorator="['keeperIdList', {
              initialValue: initData.keeperList,
              rules: [
                { required: true, message: tf('administrator') },
              ]
            }]"
            :value="selectedUsers.map((item) => item['id'])"
            :default-value="defaultPersonalKeys"
            :filter-option="false"
            :not-found-content="fetching ? undefined : null"
            style="width:480px;"
            mode="multiple"
            :placeholder="$t('reserveQuery.form.advancedSearch.selectPlaceholder')"
            @change="(keys) => changeSelect(keys, 'id')"
            @search="(value) => debounce(fetchUsers)(value)"
            @dropdownVisibleChange="changeDropdownVisible"
          >
            <a-spin
              v-if="fetching"
              slot="notFoundContent"
              size="small"
            />
            <a-select-option
              v-for="item1 in persons"
              :key="item1.id"
            >
              {{ item1.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!-- 可容纳人数 -->
        <a-form-item
          :label="t('peopleNumber')"
        >
          <a-input
            v-decorator="['capacity', {
              initialValue: initData.capacity,
              rules: [
                { required: true, message: tf('peopleNumber') },
                { validator: isNumber },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 场馆位置 -->
        <a-form-item
          :label="t('location')"
        >
          <a-input
            v-decorator="['address', {
              initialValue: initData.address,
              rules: [
                { required: true, message: tf('location') },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 备注 -->
        <a-form-item
          :label="t('remarks')"
        >
          <a-textarea
            v-decorator="['remark', {
              initialValue: initData.remark,
              rules: [
                { required: false, message: '' },
                { max: 200, message: $t('user.form.tooLong') },
              ]
            }]"
            :auto-size="{ minRows: 6, maxRows: 6 }"
            :placeholder="t('remarkPlaceHolder')"
            style="width:1000px;"
          />
        </a-form-item>
        <!-- 是否启用 -->
        <a-form-item
          :label="t('isEnable')"
        >
          <a-radio-group
            v-decorator="['isEnable', {
              initialValue: true,
              rules: [
                { required: true, message: tf('isEnable') },
              ]
            }]"
          >
            <a-radio :value="VenueStatus.ENABLED">
              {{ getIsTrueStatusText(VenueStatus.ENABLED) }}
            </a-radio>
            <a-radio :value="VenueStatus.DISABLED">
              {{ getIsTrueStatusText(VenueStatus.DISABLED) }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script>
import { VenueStatus } from '@/constants/venue';
import { nsI18n } from '@/mixins/ns-i18n';
import { searchUserApi } from '@api/unidata-api';
import { getVenueAttributeI18Options, getIsTrueStatusText } from './handler';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, args);
    }, wait);
  };
}

/**
 * 获取选中的节点
 * @param personalData: Array<{key, value, title}>
 * @param selectedKeys: Array<string|number>
 * @param key: String
 * @return {*[]}
 */
const getSelectedPersonals = (personalData, selectedKeys, keyword = 'key') => {
  let results = [];
  if (Array.isArray(selectedKeys)) {
    // eslint-disable-next-line no-restricted-syntax
    for (const key of selectedKeys) {
      results = results.concat(...personalData.filter((Item) => Item[keyword] === key));
    }
    return results;
  }
  return results;
};


export default {
  name: 'AddVenue',
  components: {
  },
  mixins: [
    nsI18n('t', 'basicData.addVenue'),
    nsI18n('tf', 'basicData.form'),
  ],

  props: {
    initData: {
      type: Object,
      default: () => {},
    },
    submiting: {
      type: Boolean,
      default: false,
    },
    venueCategoryOptions: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      // 多选组件 开始
      fetching: false,
      defaultPersonalKeys: [],
      persons: [],
      selectedKeys: [],
      selectedUsers: [],
      changedVenueOptions: [],
      // 多选组件 结束
      VenueStatus,
      saving: false,
      formModel: this.createFormModel(),
    };
  },
  computed: {
    venueAttributeptions() {
      return getVenueAttributeI18Options(this);
    },
    personalData() {
      return [...this.$store.state.uniData.userList].map((item) => ({
        ...item,
        key: item.id,
        value: item.id,
        title: item.name,
      }));
    },
  },

  methods: {
    debounce,
    // 动态获取选择器选项
    fetchUsers(value) {
      if (!value || !value.length) return;
      this.fetching = true;
      searchUserApi(value)
        .then(({ model }) => {
          this.persons = model;
          this.fetching = false;
        })
        .catch((err) => {
          this.fetching = false;
          this.$message.error(err.errorList.message);
        });
    },
    // 动态存入选中的keyArray
    /**
     * select 改变选择
     * @param keys: {Array} 已经选中的人员的key
     * @param keyword: {string} select key
     */
    changeSelect(keys, keyword) {
      // this.persons = [];
      this.selectedKeys = keys;
      this.selectedUsers = [...getSelectedPersonals(this.personalData, this.selectedKeys, keyword)];
    },
    changeDropdownVisible(open) {
      if (!open) {
        this.persons = [];
      }
    },
    // 判断是否为大于等于0的整数
    isNumber(rule, value, cb) {
      if (value) {
        const reg = /^[0-9]*$/;
        if (!reg.test(value)) {
          return cb(this.tf('isNumber'));
        }
        if (value < 0) {
          return cb(this.tf('isNumber'));
        }
      }
      return cb();
    },
    // 显示是否
    getIsTrueStatusText(process) {
      return getIsTrueStatusText(this, process);
    },
    // 监听多选事件
    handleChange(value) {
      if (value.length) {
        const tempArr = value.map((item) => Number(item));
        this.form.setFieldsValue({ keeperIdList: tempArr });
      }
    },
    createFormModel() {
      return {
        name: '',
        enName: '',
        sn: '',
        category: '',
        attribute: '',
        keeperIdList: [],
        capacity: '',
        address: '',
        remark: '',
        isEnable: '',
      };
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        const { id } = this.initData;
        if (!err) {
          const payload = {
            ...values,
            id,
          };
          this.$emit('handle-add-submit', payload);
        }
      });
    },
    handleCancel() {
      this.$emit('handle-cancel-submit');
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
}

</style>
