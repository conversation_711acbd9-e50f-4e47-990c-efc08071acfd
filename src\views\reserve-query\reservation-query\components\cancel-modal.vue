<template>
  <PopModal
    :title="t('title')"
    :width="450"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="drawer-bd">
      <p>{{ t('label') }}</p>
      <a-form
        :form="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :colon="false"
        @submit="onSubmit"
      >
        <a-form-item
          :label="t('reason')"
        >
          <a-radio-group
            v-decorator="['cancelType', {
              initialValue: '',
              rules: [
                { required: true, message: $t('reserveRule.form.pushSettings') },
              ]
            }]"
          >
            <a-radio :value="CancelType.OFF">
              {{ getCancelTypeI18Text(this, CancelType.OFF) }}
            </a-radio>
            <a-radio :value="CancelType.OTHER">
              {{ getCancelTypeI18Text(this, CancelType.OTHER) }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          :label="t('remarks')"
        >
          <a-textarea
            v-decorator="['remark', {
              initialValue: '',
              rules: [
                { required: false, message: t('remarks') },
                { max: 200, message: t('remarks') },
              ]
            }]"
            :auto-size="{ minRows: 6, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
      <div class="clearfix drawer-ft">
        <a-button @click="onClose">
          {{ $t('action.close') }}
        </a-button>
        <a-button
          type="primary"
          :loading="submiting"
          @click="onSubmit"
        >
          {{ $t('action.ok') }}
        </a-button>
      </div>
    </div>
  </PopModal>
</template>


<script>
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';
import { CancelType } from '@/constants/venue';
import { getCancelTypeI18Text } from './handler';


export default {
  name: 'ModalCancel',
  components: {
    PopModal,
  },

  mixins: [
    nsI18n('t', 'reserveQuery.cancelModal'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    submiting: {
      type: Boolean,
      default: false,
    },
    id: {
      type: Number,
      default: 0,
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      realValue: this.value,
      CancelType,
    };
  },

  computed: {
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    getCancelTypeI18Text,
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            ...values,
            id: this.id,
            isAdminCancel: true,
          };
          this.$emit('handle-cancel-submit', payload);
        }
      });
    },

    onClose() {
      this.realValue = false;
    },
  },
};
</script>
