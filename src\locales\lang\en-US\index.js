import appHeader from './app-header';
import applicationList from './application-list';
import constants from './constants';
import user from './user';
import basicData from './basic-data';
import reserveQuery from './reserve-query';
import reserveRule from './reserve-rule';
import venueCalendar from './venue-calendar';
import venueTimeSetting from './venue-time-setting';
import special from './special';
import blacklist from './blacklist';
import userNotes from './user-notes';
import sign from './sign';
import integralManagement from './integral-management';

export default {
  title: 'venue reservation',
  enableJavaScriptTips: `Sorry, the website will work properly only after JavaScript is enabled.
  Please enable it and continue.`,
  forbiddenTips: 'Sorry, you do not have permission to access this application',
  common: {
    YES: 'Yes',
    NO: 'No',
    all: 'All',
  },
  action: {
    close: 'Close',
    ok: 'Confirm',
    cancle: 'Cancel',
    edit: 'Edit',
    del: 'Delete',
    add: 'Add',
    search: 'Search',
    clear: 'Clear',
    import: 'Import',
    export: 'Export',
    view: 'View',
    save: 'Save',
    reset: 'Reset',
    highSearch: 'Advanced Search',
    upload: 'Upload',
    submit: 'Submit',
    copy: '复制',
    set: '设置',
    moveout: '移出',
    verify: '签到核销',
    modify: '修改',
  },
  pagination: {
    total: 'Total {total}',
    totalLong: 'A total of {totalPage} pages and {total} records',
    totalTips: 'A total of {totalNumberOfPages} pages and {totalNumberOfRecords} records',
    prev: 'prev',
    next: 'next',
  },
  msg: {
    operateSuccess: 'Successful operation',
    uploading: 'Uploading...',
    loading: 'In processing...',
    textTip: 'Click on the left menu to view details',
    permanentDel: 'This operation will be permanently deleted. Confirm to continue?',
    delSucc: 'User deleted successfully',
    startTime: 'Start time should be earlier than end time',
    endTime: 'End time should be later than start time',
    imgType: 'The upload image should be in jpeg, jpg, png format',
    imgSize: 'The image must be less than 2MB!',
    confirmDel: 'Confirm whether to delete？',
    imgSizeFive: 'The image must be less than 5MB!',
    fileTypeMsg: '文件应为 {types} 格式!',
    fileSizeMsg: '文件大小需小于{size}MB!',
    contentSoLongWarn: '内容过长...',
    emtryData: 'No Data',
  },
  hint: {
    required: '此项为必填',
    dataSaved: '保存成功',
    savingFailed: '保存失败',
  },
  pimaUI: {
    appHeader,
    applicationList,
  },
  form: {
    maxLength: '不能超过{num}个字符',
  },
  constants,
  user,
  basicData,
  reserveRule,
  reserveQuery,
  venueCalendar,
  venueTimeSetting,
  special,
  blacklist,
  userNotes,
  sign,
  integralManagement,
};
