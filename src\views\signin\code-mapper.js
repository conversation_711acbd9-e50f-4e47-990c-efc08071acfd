export const shiftCodeMap = (value) => {
  const mapper = new Map([
    ['Digit7', '&'],
    ['Slash', '?'],
    ['Semicolon', ':'],
  ]);
  return mapper.get(value);
};

export const codeMap = (value) => {
  const mapper = new Map([
    ['Semicolon', ';'],
    ['Slash', '/'],
    ['Period', '.'],
    ['Equal', '='],
    ['Minus', '-'],
    ['KeyA', 'a'],
    ['KeyB', 'b'],
    ['KeyC', 'c'],
    ['KeyD', 'd'],
    ['KeyE', 'e'],
    ['KeyF', 'f'],
    ['KeyG', 'g'],
    ['KeyH', 'h'],
    ['KeyI', 'i'],
    ['KeyJ', 'j'],
    ['KeyK', 'k'],
    ['KeyL', 'l'],
    ['KeyM', 'm'],
    ['KeyN', 'n'],
    ['KeyO', 'o'],
    ['KeyP', 'p'],
    ['KeyQ', 'q'],
    ['KeyR', 'r'],
    ['KeyS', 's'],
    ['KeyT', 't'],
    ['KeyU', 'u'],
    ['KeyV', 'v'],
    ['KeyW', 'w'],
    ['KeyX', 'x'],
    ['KeyY', 'y'],
    ['KeyZ', 'z'],
    ['Digit0', '0'],
    ['Digit1', '1'],
    ['Digit2', '2'],
    ['Digit3', '3'],
    ['Digit4', '4'],
    ['Digit5', '5'],
    ['Digit6', '6'],
    ['Digit7', '7'],
    ['Digit8', '8'],
    ['Digit9', '9'],
  ]);
  return mapper.get(value);
};
