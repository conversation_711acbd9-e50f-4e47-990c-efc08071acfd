<template>
  <PopModal
    :title="t('title.setting')"
    :visible.sync="realValue"
    :width="900"
    @close="onClose"
  >
    <div class="drawer-bd">
      <a-spin :spinning="submiting">
        <template v-if="initPeriodArrays.length">
          <ChildPage
            v-for="(item, index) in initPeriodArrays"
            ref="childPage"
            :key="index"
            :index="index"
            :init-period-arrays="initPeriodArrays"
            @post-delete-item="onPostDeleteItem"
            @post-add-item="onPostAddItem"
            @post-enable-item="onPostAddItem"
            @add-index="onAddIndex"
            @update-data="getData"
          />
        </template>
      </a-spin>
    </div>
  </PopModal>
</template>


<script>
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';

import {
  updateReservationRulesTimesApi,
  deleteReservationRulesTimesApi,
  getReservationRulesTimesApi,
} from '@api/reserve-rule-api';
import ChildPage from './child-page.vue';

export default {
  name: 'ModalCancel',
  components: {
    PopModal,
    ChildPage,
  },

  mixins: [
    nsI18n('t', 'reserveRule'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    rowId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      initEditable: false,
      initPeriodArrays: [],
      realValue: this.value,
      submiting: false,
      // 仅获取数据使用
      initRowId: 0,
    };
  },
  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }
    },
    realValue(val) {
      this.$emit('input', val);
    },
    async initRowId(val) {
      if (val) {
        this.initRowId = 0;
        this.initPeriodArrays = [];
        getReservationRulesTimesApi(this.rowId)
          .then(({ model = [] }) => {
            if (model && model.length) {
              this.initPeriodArrays = model;
            } else {
            // 无时段设置,新建默认编辑状态
              this.initPeriodArrays = [{ startTime: '', endTime: '', isEnable: true }];
              this.initEditable = true;
            }
            this.submiting = false;
          })
          .catch((error) => {
            this.$message.error(error.message);
            this.submiting = false;
          });
      }
    },
  },

  updated() {
    if (this.initEditable) {
      // 开启默认编辑状态后注销监听
      this.initEditable = false;
      (this.$refs.childPage)[this.initPeriodArrays.length - 1].handleEditItem();
    }
  },

  methods: {
    //  添加索引
    onAddIndex() {
      this.initPeriodArrays.push({ startTime: '', endTime: '', isEnable: true });
      // 新建默认编辑状态
      this.initEditable = true;
    },
    // 删除接口删除数据
    onPostDeleteItem(index) {
      // if (index !== 0) {
      if ((this.initPeriodArrays[index]).id) {
        const reservationRuleTimeId = (this.initPeriodArrays[index]).id;
        this.submiting = true;
        const id = this.rowId;
        deleteReservationRulesTimesApi({ id, reservationRuleTimeId })
          .then(() => {
          // 提示操作成功
            this.$message.success(this.t('msg.success'));
            // 取消列表显示
            this.initPeriodArrays.splice(index, 1);
            // 刷新页面
            this.initRowId = this.rowId;
          })
          .catch((err) => {
            this.$message.error(err.response.data.errorMsg);
            this.submiting = false;
          });
      } else {
        this.initPeriodArrays.splice(index, 1);
      }
      // }
    },
    // 添加接口添加数据
    onPostAddItem(index) {
      const reservationRuleTimeDTO = this.initPeriodArrays[index];
      const id = this.rowId;
      this.submiting = true;
      updateReservationRulesTimesApi(id, reservationRuleTimeDTO)
        .then(() => {
        // 提示操作成功
          this.$message.success(this.t('msg.success'));
          // // 取消编辑状态
          // (this.$refs.childPage)[index].handleEditItem();
          // // 刷新页面
          // this.initRowId = this.rowId;
          this.submiting = false;
        })
        .catch((err) => {
        // 提示操作失败
          this.$message.error(err.response.data.errorMsg);
          // 取消编辑状态
          this.submiting = false;
          // // 刷新页面
          // this.initRowId = this.rowId;
          this.submiting = false;
        });
    },
    getData(val) {
      const { index } = val;
      this.initPeriodArrays[index] = val.data;
    },
    handleSubmit() {
    },

    onClose() {
      this.realValue = false;
      this.initPeriodArrays = [];
    },
  },
};
</script>
