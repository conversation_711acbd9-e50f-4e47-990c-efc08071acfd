# 北大 / 场馆预约

## 安装

### 编译依赖

这个项目使用 [node](https://nodejs.org/) 和 [yarn](https://yarnpkg.com/)。请确保你本地安装了它们。

- node 需要 [14.18.x](https://nodejs.org/en/download/) 或以上TLS版本（v16.x以上未测试，不建议使用）
- yarn 需要 [1.22.x](https://yarnpkg.com/getting-started/install) 或以上版本

### 服务器依赖

1. Redis 服务器

    服务运行时需使用 Redis 相关服务，请确保已准备。123

2. 外部依赖服务项

    服务运行时会访问以下相关的外部依服资源，请务必先拿到相关信息，具体如下表所示：

| 依赖服务                   | 项目名称                                                                      | 备注                                                    |
|---------------------------|------------------------------------------------------------------------------|-------------------------------------------------------|
| CAS                       | [cas](https://pkuszdev.doocom.cn/cas)                             | 统一身份认证                                            |
| 公共接口                  | [bdc_core](https://pkuszdev.doocom.cn/bdc_core)                     | 申请/刷新 Token 数据交互                 |
| 统一权限接口              | [bdc_auth](https://pkuszdev.doocom.cn/bdc_auth)                    | 统一权限数据交互                                         |
| 统一数据接口              | [bdc_arch](https://pkuszdev.doocom.cn/bdc_arch)                     | 统一数据数据交互                                      |
| 报修接口                  | [pkusz_repair](https://pkuszdev.doocom.cn/pkusz_repair)             | 报修应用数据交互                                   |
| 微平台接口                | [pkusz_wx](https://pkuszdev.doocom.cn/pkusz_wx)                     | 微平台应用数据交互                                 |
| 统一应用接口              | [bdc_service](https://pkuszdev.doocom.cn/bdc_service)               | 统一应用数据交互                         |
| 附件管理接口              | [bdc_dfs](https://pkuszdev.doocom.cn/bdc_dfs)                       | 上传附件业务数据交互                                      |
| 静态资源                  | [static-resources](https://pkuszdev.doocom.cn/static-resources)     | 前端资源，引入字体、图片等公用资源                    |
| 远程 UI 入口文件          | [remote-ui](https://pkuszdev.doocom.cn/remote-ui/remoteEntry.js)    | 前端资源，引入远程公用的头部 UI，Webpack Module Federation |

## 运行示例（测试用）

1. 克隆代码，执行命令 ```<NAME_EMAIL>:pkusz/pkusz-empty-nodejs.git```
2. 进入工程文件夹，执行命令 ```cd pkusz-empty-nodejs```
3. 安装项目依赖，执行命令 ```yarn install --production=false```
4. 配置 [.env](#.env配置文件说明) 配置项
5. 启动示例，执行命令 ```yarn run server```

## 构建（生产用）

1. 克隆代码，执行命令 ```<NAME_EMAIL>:pkusz/pkusz-empty-nodejs.git```
2. 进入工程文件夹，执行命令 ```cd pkusz-empty-nodejs```
3. 安装项目 Webpack 依赖，执行命令 ```yarn install --production=false```
4. 配置 [.env](#.env配置文件说明) 配置项
5. Webpack 构建，执行命令 ```yarn run build```
6. 删除临时依赖，执行命令 ```rm -rf ./node_modules```
7. 安装生产使用依赖，执行命令 ```yarn install --production=true```

## 部署

### 复制环境生产使用文件

生产环境下，只需要使用部分文件夹和文件，具体如下：

| 路径                       | 备注                                           |
|---------------------------|------------------------------------------------|
| build/                    |                                                |
| dist/                     |                                                |
| node_modules/             |                                                |
| server/                   |                                                |
| 401.html                  |                                                |
| 404.html                  |                                                |
| 500.html                  |                                                |
| .env                      | 非必要，环境变量配置                            |
| pm2.json                  | 非必要，PM2 配置文件                            |
| index.template.html       |                                                |
| package.json              |                                                |

### 在生产环境启动项目

- 使用 PM2 进程管理工具启动

  建议使用 [PM2](https://pm2.keymetrics.io/) 做为 node 进程管理工具（非必须），项目带有文件名为 ecosystem.config.js 的默认 PM2 配置文件，可直接用于启动。

  - PM2需要 [3.x](https://pm2.keymetrics.io/docs/usage/quick-start/) 或以上版本

  执行命令 ```pm2 start ./pm2.json --env production```

- 直接启动

  执行命令 ```yarn run server```

### 配置 nginx

建议项，非必要。

由于构建完后，会生成 dist/static/ 文件夹，该文件夹内容为静态访问资源，通过 nginx 配置，可减少 node 端负载。

找到 nginx 配置文件，具体修改如下：

```
server {
  ...

  location /static/ { /* 静态资源访问路径 */
    alias /home/<USER>/pkusz-empty-nodejs/dist/static/; /* 构建输出dist⽂件夹下的静态资源文件夹路径 */
  }
}
```

## .env配置文件说明

项目运行前需要配置 .env 文件，.env 文件不存在项目的版本管理系统(git)当中，需要单独创建，.env 配置文件需存放在项目根目录中。

项目提供配置对照文件 .env.sample，可复制该文件创建 .env 文件。

请注意，提供 .env 配置文件的目的，是为了不能修改直接系统环境变量的情况下，做为补充的配置手段。如果你已经在环境变量配置对应的项，则可以不用创建 .env 文件。

### 项目 .env 文件所需的配置选项

| 键值                            | 备注                               | 必填 | 默认值                           | 示例                     | 构建时使用 | 说明                     | 最后修改时间          |
|---------------------------------|------------------------------------|-----|---------------------------------|--------------------------|----------|-------------------------|---------------------|
| EXPRESS_PORT                    | Express 运行端口号                  |     | 8080                            | 8080                     |          |                         |                     |
| SERVICE_URL                     | 服务访问 URL                        |     | http://localhost:{EXPRESS_PORT} | https://example.com:8080 |          | 不包含根目录<br>如果该值没有填写，将默认值为 http://localhost: 加上 EXPRESS_PORT 配置值 |                     |
| PUBLIC_PATH                     | 根目录                              |     | /                               | /test-folder/            | ✓        |与 SERVICE_URL 组成完整的访问路径，<br>如：http://example.com:8080/test-folder/ |                     |
| HOST_PROTOCOL                   | 宿主协议                            |     | http                               | http            | ✓        |宿主协议替换，<br>如：https |                     |
| SERVICE_CODE                    | 服务编码                            | ✓   |                                 | pima-repair                | ✓        | 应用管理对应配置    |                     |
| CLIENT_ID                       | 客户端 ID                           | ✓   |                                 |                          |          | 客户端对应配置     |                     |
| CLIENT_SECRET                   | 客户端密钥                           | ✓   |                                  |                          |          | 客户端对应配置     |                     |
| SESSION_ID_COOKIE_NAME          | 用于储存 Session ID 的 Cookie 键值   | ✓   |                                 | pima-bdc-remoteui.sid         |          | 本应用使用               |                     |
| ACCESS_TOKEN_COOKIE_NAME        | 用于储存 Access Token 的 Cookie 键值 | ✓   |                                 | pima-bdc-remoteui.at          | ✓        | 本应用使用               |                     |
| LOCALE_COOKIE_NAME              | 用于储存多语言的 Cookie 键值         | ✓   |                                 | pima-bdc-remoteui.locale                | ✓        | 本应用使用  |                     |
| SUPPORT_LOCALES                 | 多语言支持语言范围                   | ✓   |                                 | zh-CN,en-US                | ✓        | 支持的多语言类型  |                     |
| FALLBACK_LOCALE                 | 多语言默认语言                       | ✓   | zh-CN                                | zh-CN                | ✓        | 多语言默认语言  |                     |
| SERVICE_TYPE                    | 应用类型                            | ✓   |                                 | pc                | ✓        | 移动端接口判断，移动版值为qywx，PC版值为pc  |                     |
| WX_CODE_URL                     | 微信授权 URL                        | ✓   |                                 | http://pima-entry.doocom.net/qy/weixin/auth                | ✓        | 获取微信授权码 URL  |                     |
| WX_USER_INFO_URL                | 获取用户信息 URL                     | ✓   |                                 | http://pima-entry.doocom.net/third-parties/user-info                | ✓        | 根据微信CODE获取用户信息 URL  |                     |
| WX_AGENT_ID                     | 企业微信 ID                         | ✓   |                                 |                  | ✓        | 企业微信 ID，通过企业微信官网获取  |                     |
| WX_AGENT_KEY                    | 企业微信密钥                        | ✓   |                                 |                   | ✓        | 企业微信密钥，通过企业微信官网获取  |                     |
| PKUSZ_BASE_PATH                 | 北大信息门户 URL                    | ✓   |                                 | https://pkuszdev.doocom.cn                | ✓        | 北大信息门户 URL  |                     |
| BDC_MSG_API_BASE_URL            | 消息中心 URL                        | ✓   |                                 | https://pkuszdev.doocom.cn/bdc_msg                | ✓        | 消息中心 URL  |                     |
| PKUSZ_API_BASE_URL              | 北大信息门户中文版 URL               | ✓   |                                 | https://pkuszdev.doocom.cn/pkusz_inner                | ✓        | 北大信息门户中文版 URL  |                     |
| PKUSZ_API_BASE_URL_EN           | 北大信息门户英文版 URL               | ✓   |                                 | https://pkuszdev.doocom.cn/pkusz_inner_en                | ✓        | 北大信息门户英文版 URL  |                     |
| FAVICON_URL                     | Favicon图片地址                     | ✓   |                                 | https://pkuszdev.doocom.cn/static-resources/favicon.ico                | ✓        | Favicon图片地址  |                     |
| LOGO_URL                        | Logo图片地址                        | ✓   |                                 | https://pkuszdev.doocom.cn/static-resources/logo.png                | ✓        | Logo图片地址  |                     |
| PIMA_MESSAGE_CENTRE_URL         | 消息中心地址                        |      |                                 | /bdc-message/                | ✓        | 消息中心地址，不配置时隐藏链接  |                     |
| PIMA_CHANGE_PASSWORD_URL        | 修改密码地址                         |      |                                 | /pkusz-personnel/                | ✓        | 修改密码地址  |                     |

#### 接口相关配置
| 键值                            | 备注                               | 必填 | 默认值                  | 示例                                                               | 构建时使用 | 说明                    | 最后修改时间          |
|--------------------------------|------------------------------------|-----|------------------------|-------------------------------------------------------------------|----------|-------------------------|---------------------|
| BDC_CORE_API_BASE_URL          | 公共接口 URL                        | ✓   |                        | https://pkuszdev.doocom.cn/bdc_core                                  |          | 用于 Token 申请<br>具体生产配置地址请找运维咨询 |                     |
| AUTH_API_BASE_URL              | 统一权限接口 URL                    | ✓   |                        | https://pkuszdev.doocom.cn/bdc_auth                   |          | 具体生产配置地址请找运维咨询 |                     |
| IMPORT_API_BASE_URL              | 选人组件导入URL URL                    | ✓   |                        | https://pkuszdev.doocom.cn/bdc_import                   |          | 具体生产配置地址请找运维咨询 |                     |
| ARCH_API_BASE_URL              | 统一数据接口 URL                    | ✓   |                        | https://pkuszdev.doocom.cn/bdc_arch                             |          | 具体生产配置地址请找运维咨询 |                     |
| APP_API_BASE_URL               | 应用接口 URL                        | ✓   |                        | https://pkuszdev.doocom.cn/pkusz_repair                                      |          | 具体生产配置地址请找运维咨询 |                     |
| WX_API_BASE_URL                | 微平台应用接口 URL                   | ✓   |                        | https://pkuszdev.doocom.cn/pkusz_wx                                               |          | 具体生产配置地址请找运维咨询 |                     |
| SERVICE_API_BASE_URL           | 统一应用接口 URL                     | ✓   |                        | https://pkuszdev.doocom.cn/bdc_service                                            |          | 具体生产配置地址请找运维咨询 |                     |
| PIMA_UPLOAD_BASE_URL           | 附件管理接口 URL                     | ✓   |                        | https://pkuszdev.doocom.cn/bdc_dfs                                    |          | 具体生产配置地址请找运维咨询 |                     |
| STATIC_RESOURCES_URL           | 静态资源 URL                         | ✓   |                        | https://pkuszdev.doocom.cn/static-resources                                   |          | 具体生产配置地址请找运维咨询 |                     |
| PIMA_REMOTE_UI_ENTRY_URL       | 远程 UI 入口文件 URL                 | ✓   |                        | https://pkuszdev.doocom.cn/remote-ui/remoteEntry.js        | ✓        | 具体生产配置地址请找运维咨询 |                     |

#### CAS相关配置
| 键值                            | 备注                               | 必填 | 默认值                  | 示例                     | 构建时使用 | 说明                    | 最后修改时间          |
|--------------------------------|------------------------------------|-----|------------------------|-------------------------|----------|-------------------------|---------------------|
| CAS_SERVICE_BASE_URL                   | CAS 服务 URL                        | ✓   |                        | https://pkuszdev.doocom.cn/cas  |          | 具体生产配置地址请找运维咨询 |                     |
| CAS_SERVICE_VERSION                    | CAS 版本                            | ✓   |                        | 5.2.3                   |          | 预留                     |                     |

#### Redis相关配置
| 键值                            | 备注                               | 必填 | 默认值                  | 示例                              | 构建时使用 | 说明                     | 最后修改时间          |
|--------------------------------|------------------------------------|-----|------------------------|----------------------------------|-----------|-------------------------|---------------------|
| REDIS_HOST                      | Redis 连接 URL                  | ✓   |                        | ************* |          | 具体生产配置地址请找运维咨询 |                     |
| REDIS_PORT                 | Redis 连接端口                      |     | 6379                   | 6379                                 |          | Redis 服务器的 UNIX 套接字字符串 |            |
| REDIS_DB                 | Redis 连接数据库                      |     |                        |                                   |          | 客户端将在连接上运行 Redis ** select ** 命令 |            |
| REDIS_PASSWORD                   | Redis 连接密码                |     |                       |                         |           | Redis 连接密码 |                     |
| REDIS_PREFIX                   | Redis 存储时的键值前缀                |     |                       | pima:bdc:remoteui:                      |           | 用于为所有已用键添加前缀的字符串，避免与其他应用重复 |                     |

## 主要使用技术框架

- [webpack](https://webpack.js.org/)
- [Babel](https://babeljs.io/)
- [Express](http://expressjs.com/)
- [Vue.js](https://vuejs.org/)
- [vue-router](https://router.vuejs.org/)
- [vue-server-renderer](https://ssr.vuejs.org/)
- [vue-i18n](https://kazupon.github.io/vue-i18n/)
