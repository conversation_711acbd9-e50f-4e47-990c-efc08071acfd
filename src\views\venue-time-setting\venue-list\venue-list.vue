<template>
  <div
    v-if="hasP((P) => P.VenueTimeSetting.View)"
    class="page-panel clearfix"
  >
    <div class="content-panel">
      <a-page-header :ghost="false">
        <a-form
          layout="inline"
          :colon="false"
          :form="form"
        >
          <a-form-item
            self-update
          >
            <a-input
              v-decorator="['keyword', {
                initialValue: ''
              }]"
              :placeholder="t('simpleSearch.form.keywordPlaceholder')"
              style="width: 300px"
              allow-clear
              @pressEnter="onKeywordInputEnter"
              @change="onKeywordInputChange"
            >
              <span
                slot="prefix"
                class="iconfont icon-all_sousuo"
                @click="onSearch"
              />
            </a-input>
          </a-form-item>
        </a-form>
      </a-page-header>

      <div class="panel-body">
        <TableList
          :data-source="dataSource"
          :total-size="totalSize"
          :is-loading="isLoading"
          :page-info.sync="queryInfo"
          @view="onView"
          @pageChange="onPageChange"
        />
      </div>
    </div>

    <ModalDetail
      v-model="detailModal.show"
      :payload="detailModal.payload"
      @reload="loadData"
    />
  </div>
</template>


<script>
import _ from 'lodash';
import { mapState } from 'vuex';
import OpreationMixin from '@/mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';

import TableList from './components/table-list.vue';
import ModalDetail from './components/modal-detail.vue';


export default {
  components: {
    TableList,
    ModalDetail,
  },

  mixins: [
    OpreationMixin,
    nsI18n('t', 'venueTimeSetting'),
  ],

  data() {
    return {
      form: this.$form.createForm(this),
      detailModal: {
        show: false,
        payload: null,
      },
      queryInfo: {
        limit: 10,
        page: 1,
      },
    };
  },

  computed: {
    ...mapState({
      dataSource: (state) => state.venueTimeSetting.venueList,
      totalSize: (state) => state.venueTimeSetting.venueListTotal,
      isLoading: (state) => state.venueTimeSetting.isLoadingVenueList,
    }),
  },

  mounted() {
    this.onSearch();
  },

  methods: {
    onKeywordInputEnter() {
      this.onSearch();
    },

    onKeywordInputChange: _.debounce(function onInput() {
      this.onSearch();
    }, 1000),

    onSearch() {
      this.loadData();
    },

    onView(record) {
      this.detailModal.payload = record;
      this.detailModal.show = true;
    },

    onPageChange(page, limit) {
      this.queryInfo.page = page;
      this.queryInfo.limit = limit;
      this.loadData();
    },

    loadData() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          this.$store.dispatch('venueTimeSetting/fetchVenueList', {
            ...values,
            ...this.queryInfo,
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-content {
  position: absolute;
  top: 7px;
  padding-top: 0;
  overflow: visible;
}
</style>
