<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <a-layout>
        <a-layout-header class="layout-header">
          <ClientOnly>
            <PimaAppHeader
              ref="pimaAppHeader"
              :deployment-env="deploymentEnv"
              :service-code="serviceCode"
              :service-type="serviceType"
              :logo-url="logoUrl"
              :service-name="$t('title')"
              :get-authorization="getAuthorization"
              :api-base-url="apiBaseUrl"
              :has-sidebar="!isForbidden"
              :menu-selected-keys="['Application']"
              :support-locales="supportLocales"
              :message-url="messageUrl"
              :change-password-url="changePasswordUrl"
              :locale="localeRemote"
              @user-data-fetched="handleUserDataFetched"
              @click-service-name="handleClickServiceName"
              @logout="handleLogout"
            />
          </ClientOnly>
        </a-layout-header>
        <a-layout class="ant-layout-has-sider">
          <a-layout-sider
            v-if="isForbidden === false"
            class="page-layout-sider"
            width="240"
          >
            <ClientOnly>
              <PimaAppSider
                ref="pimaAppSider"
                :service-name="serviceName"
                :menu="sidebar"
                :open-keys="sidebarOpenKeys"
                :selected-keys="sidebarSelectedKeys"
                :icon-mapper="sidebarIconMapper"
                @select="handleSidebarMenuSelect"
              />
            </ClientOnly>
          </a-layout-sider>
          <a-config-provider>
            <a-layout-content class="page-layout-content">
              <div
                v-if="isForbidden === false"
                class="content-wrap"
              >
                <transition
                  name="fade"
                  mode="out-in"
                >
                  <router-view v-if="showView" />
                </transition>
              </div>
              <a-layout-content v-else>
                <a-result
                  status="403"
                  title="403"
                  :sub-title="$t('forbiddenTips')"
                />
              </a-layout-content>
            </a-layout-content>
            <EmtryResult slot="renderEmpty" />
          </a-config-provider>
        </a-layout>
      </a-layout>
    </a-config-provider>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import ClientOnly from 'vue-client-only';
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';
import zhTW from 'ant-design-vue/lib/locale-provider/zh_TW';
import enUS from 'ant-design-vue/lib/locale-provider/en_US';
import EmtryResult from '@/components/base/EmtryResult/index.vue';

import config from './config';
import errorCode from './constants/error-code';
import sidebarIconMapper from './constants/sidebar-icon-mapper';
import { getSidebarMenuItemList } from './utils';
// import Sidebar from './components/sidebar.vue';

export default {
  components: {
    // Sidebar,
    ClientOnly,
    EmtryResult,
    PimaAppHeader: () => import('PimaRemoteUI/PimaAppHeader'),
    PimaAppSider: () => import('PimaRemoteUI/PimaAppSider'),
  },

  data() {
    return {
      deploymentEnv: config.deploymentEnv,
      supportLocales: config.supportLocales,
      serviceCode: config.serviceCode,
      serviceType: config.serviceType,
      logoUrl: config.logoUrl,
      apiBaseUrl: config.bdcCoreApiBaseUrl,
      messageUrl: config.pimaMessageCentreUrl,
      changePasswordUrl: config.pimaChangePasswordUrl,
      sidebar: [],
      sidebarMenuUrlMapper: {},
      sidebarOpenKeys: [],
      sidebarSelectedKeys: [],
      sidebarIconMapper,
      hasIdentifierTips: false,
      isForbidden: false,
      serviceName: this.$t('title'),
      localeRemote: this.$i18n.locale,
      showView: true,
    };
  },

  computed: {
    ...mapState({
      sidebarMenuItems: (state) => state.user.sidebarMenuItems,
    }),
    locale() {
      if (this.$i18n.locale === 'zh-CN') {
        return zhCN;
      }
      if (this.$i18n.locale === 'zh-HK') {
        return zhTW;
      }
      return enUS;
    },
  },

  watch: {
    '$route'(val) {
      if (val && val.meta) {
        const { sidebarOpenKeys, sidebarSelectedKeys } = val.meta;
        this.sidebarOpenKeys = sidebarOpenKeys || [];
        this.sidebarSelectedKeys = sidebarSelectedKeys || [];
      }
    },
  },
  beforeMount() {
    if (this.$eventBus) {
      // 监听接口出错
      this.$eventBus.$on('api-error', this.handleApiError);
      // 监听侧边栏选中
      this.$eventBus.$on('sidebar-select', this.handleSidebarSelect);
      // 监听操作刷新用户数据
      this.$eventBus.$on('refresh-user-data', this.handleRefreshUserData);
    }

    // 侧边栏默认选中
    if (this.$route && this.$route.meta) {
      const { sidebarOpenKeys, sidebarSelectedKeys } = this.$route.meta;
      this.sidebarOpenKeys = sidebarOpenKeys || [];
      this.sidebarSelectedKeys = sidebarSelectedKeys || [];
    }
  },
  methods: {
    ...mapActions({
      clearUserCookies: 'user/clearUserCookies',
    }),
    ...mapMutations({
      setUserModel: 'user/setUserModel',
      setOperations: 'user/setOperations',
      setSidebarMenuItems: 'user/setSidebarMenuItems',
    }),

    handleUserDataFetched(userData) {
      const {
        error,
        userInfo,
        operations,
        sidebar,
        curService,
      } = userData;
      if (error) {
        if (error.errorCode === errorCode.ERROR_CODE_UNAUTHORIZED) {
          this.isForbidden = true;
        }
        return;
      }

      this.serviceName = curService.name;

      this.setUserModel(userInfo);

      this.setOperations(operations);

      this.sidebar = sidebar;

      this.sidebarMenuUrlMapper = this.makeSidebarMenuUrlMapper(sidebar);
      const sidebarMenuItems = getSidebarMenuItemList(sidebar);

      this.setSidebarMenuItems(sidebarMenuItems);
      // 因为该应用不存在应用，如果进行时为首页，即跳转至第一个菜单
      if ([null, 'root'].includes(this.$route.name)) {
        this.$nextTick(() => {
          this.redirectToFirstSidebarItem();
        });
      } else {
        const curRouteMenuCode = this.$route.meta.sidebarSelectedKeys;
        const index = sidebarMenuItems.findIndex((i) => curRouteMenuCode.includes(i.key));
        this.isForbidden = index === -1;
      }
    },

    getAuthorization() {

    },
    handleClickServiceName() {
      this.redirectToFirstSidebarItem();
    },
    handleLogout() {
      const location = `${config.publicPath}logout?service=${encodeURIComponent(window.location.href)}`;
      window.location.href = location;
    },

    makeSidebarMenuUrlMapper(menuItemList) {
      const mapper = {};
      if (menuItemList && Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          mapper[item.key] = item.link;
          let childrenMapper = false;
          if (item.children && Array.isArray(item.children)) {
            childrenMapper = this.makeSidebarMenuUrlMapper(item.children);
          }
          if (childrenMapper !== false) {
            Object.assign(mapper, childrenMapper);
          }
        });
      }
      return mapper;
    },

    handleSidebarMenuSelect({ key, target }) {
      if (this.sidebarMenuUrlMapper[key]) {
        this.handleSelectSidebarItem({ url: this.sidebarMenuUrlMapper[key], target });
      }
      this.sidebarSelectedKeys = [key];
      // 重置菜单列表
      this.$eventBus.$emit('reset-table');
    },

    handleSelectSidebarItem({ url, target }) {
      if (target === '_blank') {
        window.open(url, target);
      } else {
        this.isForbidden = false;
        this.showView = false;
        this.$nextTick(() => {
          this.showView = true;
        });
        this.$router.push(url);
      }
    },
    handleApiError(err) {
      // 目前接口有两种表示身份过期方式
      // 一种是通过状态码表示
      // 一种是通过响应内容错误码表示
      const doLogout = (cb) => {
        const redirect = (currCB) => {
          if (currCB) {
            currCB();
          }
          const location = window.location.href;
          window.location.href = location;
        };
        this.clearUserCookies().then(() => {
          redirect(cb);
        }).catch(() => {
          redirect(cb);
        });
      };
      if ((err.response && err.response.status === 401)
        || (err.code && err.code === errorCode.ERROR_CODE_ACCESS_CHECK)) {
        if (!this.hasIdentifierTips) {
          const self = this;
          this.hasIdentifierTips = true;
          this.$error({
            title: '身份验证会话已过期，请再次登录。',
            okText: '确定',
            onOk() {
              doLogout(() => {
                self.hasIdentifierTips = false;
              });
            },
          });
        }
      } else if (err.message) {
        this.$message.error(err.message);
        // this.$error({
        //   title: err.message,
        //   class: 'pima-confrim pima-confrim-error',
        // });
      }
    },
    handleSidebarSelect(sidebarOpenKeys, sidebarSelectedKeys) {
      this.sidebarOpenKeys = sidebarOpenKeys || [];
      this.sidebarSelectedKeys = sidebarSelectedKeys || [];
    },
    handleRefreshUserData() {
      this.$refs.pimaAppHeader.refreshUserData();
    },
    redirectToFirstSidebarItem() {
      if (this.sidebarMenuItems.length > 0) {
        this.$router.replace(this.sidebarMenuItems[0].link);
      }
    },
  },
};
</script>

<style lang="less" scoped>

</style>
