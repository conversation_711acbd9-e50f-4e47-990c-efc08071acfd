/* eslint-disable max-len */
<template>
  <div class="wmr-editor">
    <VueUeditorWrap
      :config="localeServiceurl"
      v-bind="$attrs"
      v-on="$listeners"
      @before-init="onBeforeInit"
      @ready="onReady"
    />
  </div>
</template>

<script>
/* eslint-disable no-plusplus */
/* eslint-disable no-cond-assign */
import VueUeditorWrap from 'vue-ueditor-wrap';
import config from '../../server/config';

export default {
  name: 'WmEditor',
  components: {
    VueUeditorWrap,
  },

  props: {
    height: {
      type: Number,
      default: 450,
    },
    localeServiceurl: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      config,
      ueditorConfigs: {},
    };
  },

  computed: {
    createConfig() {
      const severConfig = {
        // serverUrl: this.localeServiceurl,
        // eslint-disable-next-line max-len
        serverUrl: '/hunicorn-pimax-bdc/information/x-bdc-dfs-api/attachments/editor/upload?Authorization=bearer+57f10d77ba6f888e62f48549a19994c5',
        UEDITOR_HOME_URL: `${config.publicPath}static/UEditor/`,
        // UEDITOR_HOME_URL: '/bdc-client/static/UEditor/',
        // 配置说明文档 https://fex.baidu.com/ueditor/#start-config
        autoHeightEnabled: true,
        initialFrameHeight: this.height,
        // scaleEnabled: true,
        initialFrameWidth: '100%',
        enableAutoSave: false,
        enablePasteUpload: true,
        toolbars: [
          [
            'fullscreen',
            'source',
            '|',
            'undo',
            'redo',
            '|',
            'bold',
            'italic',
            'strikethrough',
            'pasteplain',
            '|',
            'fontsize',
            'forecolor',
            'backcolor',
            'insertorderedlist',
            'insertunorderedlist',
            '|',
            'justifyleft',
            'justifycenter',
            'justifyright',
            '|',
            'link',
            'unlink',
            '|',
            'simpleupload',
            'insertimage',
            'insertword',
            '|',
            'inserttable',
            'horizontal',
          ],
        ],
        wordCount: false,
      };
      return severConfig;
    },
  },
  watch: {
  },
  updated() {
    // this.ueditorConfigs = this.createConfig();
  },
  mounted() {
    console.log('a1', config.publicPath);
  },
  methods: {
    onBeforeInit() {
      // window.UE.registerUI(
      //   'insertword',
      //   function insertWordPlugin(editor, uiName) {
      //     const me = this;
      //     // eslint-disable-next-line prefer-const
      //     let isLoaded = false;

      //     function initInsertWordBtn(containerBtn) {
      //       const w = containerBtn.offsetWidth || 20;
      //       const h = containerBtn.offsetHeight || 20;
      //       const btnIframe = document.createElement('iframe');
      //       // eslint-disable-next-line max-len
      //       const btnStyle = `display:block;width:${w}px;height:${h}px;overflow:hidden;border:0;margin:0;padding:0;position:absolute;top:0;left:0;filter:alpha(opacity=0);-moz-opacity:0;-khtml-opacity: 0;opacity: 0;cursor:pointer;`;

      //       window.UE.dom.domUtils.on(btnIframe, 'load', () => {
      //         const timestrap = (+new Date()).toString(36);
      //         // let wrapper;
      //         // let btnIframeDoc;
      //         // let btnIframeBody;

      //         const btnIframeDoc = btnIframe.contentDocument || btnIframe.contentWindow.document;
      //         const btnIframeBody = btnIframeDoc.body;
      //         const wrapper = btnIframeDoc.createElement('div');

      //         // eslint-disable-next-line operator-linebreak
      //         wrapper.innerHTML = `<input
      //           id="edui_input_${timestrap}"
      //           type="file"
      //           accept=".docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      //           name="${me.options.imageFieldName}"
      //           style="${btnStyle}"
      //         >
      //         <iframe
      //           id="edui_iframe_${timestrap}"
      //           name="edui_iframe_${timestrap}"
      //           style="display:none;width:0;height:0;border:0;margin:0;padding:0;position:absolute;"
      //         ></iframe>`;

      //         wrapper.className = `edui-${me.options.theme}`;
      //         wrapper.id = `${me.ui.id}_iframeupload`;
      //         btnIframeBody.style.cssText = btnStyle;
      //         btnIframeBody.style.width = `${w}px`;
      //         btnIframeBody.style.height = `${h}px`;
      //         btnIframeBody.appendChild(wrapper);

      //         if (btnIframeBody.parentNode) {
      //           btnIframeBody.parentNode.style.width = `${w}px`;
      //           btnIframeBody.parentNode.style.height = `${w}px`;
      //         }

      //         // const form = btnIframeDoc.getElementById(`edui_form_${timestrap}`);
      //         const input = btnIframeDoc.getElementById(
      //           `edui_input_${timestrap}`,
      //         );
      //         // const iframe = btnIframeDoc.getElementById(`edui_iframe_${timestrap}`);
      //         function readFileInputEventAsArrayBuffer(event, callback) {
      //           const reader = new FileReader();

      //           reader.onload = function onLoad(loadEvent) {
      //             const arrayBuffer = loadEvent.target.result;
      //             callback(arrayBuffer);
      //           };

      //           // reader.readAsArrayBuffer(file);
      //         }

      //         function handleFileSelect(event) {
      //           const reader = new FileReader();
      //           reader.onload = function onLoad(e) {
      //             reader.readAsText(e);
      //           };

      //           readFileInputEventAsArrayBuffer(event, (arrayBuffer) => {
      //             mammoth
      //               .convertToHtml({ arrayBuffer })
      //               .then((data) => {
      //                 const el = document.createElement('div');
      //                 el.innerHTML = unescape(data.value);

      //                 // const imgEls = el.getElementsByTagName('img');

      //                 me.execCommand('inserthtml', unescape(data.value));
      //               })
      //               .done();
      //           });
      //         }

      //         window.UE.dom.domUtils.on(input, 'change', (event) => {
      //           if (!input.value) return;
      //           handleFileSelect(event);
      //         });

      //         let stateTimer;
      //         me.addListener('selectionchange', () => {
      //           clearTimeout(stateTimer);
      //           stateTimer = setTimeout(() => {
      //             const state = me.queryCommandState('insertword');
      //             if (state === -1) {
      //               input.disabled = 'disabled';
      //             } else {
      //               input.disabled = false;
      //             }
      //           }, 400);
      //         });
      //         isLoaded = true;
      //       });

      //       btnIframe.style.cssText = btnStyle;
      //       containerBtn.appendChild(btnIframe);
      //     }

      //     // 注册按钮执行时的command命令,用uiName作为command名字，使用命令默认就会带有回退操作
      //     editor.registerCommand(uiName, {
      //       execCommand() {
      //         // console.log('@@value', value);
      //         // 这里借用fontsize的命令
      //         // this.execCommand('test', `${value}px`);
      //       },

      //       queryCommandState() {
      //         return isLoaded ? 0 : -1;
      //       },
      //     });

      //     // 创建下拉菜单中的键值对，这里我用字体大小作为例子
      //     // 创建下来框
      //     const button = new window.UE.ui.Button({
      //       // 需要指定当前的编辑器实例
      //       editor,
      //       // 提示
      //       title: uiName,
      //       name: uiName,
      //       cssRules: 'background-position: -300px -40px',
      //       onclick() {
      //         // 这里可以不用执行命令,做你自己的操作也可
      //         editor.execCommand(uiName);
      //       },
      //     });

      //     editor.addListener('ready', () => {
      //       // editor.fireEvent('insertwordready');
      //       const buttonBody = button.getDom('body');
      //       const iconSpan = buttonBody.children[0];
      //       // editor.fireEvent("simpleuploadbtnready", iconSpan);
      //       initInsertWordBtn(iconSpan);
      //       // console.log('@@editor', editor);
      //       // editor.afterConfigReady(initInsertWordBtn);
      //     });

      //     editor.addListener('selectionchange', (type, causeByUi, uiReady) => {
      //       if (!uiReady) {
      //         const state = editor.queryCommandState(uiName);
      //         if (state === -1) {
      //           button.setDisabled(true);
      //         } else {
      //           button.setDisabled(false);
      //           // let value = editor.queryCommandValue(uiName);
      //           // if (!value) {
      //           //   button.setValue(uiName);
      //           //   return;
      //           // }
      //           // // ie下从源码模式切换回来时，字体会带单引号，而且会有逗号
      //           // value && (value = value.replace(/['"]/g, '').split(',')[0]);
      //           // button.setValue(value);
      //         }
      //       }
      //     });

      //     return button;
      //   },
      //   23, /* index 指定添加到工具栏上的那个位置，默认时追加到最后,editorId 指定这个UI是那个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮 */
      // );

      this.$emit('before-init');
    },
    onReady(editorInstance) {
      // 调整zIndex
      // iView抽屉的zIndex为1000，需比这个值大
      // eslint-disable-next-line no-param-reassign
      editorInstance.container.style.zIndex = 1001;

      this.$emit('ready', editorInstance);
    },
  },
};
</script>

<style lang="less" scoped>
.wmr-editor {
  position: relative;
  z-index: 1;
  .hint {
    position: absolute;
    bottom: -4px;
    right: 5px;
    z-index: 1000;
  }

  ::v-deep {
    .edui-editor-breadcrumb {
      visibility: hidden;
    }

    .edui-default .edui-toolbar {
      line-height: 20px;
    }
  }
  ::v-deep .edui-default .edui-editor-messageholder {
    width: 0px;
  }
}
</style>
