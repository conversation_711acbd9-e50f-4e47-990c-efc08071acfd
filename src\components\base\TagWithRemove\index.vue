<template>
  <span
    :class="`tag-with-remove ${showRemove ? '' : 'hidden-remove'}`"
  >
    {{ title }}
    <span
      v-if="showRemove"
      class="remove-wrap"
      @click="$emit('close', propItem)"
    >
      <span
        v-if="!$slots.removeIcon"
        class="remove-icon"
      />
      <slot name="removeIcon" />
    </span>
  </span>
</template>

<script>
export default {
  name: 'TagWithRemove',
  props: {
    title: {
      type: String,
      default: 'Tag',
    },
    propItem: {
      type: [String, Object],
      default: () => {},
    },
    showRemove: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
.tag-with-remove {
  display: inline-block;
  height: 26px;
  line-height: 26px;
  padding-left: 8px;
  margin-right: 13px;
  margin-bottom: 8px;
  border-radius: 2px;
  border: 1px solid #E4E4E4;
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
  vertical-align: middle;

  &.hidden-remove {
    padding-right: 13px;
    .remove-wrap {
      display: none;
    }
  }

  &:hover .remove-wrap{
    background-color: #F5F5F5;
  }

  .remove-wrap {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-color: transparent;
    cursor: pointer;
    overflow: hidden;
    vertical-align: top;
    text-align: center;
    line-height: 24px;
    transition: background-color 200ms ease;
  }
  .remove-icon {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-image: url("./close-tag-icon.svg");
    background-repeat: no-repeat;
    background-position: center center;
  }
}
</style>
