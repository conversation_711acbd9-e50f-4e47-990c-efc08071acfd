import URI from 'urijs';

const publicPath = process.env.PUBLIC_PATH || '/';
const LOCAL_APP_API_BASE_URL = `${publicPath}x-venue-reservation-api`;
const LOCAL_BDC_CORE_API_BASE_URL = `${publicPath}x-bdc-core-api`;
const LOCAL_BDC_ARCH_API_BASE_URL = `${publicPath}x-bdc-arch-api`;
const LOCAL_BDC_AUTH_API_BASE_URL = `${publicPath}x-bdc-auth-api`;
const LOCAL_BDC_DFS_API_BASE_URL = `${publicPath}x-bdc-dfs-api`;
const LOCAL_UPLOAD_IMAGE_API_URL = `${LOCAL_BDC_DFS_API_BASE_URL}/attachments/editor/upload`;
const LOCAL_BDC_MSG_API_BASE_URL = `${publicPath}x-bdc-msg-api`;
const LOCAL_BDC_PKUSZ_API_BASE_URL = `${publicPath}x-bdc-pkusz-api`;
const LOCAL_BDC_PKUSZ_API_BASE_URL_EN = `${publicPath}x-bdc-pkuszen-api`;
const LOCAL_SERVICE_API_BASE_URL = `${publicPath}x-bdc-service-api`;
const LOCAL_BDC_IMPORT_API_BASE_URL = `${publicPath}x-bdc-import-api`;
const expressPort = process.env.EXPRESS_PORT || 8080;
const localhostUrl = (expressPort === 80) ? 'http://localhost' : `http://localhost:${expressPort}`;
const serviceUrlString = process.env.SERVICE_URL || localhostUrl;
const serviceUrl = new URI(serviceUrlString);
const cookieSecure = (serviceUrl.protocol() === 'https');
const cookieSameSite = cookieSecure ? 'strict' : 'lax';


// 静态目录在生产环境下的缓存maxAge
const STATIC_MAX_AGE = 365 * 24 * 60 * 60 * 1000; // 单位为ms，共1年;

const config = {
  // Express配置
  expressPort,
  serviceUrl: serviceUrlString,
  // Redis配置
  redisTTL: process.env.REDIS_TTL ? parseInt(process.env.REDIS_TTL, 10) : (60 * 60 * 2),
  redisHost: process.env.REDIS_HOST || 'localhost',
  redisPort: process.env.REDIS_PORT,
  redisDB: process.env.REDIS_DB,
  redisPassword: process.env.REDIS_PASSWORD,
  redisPrefix: process.env.REDIS_PREFIX,
  // Session配置
  sessionSecret: process.env.SESSION_SECRET || 'DOOCOM-PIMA',
  sessionIdCookieName: process.env.SESSION_ID_COOKIE_NAME || '__sid',
  // Cas中间件配置
  casServiceBaseUrl: process.env.CAS_SERVICE_BASE_URL,
  casServiceVersion: process.env.CAS_SERVICE_VERSION,
  bdcCoreApiBaseUrl: process.env.BDC_CORE_API_BASE_URL,
  pkuszBaseUrl: process.env.PKUSZ_API_BASE_URL,
  pkuszBaseUrlEn: process.env.PKUSZ_API_BASE_URL_EN,
  pkuszBasePath: process.env.PKUSZ_BASE_PATH,
  loginStatusLoggedInKey: 'appLoggedIn',
  loginStatusUsernameKey: 'appUsername',
  cookieSecure,
  cookieSameSite,
  // Token配置
  accessTokenCookieName: process.env.ACCESS_TOKEN_COOKIE_NAME || '__at',
  // Webpack配置
  publicPath,
  bundleAnalyzerReport: process.env.npm_config_report || false,
  // u-editor上传图片接口
  LOCAL_UPLOAD_IMAGE_API_URL,
  // 代理中间件配置
  proxyTable: {
    [LOCAL_APP_API_BASE_URL]: {
      target: process.env.APP_API_BASE_URL,
      pathRewrite: {
        [LOCAL_APP_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_CORE_API_BASE_URL]: {
      target: process.env.BDC_CORE_API_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_CORE_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_ARCH_API_BASE_URL]: {
      target: process.env.ARCH_API_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_ARCH_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_AUTH_API_BASE_URL]: {
      target: process.env.AUTH_API_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_AUTH_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_DFS_API_BASE_URL]: {
      target: process.env.PIMA_UPLOAD_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_DFS_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_MSG_API_BASE_URL]: {
      target: process.env.BDC_MSG_API_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_MSG_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_PKUSZ_API_BASE_URL]: {
      target: process.env.PKUSZ_API_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_PKUSZ_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_PKUSZ_API_BASE_URL_EN]: {
      target: process.env.PKUSZ_API_BASE_URL_EN,
      pathRewrite: {
        [LOCAL_BDC_PKUSZ_API_BASE_URL_EN]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_BDC_IMPORT_API_BASE_URL]: {
      target: process.env.IMPORT_API_BASE_URL,
      pathRewrite: {
        [LOCAL_BDC_IMPORT_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
    [LOCAL_SERVICE_API_BASE_URL]: {
      target: process.env.SERVICE_API_BASE_URL,
      pathRewrite: {
        [LOCAL_SERVICE_API_BASE_URL]: '',
      },
      changeOrigin: true,
      xfwd: true,
      logLevel: 'debug',
    },
  },
  // 业务配置
  clientId: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  serviceCode: process.env.SERVICE_CODE,
  serviceType: process.env.SERVICE_TYPE,
  serviceName: process.env.SERVICE_NAME,
  wxCodeUrl: process.env.WX_CODE_URL,
  wxUserInfoUrl: process.env.WX_USER_INFO_URL,
  wxAgentId: process.env.WX_AGENT_ID,
  wxAgentKey: process.env.WX_AGENT_KEY,
  wxSignature: process.env.WX_SIGNATURE,
  locale: 'zh-CN',
  localeCookieName: process.env.LOCALE_COOKIE_NAME || '__locale',
  hostProtocol: process.env.HOST_PROTOCOL || 'http',
  supportLocales: process.env.SUPPORT_LOCALES ? process.env.SUPPORT_LOCALES.split(',') : ['zh-CN', 'zh-HK', 'en-US'],
  fallbackLocale: process.env.FALLBACK_LOCALE,
  faviconUrl: process.env.FAVICON_URL || `${process.env.PUBLIC_PATH || '/'}static/favicon.ico`,
  logoUrl: process.env.LOGO_URL || `${process.env.PUBLIC_PATH || '/'}static/logo.png`,
  staticResourcesUrl: process.env.STATIC_RESOURCES_URL || process.env.PUBLIC_PATH || '/',
  userPreVerifyUrl: process.env.USER_PRE_VERIFY_URL || '/',
  img404Url: process.env.NOT_FOUND_URL || `${process.env.PUBLIC_PATH || '/'}static/404.png`,
  img500Url: process.env.SERVER_ERROR_URL || `${process.env.PUBLIC_PATH || '/'}static/500.png`,
  errorPage404: process.env.ERROR_PAGE_404,
  errorPage500: process.env.ERROR_PAGE_500,
  staticMaxAge: STATIC_MAX_AGE,
};

export default config;
