import httpRequest from './request';


// 获取场馆列表
export async function getVenueList(params) {
  const res = await httpRequest({
    url: 'venues',
    method: 'get',
    params,
  });
  return res.data;
}

// 获取场馆详情
export async function getVenueDetail(id) {
  const res = await httpRequest({
    url: `venues/${id}`,
    method: 'get',
  });
  return res.data.model;
}

// 修改场馆状态
export async function updateVenueStatus(id, params) {
  await httpRequest({
    url: `venues/${id}/set-enable`,
    method: 'post',
    params,
  });
}

// 获取场馆时间设置详情
export async function getVenueTimePeriodList(id) {
  const res = await httpRequest({
    url: `venues/${id}/times`,
    method: 'get',
  });
  return res.data.model;
}

// 修改场馆时间设置状态
export async function updateVenueTimePeriodStatus(id, params) {
  await httpRequest({
    url: `venues/${id}/times/week/set-enable`,
    method: 'post',
    params,
  });
}

// 开放预约
export async function openVenueAppointmentPeriod(id, data) {
  await httpRequest({
    url: `venues/${id}/times/set-enable`,
    method: 'post',
    params: {
      enable: 1,
    },
    data,
  });
}

// 不开放预约
export async function closeVenueAppointmentPeriod(id, data) {
  await httpRequest({
    url: `venues/${id}/times/set-enable`,
    method: 'post',
    params: {
      enable: 0,
    },
    data,
  });
}

// 固定场馆
export async function lockVenueAppointmentPeriod(id, params, data) {
  await httpRequest({
    url: `venues/${id}/times/set-fixed`,
    method: 'post',
    params,
    data,
  });
}
