<template>
  <a-table
    :data-source="dataSource"
    :loading="isLoading"
    :columns="columns"
    :row-key="record => record.reservationRuleTimeId"
    :pagination="false"
    bordered
    class="time-setting-table"
  >
    <template
      slot="period"
      slot-scope="text, record"
    >
      <div class="start-time">
        {{ record.startTime | hourTrimLeft }}
      </div>
      <div class="end-time end-time1">
        ~
      </div>
      <div class="end-time end-time2">
        {{ record.endTime | hourTrimLeft }}
      </div>
    </template>

    <template
      slot="enable"
      slot-scope="text, record"
    >
      <div class="white-border">
        <a-switch
          v-model="record.isEnable"
          :loading="getIsSavingCurrentItem(record)"
          @change="(checked, event) => onPeriodStatusChange(checked, event, record)"
        />
      </div>
    </template>

    <template
      slot="monday"
      slot-scope="text"
    >
      <!-- text为当天的数据 -->
      <CellPeriod
        :value="text"
      />
    </template>
  </a-table>
</template>


<script>
import { mapState } from 'vuex';
import OpreationMixin from '@/mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';
import CellPeriod from './cell-period.vue';
import { createTableColumns } from './table-columns';
import { getDayByColumnIndex } from './utils';

export default {
  name: 'TimeSettingTableList',

  components: {
    CellPeriod,
  },

  filters: {
    hourTrimLeft(value) {
      if (typeof value === 'string' && value.indexOf(':') !== -1) {
        const [hour, minute] = value.split(':');
        return `${parseInt(hour, 10)}:${minute}`;
      }

      return value;
    },
  },

  mixins: [
    OpreationMixin,
    nsI18n('t', 'venueTimeSetting.timeSetting.tableList'),
  ],

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },

    isLoading: {
      type: Boolean,
      default: true,
    },

    venueId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      detail: {
        show: false,
        id: null,
      },
    };
  },

  computed: {
    ...mapState({
      savingTimePeriodIds: (state) => state.venueTimeSetting.savingTimePeriodIds,
    }),

    columns() {
      return createTableColumns(this);
    },
  },
  methods: {
    getIsSavingCurrentItem(record) {
      const { reservationRuleTimeId } = record;
      return this.savingTimePeriodIds.includes(reservationRuleTimeId);
    },

    onPeriodStatusChange(checked, event, record) {
      const { reservationRuleTimeId } = record;
      const payload = { id: this.venueId, reservationRuleTimeId, isEnable: checked };
      this.$store.dispatch('venueTimeSetting/updateVenueTimePeriodStatus', payload)
        .then()
        .catch(
          (err) => {
            this.$message.error(err.response.data.errorMsg);
            // 重新获取数据,还原switch状态
            this.$parent.loadData();
          },
        );
    },

    // 在columns里定义
    onClickCell(record, rowIndex, columnIndex) {
      const today = getDayByColumnIndex(columnIndex);
      const { reservationRuleTimeId } = record;
      const payload = { reservationRuleTimeId, checkDay: today };
      this.$store.dispatch('venueTimeSetting/toggleVenueTimePeriodDay', payload);
    },
  },
};
</script>


<style lang="less" scoped>
.time-setting-table {
  margin-top: 32px;
  border: 0;

  ::v-deep .ant-table-body {
    table {
      border: none;
    }
  }

  ::v-deep .ant-table-thead {
    tr {
      th {
        height: 50px;
        background: #f8f8f8;
        border-bottom-width: 2px;
        border-bottom-color: #ededed;
        border-right-color: #ededed;

        &.th-period-title-wrap {
          position: relative;
          color: fadeout(#000, 15%);
          font-size: 16px;
          font-weight: 500;

          &.th-column-first {
            border-right-color: #ededed;
          }
        }
        &.bg-hover {
          border-bottom: 2px solid #8D0306 !important;
        }
        .ant-table-column-title {
          font-size: 16px;
        }
      }
    }
  }

  ::v-deep .ant-table-tbody {
    tr {
      td {
        padding: 0;
        height: 66px;

        &:hover {
          .cell-period {
            border: 1px solid #8D0306;
          }
        }

        &.td-period-title-wrap {
          background: #f8f8f8;
          border-right-color: #ededed;
          border-bottom-color: #ededed;
        }

        &.td-clickable {
          position: relative;
          cursor: pointer;
        }

        &.td-status-opened {
          // background-color: fadeout(#179e00, 90%);
        }

        &.td-status-closed {
          background-color: #f7f7f7;
        }

        &.td-status-locked {
          background-color: #f4e6e7;
        }

        &.td-checked {
          &.td-status-opened {
            // background-color: fadeout(#179e00, 90%);

            // .cell-period {
            //   border: 1px solid #179e00;
            // }
          }

          &.td-status-closed {
            background-color: #f7f7f7;

            // .cell-period {
            //   border: 1px solid gray;
            // }
          }

          &.td-status-locked {
            background-color: #f4e6e7;

            // .cell-period {
            //   border: 1px solid #8d0306;
            // }
          }
          .cell-period {
            border: 1px solid #8d0306;
              .selected-icon {
                visibility: visible;
              }
            }
          }

        &.td-enable {
          // background-color: #fff;
        }


        .start-time {
          color: fadeout(#000, 35%);
          font-size: 16px;
          font-weight: bold;
        }

        .end-time {
          color: fadeout(#000, 55%);
          font-size: 12px;
          font-weight: bold;
          margin-top: -2px;

          &.end-time1 {
            margin-top: -4px;
          }
        }
      }
      td:first-child {
        border-right-width: 2px;
      }
      .white-border {
        width: 100%;
        height: 100%;
        padding-top: 20px;
        border: 2px solid #fff;
      }
    }
    .bg-hover {
      background-color: #F8F8F8 !important;
    }
    .ant-table-row-hover td:first-child {
      border-right: 2px solid #8D0306;
    }
  }
}
</style>
