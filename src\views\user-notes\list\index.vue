<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.UserNotes.View)"
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <SimpleSearch
          ref="SimpleSearch"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
        />

        <template
          #extra
        >
          <!--基础数据头部-->
          <a-button
            v-if="hasP(P => P.UserNotes.AnnouncePublish)"
            type="primary"
            size="small"
            @click="handleAnnounceCreate()"
          >
            {{ $t('userNotes.title.publishAnnounce') }}
          </a-button>
          <a-button
            v-if="hasP(P => P.UserNotes.Add)"
            type="primary"
            size="small"
            @click="handleCreat()"
          >
            {{ $t('userNotes.title.add') }}
          </a-button>
        </template>
      </a-page-header>

      <div class="panel-body">
        <!--基础数据查看-->
        <TableList
          @edit-notes="onEditNotes"
          @delete-notes="onDeleteNotes"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { hasOwn } from '@utils/core';
import operation from '@mixins/operation';
import {
  delNotesApi,
} from '@/api/user-notes-api';
import SimpleSearch from './components/simple-search.vue';
import TableList from './components/table-list.vue';

export default {
  name: 'UserNotes',

  components: {
    TableList,
    SimpleSearch,
  },

  mixins: [operation],

  data() {
    return {
      filter: {
        keyword: '',
        status: '',
      },
      record: {},
    };
  },

  mounted() {
    // 重置菜单列表
    this.$eventBus.$on('reset-table', this.resetTable);

    this.fetchList();
  },

  methods: {
    resetTable() {
      // 重新获取基础数据列表
      if (this.$refs.SimpleSearch) {
        this.$refs.SimpleSearch.resetSearch();
      }
    },
    ...mapActions({
      fetchNotesListApi: 'userNotes/fetchNotesListApi',
      fetchUserList: 'uniData/fetchUserList',
    }),
    resetSearch(filter) {
      this.fetchNotesListApi(filter);
    },
    handleSearch(filter) {
      this.fetchNotesListApi(filter);
    },
    fetchList() {
      const urlQuery = this.$route.query;
      Object.keys(urlQuery).forEach((k) => {
        if (hasOwn(this.filter, k)) this.filter[k] = urlQuery[k];
      });
      this.fetchNotesListApi({
        page: urlQuery.page || 1,
        pageSize: urlQuery.pageSize || 10,
        filter: {
          ...this.filter,
        },
      });
    },
    // 监听编辑事件
    onEditNotes(row) {
      const { id } = row;
      this.$router.push({
        name: 'userNotesCreat',
        query: {
          id,
        },
      });
    },
    // 监听打开删除modal
    onDeleteNotes(record) {
      const self = this;
      this.$confirm({
        title: this.$t('userNotes.msg.delTip'),
        class: 'pima-confrim',
        onOk() {
          delNotesApi({
            id: record.id,
          })
            .then(() => {
              self.$message.success(self.$t('userNotes.msg.delSucc'));
              self.fetchList();
            });
        },
      });
    },
    handleCreat() {
      this.$router.push({
        name: 'userNotesCreat',
      });
    },
    handleAnnounceCreate() {
      this.$router.push({
        name: 'userAnnounceCreat',
      });
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-heading-title {
  display: flex;
  align-items: center;
}
::v-deep .ant-page-header-content {
  position: absolute;
  top: 7px;
  padding-top: 0;
  overflow: visible;
}
</style>
