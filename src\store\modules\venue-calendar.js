/* eslint-disable arrow-body-style */
import _ from 'lodash';
import {
  getUserInfo,
  fetchUsers,
} from '@/api/arch-api';
import {
  getVenueCalendar,
  reserveVenue,
  updateReserveInfo,
} from '@/api/venue-calendar';
import { getReservationDetailApi } from '@api/reserve-query-api';
// import { createImportSpecifier } from 'typescript';

const initialState = {
  periods: [],
  venues: [],
  isLoadingCalendar: false,
  currentUserInfo: null,
  isLoadingCurrentUserInfo: false,
  reserveInfo: null,
  userDepartments: [],
  isLoadingReserveInfo: false,
  isSubmitingReserve: false,
  users: [], // 获取教职工
};

const getters = {};

const mutations = {
  setIsLoadingCalendar(state, payload) {
    state.isLoadingCalendar = payload;
  },

  setPeriods(state, payload) {
    state.periods = payload;
  },

  setVenues(state, payload) {
    state.venues = payload;
  },

  setIsLoadingReserveInfo(state, payload) {
    state.isLoadingReserveInfo = payload;
  },

  setIsLoadingCurrentUserInfo(state, payload) {
    state.isLoadingCurrentUserInfo = payload;
  },

  setCurrentUserInfo(state, payload) {
    state.currentUserInfo = payload;
  },

  setReserveInfo(state, payload) {
    state.reserveInfo = payload;
  },

  setUserDepartments(state, payload) {
    state.userDepartments = payload;
  },

  setIsSubmitingReserve(state, payload) {
    state.isSubmitingReserve = payload;
  },

  setUsers(state, payload) {
    state.users = payload;
  },
  clearUsers(state) {
    state.users = [];
  },
};

const actions = {
  // 获取人个信息
  async fetchCurrentUserInfo({ commit, state, rootState }) {
    if (state.currentUserInfo) {
      return;
    }

    const userId = _.get(rootState, 'user.userModel.id', null);
    if (!userId) {
      return;
    }

    commit('setIsLoadingCurrentUserInfo', true);
    try {
      const res = await getUserInfo(rootState.user.userModel.id);
      const mainDepts = _.get(res, 'model.mainDeptIds', []);
      const depts = _.get(res, 'model.deptIds', []);
      let combination = mainDepts.concat(depts);
      combination = _.uniqBy(combination, 'id');

      commit('setCurrentUserInfo', res.model);
      commit('setUserDepartments', combination);
    } finally {
      commit('setIsLoadingCurrentUserInfo', false);
    }
  },

  // 获取场馆日历
  async fetchVenueCalendar({ commit }, payload) {
    commit('setIsLoadingCalendar', true);
    try {
      const params = payload;
      const res = await getVenueCalendar(params);
      const { timePairList, venueList } = res || {};
      const periods = (timePairList || []).map((item, index) => {
        return {
          ...item,
          id: index,
        };
      });
      commit('setPeriods', periods);
      commit('setVenues', venueList || []);
    } finally {
      commit('setIsLoadingCalendar', false);
    }
  },

  // 提交预约
  async reserveVenue({ commit }, payload) {
    commit('setIsSubmitingReserve', true);
    try {
      const data = payload;
      await reserveVenue(data);
    } finally {
      commit('setIsSubmitingReserve', false);
    }
  },
  // 修改预约
  async updateReserveInfo({ commit }, payload) {
    commit('setIsSubmitingReserve', true);
    try {
      const data = payload;
      await updateReserveInfo(data);
    } finally {
      commit('setIsSubmitingReserve', false);
    }
  },

  fetchReservationDetail({ commit }, id) {
    return new Promise((resolve, reject) => {
      getReservationDetailApi(id)
        .then((res) => {
          const list = [{
            id: res.model.userId,
            phone: res.model.userPhone,
            name: res.model.userName,

            userNo: res.model.userNo,
            mainDeptNames: res.model.deptName,
            deptId: res.model.deptId,
            mobile: res.model.userPhone,
            mainDeptIds: [
              {
                id: res.model.deptId,
              },
            ],
          }];
          commit('setUsers', list);
          resolve(res);
        })
        .catch(reject);
    });
  },

  fetchUserList({ commit }, payload) {
    const params = {
      limit: -1,
      ...payload,
    };
    return new Promise((resolve, reject) => {
      fetchUsers(params)
        .then((res) => {
          commit('setUsers', res.data);
          resolve(res);
        })
        .catch(reject);
    });
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  mutations,
  actions,
};
