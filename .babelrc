{"presets": [["@babel/preset-env", {"modules": false, "useBuiltIns": "usage", "corejs": 3}]], "env": {"test": {"plugins": ["babel-plugin-dynamic-import-node", "babel-plugin-transform-require-context"]}}, "plugins": [["import", {"libraryName": "ant-design-vue", "libraryDirectory": "es", "style": "css"}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-transform-runtime", "@babel/plugin-transform-modules-commonjs"], "comments": false}