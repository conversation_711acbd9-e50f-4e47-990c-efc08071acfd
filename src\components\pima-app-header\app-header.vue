<template>
  <div class="header">
    <div class="logo">
      <a
        href="javascript:;"
        @click="clickTitle"
      >
        <img
          v-if="logoUrl"
          class="img"
          :src="logoUrl"
        >
        <!-- <span class="logo-title">PIMA<span class="title-symbol">+</span></span> -->
        <!-- <span class="t">{{ title }}</span> -->
        <span class="t-nanyan">
          {{ i18n.t('pimaUI.appHeader.nanyanPortal') }}
        </span>
      </a>
    </div>
    <template v-if="!loading">
      <div
        v-if="hasMenu"
        class="menu"
      >
        <user-menu-pkusz
          v-if="isPkuszMenu"
          :menu="menu"
          :default-selected-keys="menuSelectedKeys"
          :application-key="menuApplicationKey"
          :active-menu="activeMenu"
          :has-msg-dot="hasMsgDot"
          @click-application="clickMenuApplication"
        />
        <user-menu
          v-else
          :menu="menu"
          :default-selected-keys="menuSelectedKeys"
          :application-key="menuApplicationKey"
          @click-application="clickMenuApplication"
        />
      </div>
      <div class="actions">
        <user-action
          :support-locales="supportLocales"
          :has-message-link="hasMessageLink"
          :message-badge-count="messageBadge"
          :user-name="userName"
          :user-account="userAccount"
          :user-avatar-url="userAvatarUrl"
          @locale="clickLocale"
          @message="clickMessage"
          @change-password="clickChangePassword"
          @logout="clickLogout"
        />
      </div>
    </template>
    <div
      v-else
      class="loading"
    >
      <div
        slot="indicator"
        type="loading"
        class="indicator"
        spin
      />
    </div>
  </div>
</template>

<script>
import { Locale } from '@/constants/locale';
import UserMenu from './app-header/user-menu.vue';
import UserAction from './app-header/user-action.vue';
import UserMenuPkusz from './app-header/user-menu-pkusz.vue';


export default {
  name: 'AppHeader',
  components: {
    // [Layout.Header.name]: Layout.Header,
    // [Spin.name]: Spin,
    // [Icon.name]: Icon,
    UserMenu,
    UserAction,
    UserMenuPkusz,
  },
  inject: ['i18n'],
  props: {
    // 语言
    locale: {
      type: String,
      default: Locale.zhCN,
    },

    hasMsgDot: {
      type: Boolean,
      default: false,
    },
    activeMenu: {
      type: String,
      default: '',
    },
    isPkuszMenu: {
      type: Boolean,
      default: false,
    },
    logoUrl: {
      type: String,
      default: null,
    },
    // 标题
    title: {
      type: String,
      default: null,
    },
    // 点击标题
    clickTitle: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 是否有主菜单
    hasMenu: {
      type: Boolean,
      default: true,
    },
    // 菜单
    menu: {
      type: Array,
      default() {
        return [];
      },
    },
    // 菜单默认选中项键值
    menuSelectedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 菜单应用对应键值
    menuApplicationKey: {
      type: String,
      default: null,
    },
    // 是否有消息链接
    hasMessageLink: {
      type: Boolean,
      default: true,
    },
    // 消息Badge
    messageBadge: {
      type: Number,
      default: 0,
    },
    // 用户名
    userName: {
      type: String,
      default: null,
    },
    // 账号
    userAccount: {
      type: String,
      default: null,
    },
    // 用户头像
    userAvatarUrl: {
      type: String,
      default: null,
    },
    // 点击菜单应用项
    clickMenuApplication: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 点击切换语言
    clickLocale: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 点击消息
    clickMessage: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 点击修改密码
    clickChangePassword: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 点击退出登录
    clickLogout: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 可切换语言
    supportLocales: {
      type: Array,
      default() {
        return ['zh-CN', 'zh-HK', 'en-US'];
      },
    },
  },
};
</script>

<style lang="less" scoped>
@headerHeight: 40px;
@logoWidth: 240px;
@logoImgHeight: 30px;
.header {
  height: @headerHeight;
  line-height: @headerHeight;
  background-color: #8d0405;
  padding: 0;
  display: flex;
  justify-content: flex-start;
  position: relative;
  z-index: 199;

  .clearfix {
    height: 100%;
  }

  .logo {
    width: @logoWidth;
    height: 100%;
    text-align: center;

    .img {
      /* width: 109px; */
      height: @logoImgHeight;
      display: block;
      margin: (@headerHeight - @logoImgHeight) / 2 0 0 15px;
    }

    .logo-title{
      line-height:1.5;
      text-decoration: none;
      font-size: 16px;
      color: #fff;
      position: absolute;
      left: 15px;
      top: 8px;
      .title-symbol{
        font-size: 14px;
        vertical-align: top;
      }
    }

    .t {
      position: absolute;
      left:75px;
      top:35px;
      line-height:1.5;
      text-decoration: none;
      font-size: 16px;
      color: #fff;
      :hover {
        color: #fff;
      }
    }
  }

  .menu {
    flex-grow: 1;
  }

  .loading {
    line-height: inherit;
    text-align: right;
    flex-grow: 1;
    padding-right: 40px;

    .indicator {
      color: #ffffff;
      font-size: 34px;
      vertical-align: middle;
    }
  }
}
.t-nanyan {
  position: absolute;
  left: 55px;
  top: 1px;
  line-height: 1.5;
  text-decoration: none;
  font-size: 12px;
  color: #fff;
  line-height: 40px;
}

</style>
