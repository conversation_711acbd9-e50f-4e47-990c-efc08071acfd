<template>
  <i
    class="icon"
    :class="className"
  />
</template>


<script>
import { PeriodType } from '@/constants/venue';


export default {
  name: 'PeriodTypeIcon',

  props: {
    value: {
      type: String,
      default() {
        return null;
      },
    },
    small: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    className() {
      const classList = [];

      const map = new Map([
        [PeriodType.CLOSED, 'icon-closed'],
        [PeriodType.OPENED, 'icon-opened'],
        [PeriodType.LOCKED, 'icon-locked'],
      ]);
      if (map.get(this.value)) {
        classList.push(map.get(this.value));
      }

      if (this.small) {
        classList.push('icon-small');
      }

      return classList;
    },
  },
};
</script>


<style lang="less" scoped>
.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: no-repeat center center / 100% auto;

  &.icon-closed {
    background-image: url("~@/assets/img/time-setting/close.png");
  }

  &.icon-opened {
    background-image: url("~@/assets/img/time-setting/open.png");
  }

  &.icon-locked {
    background-image: url("~@/assets/img/time-setting/fix.png");
  }

  &.icon-small {
    width: 20px;
    height: 20px;
  }
}
</style>
