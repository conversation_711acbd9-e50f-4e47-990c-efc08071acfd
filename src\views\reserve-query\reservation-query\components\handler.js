import {
  ReserveStatus, TimeRangeType, CancelType, IsStudentReservation,
} from '@/constants/venue';

// 预约状态
const ReserveStatusI18Map = {
  [ReserveStatus.PENDING]: 'reserveQuery.status.PENDING',
  [ReserveStatus.APPROVING]: 'reserveQuery.status.APPROVING',
  [ReserveStatus.PASS]: 'reserveQuery.status.PASS',
  [ReserveStatus.AUTO_PASS]: 'reserveQuery.status.AUTO_PASS',
  [ReserveStatus.PEDNING_SIGN]: 'reserveQuery.status.PEDNING_SIGN',
  [ReserveStatus.SIGNED]: 'reserveQuery.status.SIGNED',
  [ReserveStatus.REJECT]: 'reserveQuery.status.REJECT',
  [ReserveStatus.CANCEL]: 'reserveQuery.status.CANCEL',
  [ReserveStatus.OFF]: 'reserveQuery.status.OFF',
};

export function getreserveStatusI18Text(vm, progress) {
  return vm.$t(ReserveStatusI18Map[progress]);
}

export function getreserveStatusI18Options(vm, withAll = false) {
  const opts = Object.entries(ReserveStatusI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      {
        key: 'all',
        value: '',
        title: vm.$t('common.all'),
        label: vm.$t('common.all'),
      },
      ...opts,
    ]
    : [...opts];
}

// 时间范围
const TimeRangeTypeI18Map = {
  [TimeRangeType.CREATETIME]: 'reserveQuery.timeRangeType.CREATETIME',
  [TimeRangeType.APPROVALTIME]: 'reserveQuery.timeRangeType.APPROVALTIME',
};

export function getTimeRangeTypeI18Text(vm, progress) {
  return vm.$t(TimeRangeTypeI18Map[progress]);
}

export function getTimeRangeTypeI18Options(vm, withAll = false) {
  const opts = Object.entries(TimeRangeTypeI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      // {
      //   key: 'all',
      //   value: '',
      //   title: vm.$t('common.all'),
      //   label: vm.$t('common.all'),
      // },
      ...opts,
    ]
    : [...opts];
}

// 取消类型
const CancelTypeI18Map = {
  [CancelType.OFF]: 'reserveQuery.cancelType.OFF',
  [CancelType.OTHER]: 'reserveQuery.cancelType.OTHER',
};
export function getCancelTypeI18Text(vm, progress) {
  return vm.$t(CancelTypeI18Map[progress]);
}
// 是否学生组织预约
const IsStudentReservationI18nMap = {
  [IsStudentReservation.TRUE]: 'reserveQuery.isStudentReservationStatus.true',
  [IsStudentReservation.FALSE]: 'reserveQuery.isStudentReservationStatus.false',
};

export function getIsStudentReservationText(vm, progress) {
  return vm.$t(IsStudentReservationI18nMap[progress]);
}
