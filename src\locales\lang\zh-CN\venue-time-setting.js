const venueTimeSetting = {
  simpleSearch: {
    form: {
      keywordPlaceholder: '请输入场馆编号或场馆名称',
    },
  },
  tableList: {
    columns: {
      venueNumber: '场馆编号',
      venueName: '场馆名称',
      venueCategory: '场馆类别',
      capacity: '可容纳人数',
      appointmentRule: '预约规则',
      lastAction: '最后操作人/最后操作时间',
      status: '状态',
      action: '操作',
    },
    action: {
      timeSetting: '时间设置',
    },
  },

  modalDetail: {
    title: '场馆详情',
    form: {
      label: {
        venueChnName: '场馆中文名称',
        venueEngName: '场馆英文名称',
        venueNumber: '场馆编号',
        venueCategory: '场馆类别',
        venueProperty: '场馆属性',
        venueManagerUsers: '场地管理人员',
        capacity: '可容纳人数',
        venueLocation: '场馆位置',
        whetherToEnable: '是否启用',
        lockid: '门禁ID',
      },
    },
  },

  timeSetting: {
    title: '{name}时间设置',
    action: {
      openAppointment: '开放预约',
      closeAppointment: '不开放预约',
      fixVenue: '固定场馆',
    },
    tableList: {
      columns: {
        period: '时段',
        enable: '时段启用',
        date: '日期',
        monday: '星期一',
        tuesday: '星期二',
        wednesday: '星期三',
        thursday: '星期四',
        friday: '星期五',
        saturday: '星期六',
        sunday: '星期日',
      },
      cell: {
        fixedVenueName: '固定场名称：',
      },
    },
    hint: {
      timePeriodsRequired: '请先选择时段',
      intro: {
        symbolDesc: '符号说明：',
        openTip: '绿色对勾，表示当前场馆在对应时段开放预约',
        closeTip: '灰色叉号，表示当前场馆在对应时段内不开放',
        lockTip: '锁形，表示场馆对应时段为固定场馆（教学、活动使用），预约界面会自动显示不可预约状态且标注固定场名称。',
      },
    },
    modalOpen: {
      title: '开放预约设置',
      content: '确定将所选的时段设为开放预约吗？',
    },
    modalClose: {
      title: '不开放预约设置',
      content: '确定将所选的时段设为不开放预约吗？',
    },
    modalLock: {
      title: '固定场设置',
      content: '将所选的时段设为不可预约的固定场吗？',
      form: {
        label: {
          fixedVenueName: '固定场名称',
        },
        placeholder: {
          fixedVenueName: '请输入不超过二十个字',
        },
      },
    },
  },
};

export default venueTimeSetting;
