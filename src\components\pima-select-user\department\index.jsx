/**
 * Select Department Component
 */

import Tree from 'ant-design-vue/lib/tree';
import Input from 'ant-design-vue/lib/input';
import Tag from 'ant-design-vue/lib/tag';
import Empty from 'ant-design-vue/lib/empty';
import {
  flatTreeNodeList,
  getFilterTree,
  getSelectedTreeNodeList,
  getTreeNodeChildrenList,
} from '../utils';

import './index.less';

let treeAllKeys = [];

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  return function () {
    const ctx = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, args);
    }, wait);
  };
}

const PimaSelectDepartment = {
  name: 'PimaSelectDepartment',

  props: {
    checked: {
      type: Array,
      default: () => [],
    },
    departmentData: {
      type: Array,
      default: () => [],
    },
    defaultDepartmentKeys: {
      type: Array,
      default: () => [],
    },
    filterProp: {
      type: String,
      default: 'title',
    },
    searchPlaceholder: {
      type: String,
      default: '请输入关键字...',
    },
    searchTimeGap: {
      type: Number,
      default: 600,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  components: {
    [Tree.name]: Tree,
    [Input.Search.name]: Input,
    [Tag.name]: Tag,
    [Empty.name]: Empty,
  },

  data() {
    return {
      isFirstRender: true,
      searchValue: '',
      checkedKeys: [],
      treeExpandedKeys: [],
      departmentValue: [],
      treeData: [],
      hiddenTree: true,
      triggerSelectCounter: 0,
      bodyAllClickCounter: 0,
    };
  },

  computed: {
    /**
     * 将已选择的 TreeNode Keys 转换成展示的 Tags
     * @return {Array<TreeNode>}
     */
    selectedTags() {
      if (this.isFirstRender) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.isFirstRender = false;
        return getSelectedTreeNodeList(this.departmentData, this.checkedKeys.map(key => ({ key })));
      }
      return getSelectedTreeNodeList(this.departmentData, this.checkedKeys);
    },
  },

  mounted() {
    // this.initBodyClickEvent();
    this.treeData = this.departmentData;
    this.checkedKeys = this.defaultDepartmentKeys;
    treeAllKeys = flatTreeNodeList(this.treeData).map(item => item.key);
    this.treeExpandedKeys = treeAllKeys;
    this.$emit('update:checked', getSelectedTreeNodeList(this.treeData, [...this.defaultDepartmentKeys]));
  },

  methods: {
    /**
     * 选中节点改变选中key
     * @param selectedKeys:
     */
    handleCheckTreeNode(selectedKeys) {
      this.checkedKeys = [...selectedKeys.checked];
      this.$emit('update:checked', getSelectedTreeNodeList(this.treeData, [...selectedKeys.checked]));
    },

    /**
     * 树展开/收缩
     * @param expandedKeys: {Array<any>} 已经展开的节点key
     * @param ev: {Object} 当前点击的节点
     */
    handleExpandTreeNode(expandedKeys, ev) {
      const nodeIndex = this.treeExpandedKeys.indexOf(ev.node.eventKey)
      const nodeChildBranch = getTreeNodeChildrenList(this.treeData, ev.node.eventKey);
      let calcExpandKeys = JSON.parse(JSON.stringify(this.treeExpandedKeys));
      if (nodeIndex > -1) {
        nodeChildBranch.forEach((item) => {
          calcExpandKeys = calcExpandKeys.filter((key) => key !== item.id);
        })
        this.treeExpandedKeys = calcExpandKeys;
      } else {
        this.treeExpandedKeys = [...calcExpandKeys, ...nodeChildBranch.map((item) => item.id)];
      }
    },

    /**
     * 搜索树
     */
    searchDepartment() {
      this.treeData = getFilterTree(this.departmentData, this.searchValue, this.filterProp);
      if (!this.searchValue) {
        this.treeExpandedKeys = treeAllKeys;
        this.treeData = this.departmentData;
        return;
      }
      this.treeExpandedKeys = this.expandSearchTreeKeys(this.treeData);
    },

    /**
     * 获取搜索后的所有节点的key
     * @param treeData: Array<treeData>
     * @param result: Array<String>
     */
    expandSearchTreeKeys(treeData, result = []) {
      if (Array.isArray(treeData)) {
        treeData.forEach(item => {
          if (item.children && item.children.length) {
            this.expandSearchTreeKeys(item.children, result);
          } else {
            result.push(item.key);
          }
        });
      }
      return result;
    },

    /**
     * 关闭某个 Tag
     * @param item: TreeNode
     */
    closeTag(item) {
      this.checkedKeys = this.checkedKeys.filter(key => key !== item.key);
      this.$emit('update:checked', getSelectedTreeNodeList(this.treeData, this.checkedKeys.filter(key => key !== item.key)));
    },

    /**
     * 同步改变searchValue
     * @param e: Event
     */
    handleChangeSearchInput(e) {
      this.searchValue = e.target.value;
    },
    toggleSelectOpen(e) {
      const classList = Array.from(e.target.classList);
      if (
        (classList.indexOf('place') > -1 ||
        classList.indexOf('pima-selected-string') > -1 ||
        classList.indexOf('pima-select-title') > -1 ||
        classList.indexOf('select-arrow') > -1) && this.hiddenTree
      ) {
        this.hiddenTree = false;
      } else if (
        (classList.indexOf('place') > -1 ||
        classList.indexOf('pima-selected-string') > -1 ||
        classList.indexOf('pima-select-title') > -1 ||
        classList.indexOf('select-arrow') > -1) && !this.hiddenTree
      ) {
        this.hiddenTree = true;
      }
    },
    /**
     * Tree失去焦点
     */
    blurTreeSelect() {
      // this.hiddenTree = true;
    },

    /**
     * Tree获取焦点显示
     */
    focusTreeSelect() {
      this.hiddenTree = false;
      this.triggerSelectCounter = this.bodyAllClickCounter;
    },

    /**
     * Tree获取焦点时点击次数计数器
     */
    treeClickCounter(e) {
      if (!this.hiddenTree) {
        // eslint-disable-next-line no-plusplus
        this.triggerSelectCounter++;
      }
    },

    /**
     * 初始化Body点击事件及Body点击次数计数器
     */
    initBodyClickEvent() {
      document.querySelector('.select-user-box').addEventListener('click', (e) => {
        e.preventDefault();
        // eslint-disable-next-line no-plusplus
        this.bodyAllClickCounter++;
        if (!this.hiddenTree && this.bodyAllClickCounter !== this.triggerSelectCounter) {
          this.hiddenTree = true;
        }
      });
    },
  },
  render(h, ctx) {
    /**
     * 树组件 .sync update事件
     * @type {{"update:checkedKeys": (function(*): *)}}
     */
    const treeListenrs = {
      // eslint-disable-next-line no-return-assign
      'update:checkedKeys': val => this.checkedKeys = val,
    };

    /**
     * 树插槽
     * @type {{title: (function({title: *}))}}
     */
    const treeScopedSlots = {
      title: (props) => {
        return (
          <span>
            <div class="whole-placeholder"/>
            <span style={
              this.searchValue && props[this.filterProp] &&
              props[this.filterProp].toLowerCase().indexOf(this.searchValue.toLowerCase()) > -1
                ? 'color: #f50' : ''
            }>
              { props[this.filterProp] }
            </span>
          </span>
        );
      },
    };

    return (
      <div
        tabindex="0"
        class="pima-select-user"
        // onFocus={this.focusTreeSelect}
        onBlur={this.blurTreeSelect}
        onClick={this.toggleSelectOpen}
      >
        <span class="pima-select-title">
          <span class="pima-selected-string">
            { this.selectedTags.length ? '' : <span class={'place'} style={{ color: '#999' }}>请选择部门</span> }
            {this.selectedTags.map((item, index) => {
              return (
                <a-tag
                  key={item[this.filterProp] + index + +new Date() + Math.random() * 100}
                  closable={true}
                  onClose={() => this.closeTag(item)}
                >
                  {item[this.filterProp]}
                </a-tag>
              );
            })}
          </span>
          <div class={`select-arrow ${this.hiddenTree ? '' : 'open'}`}>
            <div>
              <span class={'iconfont icon-allright_xiala'}></span>
            </div>
          </div>
        </span>

        <div
          class={`treebox ${this.hiddenTree ? 'hidden' : ''}`}
        >
          <div class={'tree-search-wrap'}>
            <a-input-search
              value={this.searchValue}
              placeholder={this.searchPlaceholder}
              onInput={e => this.handleChangeSearchInput(e)}
              onChange={() => debounce(this.searchDepartment, this.searchTimeGap)()}
            >
              <a-icon slot="prefix" type="search" />
            </a-input-search>
          </div>
          <div class="tree-wrap">
            {
              this.treeData.length ?
                <a-tree
                  selectable={false}
                  checkable={true}
                  check-strictly={true}
                  default-expand-all={true}
                  auto-expand-parent={true}
                  expanded-keys={this.treeExpandedKeys}
                  checked-keys={this.checkedKeys}
                  tree-data={this.treeData}
                  {...{ on: treeListenrs, scopedSlots: treeScopedSlots }}
                  onCheck={this.handleCheckTreeNode}
                  onExpand={this.handleExpandTreeNode}
                />
                : <a-empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            }
          </div>
        </div>
      </div>
    );
  },
};

export default PimaSelectDepartment;
