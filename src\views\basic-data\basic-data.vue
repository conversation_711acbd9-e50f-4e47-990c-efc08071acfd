<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.BasicData.View)"
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <template #title>
          <!--查看详情-->
          <template
            v-if="contentType !== SHOW_CONTENT_TYPE.VIEW"
          >
            <GoBack
              @click.native="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
            />
            {{ headerTitle }}
          </template>
        </template>
        <SimpleSearch
          v-show="contentType === SHOW_CONTENT_TYPE.VIEW"
          ref="SimpleSearch"
          :venue-category-options="venueCategoryOptionsAll"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
        />

        <template
          #extra
        >
          <!--基础数据头部-->
          <template v-if="contentType === SHOW_CONTENT_TYPE.VIEW">
            <!-- 场馆类别管理 -->
            <a-button
              v-if="hasP(P => P.BasicData.Category)"
              type="primary"
              size="small"
              @click="handleVenueListCategory"
            >
              {{ $t('basicData.title.category') }}
            </a-button>
            <a-button
              v-if="hasP(P => P.BasicData.Add)"
              type="primary"
              size="small"
              @click="handleShowContentType(SHOW_CONTENT_TYPE.ADD)"
            >
              {{ $t('basicData.title.add') }}
            </a-button>
          </template>
        </template>
      </a-page-header>

      <div
        class="panel-body"
        :class="contentType === SHOW_CONTENT_TYPE.VIEW?'':'pb-80'"
      >
        <!--基础数据查看-->
        <template
          v-if="contentType === SHOW_CONTENT_TYPE.VIEW"
        >
          <TableList
            :venue-category-map="venueCategoryMap"
            @edit-venue="onEditVenue"
          />
        </template>
        <!-- 新增场馆页面 -->
        <template v-if="contentType === SHOW_CONTENT_TYPE.ADD">
          <AddVenue
            ref="AddVenue"
            :init-data="{initData: {}}"
            :submiting="formLoading"
            :venue-category-options="venueCategoryOptions"
            @handle-add-submit="handleAddSubmit"
            @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
          />
          <div class="footer_btns">
            <div class="align-center-btns">
              <a-button
                type="default"
                @click="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
              >
                {{ $t('reserveRule.action.cancel') }}
              </a-button>
              <a-button
                type="primary"
                @click="$refs.AddVenue.handleSubmit()"
              >
                {{ $t('action.submit') }}
              </a-button>
            </div>
          </div>
        </template>
        <!-- 编辑场馆页面 -->
        <template v-if="contentType === SHOW_CONTENT_TYPE.EDIT">
          <EditVenue
            ref="EditVenue"
            :row-id="rowId"
            :venue-category-options="venueCategoryOptions"
            @handle-edit-submit="handleEditSubmit"
            @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
          />
          <div class="footer_btns">
            <div class="align-center-btns">
              <a-button
                type="default"
                @click="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
              >
                {{ $t('reserveRule.action.cancel') }}
              </a-button>
              <a-button
                type="primary"
                @click="$refs.EditVenue.handleSubmit()"
              >
                {{ $t('action.submit') }}
              </a-button>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { hasOwn } from '@utils/core';
import operation from '@mixins/operation';
import {
  addVenuesApi,
  updateVenuesApi,
  getCategoryListApi,
} from '@api/basic-data-api';
import GoBack from '@components/base/go-back.vue';

import SimpleSearch from './components/simple-search.vue';
import TableList from './components/table-list.vue';
import AddVenue from './components/add-venue.vue';
import EditVenue from './components/edit-venue.vue';

const SHOW_CONTENT_TYPE = {
  ADD: 'ADD', // 新增
  VIEW: 'VIEW', // 基础数据查看页
  EDIT: 'EDIT', // 编辑
};

export default {
  name: 'BasicData',

  components: {
    AddVenue,
    EditVenue,
    GoBack,
    TableList,
    SimpleSearch,
  },

  mixins: [operation],

  data() {
    this.SHOW_CONTENT_TYPE = SHOW_CONTENT_TYPE;
    return {
      venueCategoryMap: new Map(),
      venueCategoryOptions: [],
      venueCategoryOptionsAll: [],
      rowId: 0,
      filter: {
        keyword: '',
        status: '',
      },
      // 编辑页初始数据
      initData: {},
      isAdvancedSearch: false,
      record: {},
      contentType: SHOW_CONTENT_TYPE.VIEW,
      modalType: '',
      formLoading: false,
      activeRowRecord: {},
    };
  },

  computed: {
    headerTitle() {
      let title;
      switch (this.contentType) {
        case SHOW_CONTENT_TYPE.VIEW:
          title = this.$t('basicData.title.title');
          break;
        case SHOW_CONTENT_TYPE.ADD:
          title = this.$t('basicData.title.addTitle');
          break;
        case SHOW_CONTENT_TYPE.EDIT:
          title = this.$t('basicData.title.editTitle');
          break;
        default:
          title = this.$t('basicData.title.title');
          break;
      }
      return title;
    },
  },
  beforeMount() {
  // 动态获取场馆类别
    getCategoryListApi({ limit: -1 })
      .then(({ data = [] }) => {
        const venueCategoryOptions = data.map((item) => ({
          key: item.id,
          value: item.id,
          title: item.name,
          label: item.name,
        }));
        this.venueCategoryOptions = venueCategoryOptions;
        this.venueCategoryOptionsAll = [
          {
            key: 'all',
            value: '',
            title: this.$t('common.all'),
            label: this.$t('common.all'),
          }, ...venueCategoryOptions];
        const venueCategoryMap = new Map();
        (data.map((item) => [item.id, item.name])).forEach((item) => {
          venueCategoryMap.set(...item);
        });
        this.venueCategoryMap = venueCategoryMap;
      })
      .catch((err) => {
        this.$message.error(err.response.data.errorMsg);
      });
  },

  mounted() {
    // 重置菜单列表
    this.$eventBus.$on('reset-table', this.resetTable);
    const urlQuery = this.$route.query;
    Object.keys(urlQuery).forEach((k) => {
      if (hasOwn(this.filter, k)) this.filter[k] = urlQuery[k];
    });
    this.fetchVenuesListApi({
      page: urlQuery.page || 1,
      pageSize: urlQuery.pageSize || 10,
      filter: {
        ...this.filter,
      },
    });
    // 获取选人组件数据
    this.fetchUserList({
      limit: -1,
    });
  },

  methods: {
    resetTable() {
      this.handleShowContentType(SHOW_CONTENT_TYPE.VIEW);
      // 重新获取基础数据列表
      if (this.$refs.SimpleSearch) {
        this.$refs.SimpleSearch.resetSearch();
      }
    },
    ...mapActions({
      fetchVenuesListApi: 'basicData/fetchVenuesListApi',
      fetchUserList: 'uniData/fetchUserList',
    }),
    resetSearch(filter) {
      this.fetchVenuesListApi(filter);
    },
    handleSearch(filter) {
      this.fetchVenuesListApi(filter);
    },
    // 提交新增表单
    handleAddSubmit(formData) {
      this.formLoading = true;
      addVenuesApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.$t('basicData.msg.submitSuccess'));
          this.closeModal();
          this.fetchVenuesListApi({});
        })
        .catch(() => {
          this.formLoading = false;
          // this.$message.error(err.response.data.errorMsg);
        });
    },
    // 提交编辑表单
    handleEditSubmit(formData) {
      this.formLoading = true;
      updateVenuesApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.$t('basicData.msg.submitSuccess'));
          this.closeModal();
          this.fetchVenuesListApi({});
        })
        .catch(() => {
          this.formLoading = false;
          // this.$message.error(err.response.data.errorMsg);
        });
    },
    // 修改显示类型
    handleShowContentType(type, cb) {
      new Promise((resolve) => {
        const res = cb ? cb(this) : null;
        resolve(res);
      })
        .then(() => {
          this.contentType = type;
        });
    },
    // 监听编辑事件
    onEditVenue(row) {
      this.handleShowContentType(SHOW_CONTENT_TYPE.EDIT);
      const { id } = row;
      this.rowId = id;
    },
    // 关闭表单
    closeModal() {
      this.modalType = '';
      this.activeRowRecord = {};
      this.formLoading = false;
      this.contentType = SHOW_CONTENT_TYPE.VIEW;
    },
    handleVenueListCategory() {
      this.$router.push({
        name: 'venueListCategory',
      });
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-heading-title {
  display: flex;
  align-items: center;
}
::v-deep .ant-page-header-content {
  position: absolute;
  top: 7px;
  padding-top: 0;
  overflow: visible;
}
.panel-body.pb-80 {
  padding-bottom: 80px;
}
</style>
