<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.ReserveQuery.View)"
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <SimpleSearch
          v-if="!isAdvancedSearch"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
          @toggle-advanced-search="() => { isAdvancedSearch = true }"
        />
        <template #extra>
          <!--预约查询头部头部-->
          <a-button
            v-if="hasP(P => P.ReserveQuery.Export)"
            type="primary"
            size="small"
            @click="handleExport"
          >
            {{ t('title.export') }}
          </a-button>
        </template>
      </a-page-header>

      <div class="panel-body">
        <!--基础数据查看-->
        <AdvancedSearch
          v-if="isAdvancedSearch"
          ref="AdvancedSearch"
          :depts="depts"
          :venue-options="venueIds"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
          @toggle-advanced-search="() => { isAdvancedSearch = false }"
        />
        <TableList
          :scroll="tableScroll"
          @cancel-reserve="onCancelReserve"
          @detail-reserve="onDetailReserve"
          @on-export="onSelectExport"
        />
        <!-- 取消预约Modal -->
        <CancelModal
          :id="id"
          ref="CancelModal"
          v-model="isShowCancelModal"
          :submiting="formLoading"
          @handle-cancel-submit="handleCancelSubmit"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { hasOwn } from '@utils/core';
import operation from '@mixins/operation';
import {
  getVenuesListApi,
} from '@api/basic-data-api';
import {
  postCancelReservationApi,
} from '@api/reserve-query-api';
import { getDepts } from '@api/arch-api';
import { nsI18n } from '@/mixins/ns-i18n';
import { getTimeStamp } from '@utils/dateformat';
import { TimeRangeType } from '@/constants/venue';

import SimpleSearch from './components/simple-search.vue';
import AdvancedSearch from './components/advanced-search.vue';
import TableList from './components/table-list.vue';
import CancelModal from './components/cancel-modal.vue';

export default {
  name: 'ReserveQuery',

  components: {
    CancelModal,
    TableList,
    SimpleSearch,
    AdvancedSearch,
  },

  mixins: [
    operation,
    nsI18n('t', 'reserveQuery'),
  ],

  data() {
    return {
      TimeRangeType,
      initData: {},
      initSteps: {},
      filter: {
        keyword: null,
        status: null,
        venueIds: [],
        timeRangeType: TimeRangeType.APPROVALTIME,
      },
      id: 0,
      depts: [],
      venueIds: [],
      isAdvancedSearch: false,
      isShowCancelModal: false,
      // 是否是从详情取消
      isCancelDetail: false,
      record: {},
      modalType: '',
      formLoading: false,
    };
  },

  computed: {
    tableScroll() {
      if (this.isAdvancedSearch) {
        return { y: 'calc(100vh - 425px)' };
      }
      return { y: 'calc(100vh - 214px)' };
    },
  },

  mounted() {
    const urlQuery = this.$route.query;
    Object.keys(urlQuery).forEach((k) => {
      if (hasOwn(this.filter, k)) this.filter[k] = urlQuery[k];
    });
    this.fetchMyReservationsList({
      page: urlQuery.page || 1,
      pageSize: urlQuery.pageSize || 10,
      filter: {
        ...this.filter,
      },
    });
    // 获取部门
    getDepts()
      .then(({ model }) => {
        this.depts = [
          {
            key: 'all',
            value: '',
            title: this.$t('common.all'),
            label: this.$t('common.all'),
          },
          ...model.map((m) => ({
            label: m.name,
            key: m.id,
            value: m.id,
          })),
        ];
      });
    // 获取场馆名称
    const filter = { ...this.filter, isAll: true };
    getVenuesListApi(filter)
      .then(({ data = [] }) => {
        this.venueIds = [
          ...data.map((m) => ({
            label: m.name,
            key: m.id,
            value: m.name,
          })),
        ];
      });
  },

  methods: {
    getTimeStamp,
    ...mapActions({
      fetchMyReservationsList: 'reserveQuery/fetchMyReservationsList',
      fetchExportMyReservationsList: 'reserveQuery/fetchExportMyReservationsList',
    }),
    resetSearch(filter) {
      this.filter = filter;
      this.fetchMyReservationsList(filter);
    },
    handleSearch(filter) {
      this.filter = filter;
      this.fetchMyReservationsList(filter);
    },
    // 提交编辑表单
    handleCancelSubmit(formData) {
      this.formLoading = true;
      postCancelReservationApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.t('msg.submitSuccess'));
          this.closeModal();
          // 刷新页面
          this.fetchMyReservationsList({});
        })
        .catch(() => {
          this.formLoading = false;
        });
    },
    // 监听取消预约事件
    onCancelReserve(row) {
      this.isShowCancelModal = true;
      const { id } = row;
      this.id = id;
    },
    // 监听详情预约取消事件
    onCancelDetail(row) {
      const { id } = row;
      this.id = id;
      this.isShowCancelModal = true;
      this.isCancelDetail = true;
    },
    // 监听预约详情事件
    onDetailReserve(row) {
      const { id } = row;
      this.$router.push({
        name: 'ReservationDetail',
        query: { id },
      });
    },
    // 检查导出是否有选择条件
    checkHasSearchItem(filters) {
      const values = { ...filters };
      delete values.timeRangeType;
      Object.keys(values).forEach((k) => {
        if (!values[k]) {
          delete values[k];
        }
      });
      if (!Object.keys(values).length) {
        this.$message.error(this.t('msg.notHasSearchItems'));
        return false;
      }
      return true;
    },
    // 监听导出事件
    handleExport() {
      if (this.isAdvancedSearch) {
        const advanceSearchValue = this.$refs.AdvancedSearch.getFormData();
        this.filter = {
          filter: { ...advanceSearchValue },
        };
        if (!this.checkHasSearchItem(advanceSearchValue)) {
          return;
        }
      } else {
        const simpleSearchValues = { ...this.filter };
        delete simpleSearchValues.venueIds;
        if (!this.checkHasSearchItem(simpleSearchValues)) {
          return;
        }
      }

      const tempFilter = this.filter;
      const fileName = `${this.$t('reserveQuery.title.fileName')}${this.getTimeStamp()}`;
      tempFilter.fileName = fileName;
      this.fetchExportMyReservationsList(this.filter, fileName);
    },
    // 监听批量导出事件
    onSelectExport(ids) {
      this.filter.venueIds = ids;
    },
    // 关闭表单
    closeModal() {
      this.modalType = '';
      this.id = '';
      this.formLoading = false;
      this.isShowCancelModal = false;
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header {
  min-height: 50px;
  height: auto;
}
::v-deep .ant-page-header-content {
  padding-top: 0;
  overflow: visible;
  padding-right: 120px;
}
::v-deep .ant-page-header-heading {
  position: absolute;
  right: 0;
  padding-right: 24px;
}
::v-deep .ant-form-inline .ant-form-item {
  margin: 7px 8px 7px 0;
}
</style>
