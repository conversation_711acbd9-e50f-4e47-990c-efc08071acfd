export default {
  title: {
    title: 'Repairer management',
    addTitle: 'Add repairer',
    editTitle: 'Edit repairer',
  },
  columns: {
    staffNo: 'Staff No.',
    dept: 'Management department',
    repairStaff: 'Repairer',
    createPerson: 'Created by',
    lastUpdateTime: 'Last edit time',
    isAdmin: 'Whether to set as the administrator',
    enable: 'Status',
    action: 'Operate',
    enabled: 'Enable',
    disabled: 'Unable',
  },
  action: {
    addStaff: 'Add Repairer',
  },
  form: {
    repairStaffPlace: 'Please select personnel',
    keywordPlace: 'Employee number or name',
    isEnable: 'Whether to enable',
    managementDepart: 'Management department',
    managementDepartPlace: 'Please select management department',
    isAdmin: 'Whether to set as the administrator',
    its: 'its',
    cso: 'cso',
    tooLong: 'Content is too long',
  },
  msg: {
    addSuccess: 'Added successfully',
    editSuccess: 'Updated successfully',
  },
};
