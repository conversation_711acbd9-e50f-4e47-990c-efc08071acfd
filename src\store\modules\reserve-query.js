import { getMyReservationsListApi, exportMyReservationsListApi } from '@api/reserve-query-api';

const initState = {
  dataSource: [],
  loading: false,
  page: 1,
  pageSize: 10,
  total: 0,
  filter: {
    // 活动名称
    activityName: '',
    // 申请时间范围开始时间
    approvalStartTime: '',
    // 申请时间范围结束时间
    approvalEndTime: '',
    // 状态
    // [pending-待审批，approving-审批中，pass-审批通过，auto-pass-自动通过，pending_sign-待签到,signed-已签到，reject-未通过，cancel-已取消预约, off-已爽约]
    status: '',
    // 申请状态
    // [pending-待审批，approving-审批中，pass-审批通过，auto-pass-自动通过，pending_sign-待签到,signed-已签到，reject-未通过，cancel-已取消预约, off-已爽约]
    approvalStatus: '',
    // 所在组织
    deptId: '',
    // 预约开始时间
    startTime: '',
    // 预约结束时间
    endTime: '',
    // 关键字
    keyword: '',
    // 预约时间范围开始时间
    reserveStartTime: '',
    // 预约时间范围结束时间
    reserveEndTime: '',
    // 申请单号
    sn: '',
    // 时间范围
    // [申请时间范围:createTime, 预约时间范围：approvalTime]
    timeRangeType: '',
    // 用户id
    userId: '',
    // 预约人关键字
    userKeyword: '',
    // 场馆预约名称
    venueIds: '',
  },
};

const getters = {};

const mutations = {
  setDataSource(state, payload) {
    state.dataSource = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setPage(state, payload) {
    if (!payload) return;
    state.page = Number(payload);
  },
  setPageSize(state, payload) {
    if (!payload) return;
    state.pageSize = Number(payload);
  },
  setTotal(state, payload = 0) {
    state.total = payload;
  },
  setFilter(state, payload) {
    Object.keys(payload).forEach((key) => {
      if (payload[key] !== undefined && Object.prototype.hasOwnProperty.call(state.filter, key)) {
        state.filter[key] = payload[key];
      }
    });
  },
};

const actions = {
  fetchMyReservationsList({ commit, state }, payload) {
    const { filter, page, pageSize } = payload;
    commit('setFilter', filter || {});
    commit('setPage', page);
    commit('setPageSize', pageSize);
    commit('setLoading', true);

    const params = {
      page: state.page,
      limit: state.pageSize,
    };

    Object.entries(state.filter).forEach(([k, v]) => {
      if (v !== '') params[k] = v;
      // 高级搜索不传该参数 timeRangeType
      if (params[k] === 'advancedSearch') {
        delete params[k];
      }
    });

    return new Promise((resolve, reject) => {
      getMyReservationsListApi(params)
        .then((res) => {
          commit('setDataSource', res.data);
          commit('setTotal', res.total);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
  fetchExportMyReservationsList({ commit, state }, payload) {
    const {
      filter, page, pageSize, fileName,
    } = payload;
    commit('setFilter', filter || {});
    commit('setPage', page);
    commit('setPageSize', pageSize);
    commit('setLoading', true);

    const params = {
      page: state.page,
      limit: state.pageSize,
    };

    Object.entries(state.filter).forEach(([k, v]) => {
      if (v !== '') params[k] = v;
      // 高级搜索不传该参数 timeRangeType
      if (params[k] === 'advancedSearch') {
        delete params[k];
      }
    });
    params.fileName = fileName;
    return new Promise((resolve, reject) => {
      exportMyReservationsListApi(params)
        .then((res) => {
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};

export default {
  namespaced: true,
  state: initState,
  getters,
  mutations,
  actions,
};
