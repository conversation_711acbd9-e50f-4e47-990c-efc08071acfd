const { getRedisValue, setRedisValue, deleteRedisKey } = require('./redis');
const { getJsonFromXML } = require('./utils');

const TICKET_PREFIX = 'tkt';
const SESSION_PREFIX = 'sess';
const DURATION = 60 * 60 * 24 * 30;

module.exports = function (options) {
  const { redisClient } = options;
  if (!redisClient) {
    throw new Error('redisClient not configured');
  }

  function getTicketKey(ticket) {
    return `${TICKET_PREFIX}:${ticket}`;
  }

  function getSessionKey(sessionId) {
    return `${SESSION_PREFIX}:${sessionId}`;
  }

  async function casTicketSessionMapperMiddleware(req, res, next) {
    if (req.query && req.query.ticket && req.session && req.session.id) {
      // 仅保存30天
      const key = getTicketKey(req.query.ticket);
      // 保存30天，正常情况不会超过这个时间，实现自动清除
      await setRedisValue(redisClient, key, req.session.id, DURATION);
    }
    next();
  }

  async function casSloMiddleware(req, res, next) {
    if (req.body && req.body.logoutRequest) {
      const logoutRequest = await getJsonFromXML(req.body.logoutRequest);
      if (logoutRequest
        && logoutRequest['samlp:LogoutRequest']
        && logoutRequest['samlp:LogoutRequest']['samlp:SessionIndex']) {
        const sessionIndex = logoutRequest['samlp:LogoutRequest']['samlp:SessionIndex'];
        if (sessionIndex && Array.isArray(sessionIndex) && sessionIndex.length > 0) {
          const ticketKey = getTicketKey(sessionIndex[0].trim());
          const sessionId = await getRedisValue(redisClient, ticketKey);
          const sessionKey = getSessionKey(sessionId);
          await deleteRedisKey(redisClient, sessionKey);
          await deleteRedisKey(redisClient, ticketKey);
        }
      }

      res.status(201).end();
    }

    next();
  }

  return {
    casTicketSessionMapperMiddleware,
    casSloMiddleware,
  };
}
