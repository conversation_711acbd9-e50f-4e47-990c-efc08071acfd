import httpRequest from './request';
import config from '../config';

// 查询场馆列表
export function getVenuesListApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/venues',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
// 查询场馆列表动态选择器选项
export function searchVenuesListApi(name) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/venues',
      method: 'get',
      params: {
        name,
        isAll: true,
      },
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 新增基础数据场馆
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function addVenuesApi(data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/venues',
      method: 'post',
      data,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 获取规则详情
 * @param userId: number
 * @return {Promise<unknown>}
 */
export function getVenuesDetailApi(id) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/venues/${id}`,
      method: 'get',
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 编辑规则
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function updateVenuesApi(data) {
  const { id = '' } = data;
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/venues/${id}`,
      method: 'post',
      data,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 获取场馆类别列表
export function getCategoryListApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: 'venue-categories',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 获取规则详情
 * @param id: number
 * @return {Promise<unknown>}
 */
export function getCategoryDetailApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `venue-categories/${params.id}`,
      method: 'get',
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 新增场馆类别
export function addCategoryApi(data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: 'venue-categories',
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 修改场馆类别
export function editCategoryApi({ id, data }) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `venue-categories/${id}`,
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 删除场馆类别
export function delCategoryApi({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `venue-categories/${id}/remove`,
      method: 'post',
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

export function uploadAttachment({ relateType, fileData }) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('relateType', relateType);
    formData.append('fileData', fileData);
    httpRequest
      .post('/attachments/upload', formData, {
        baseURL: config.pimaUploadBaseUrl,
        headers: {
          Accept: ' */*',
          'Content-Type': 'multipart/form-data',
        },
      })
      .then((resp) => {
        resolve(resp);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
