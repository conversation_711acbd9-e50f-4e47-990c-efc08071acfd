<template>
  <div>
    <!-- <div class="sidebar-header">
      <div class="sidebar-title">{{ serviceName }}</div>
      <a-icon
        type="menu-fold"
        class="fold-icon"
      />
    </div> -->
    <ul
      class="menu"
      mode="inline"
      :default-selected-keys="defaultSelectedKeys"
      :selected-keys="currentSelectedKeys"
      :default-open-keys="defaultOpenKeys"
      :open-keys="currentOpenKeys"
      :inline-indent="40"
      @openChange="handleOpenChange"
    >
      <template v-for="item in menu">
        <li
          v-if="item.children && item.children.length > 0"
          :key="item.key"
          class="submenu ant-menu-submenu ant-menu-submenu-inline"
          :class="
            [item.key == currentOpenKeys[0] ? 'ant-menu-submenu-selected' : '', {'ant-menu-submenu-open':item.open}]"
        >
          <div
            slot="title"
            class="ant-menu-submenu-title"
            @click="togglePay(item)"
          >
            <div class="menu-item-wrap">
              <span
                v-if="item.icon"
                class="menu-icon iconfont"
                :class="item.icon"
              />
              {{ item.title }}
            </div>
            <i class="ant-menu-submenu-arrow" />
            <span
              class="badge"
              :count="item.badge"
              :overflow-count="overflowCount"
            />
          </div>
          <ul class="ant-menu ant-menu-sub ant-menu-inline">
            <template v-for="child in item.children">
              <li
                :key="child.key"
                :class="[child.key == currentSelectedKeys[0] ? 'ant-menu-item-selected' : '']"
                class="menu-item submenu-item ant-menu-item"
                @click="itemSelect(child.key, item.key, child.target)"
              >
                {{ child.title }}
                <span
                  :count="child.badge"
                  :overflow-count="overflowCount"
                  class="badge"
                />
              </li>
            </template>
          </ul>
        </li>
        <li
          v-else
          :key="item.key"
          :title="item.title"
          :class="[item.key == currentSelectedKeys[0] ? 'ant-menu-item-selected' : '']"
          class="menu-item ant-menu-item"
          @click="itemSelect(item.key, item.key, item.target)"
        >
          <span
            v-if="item.icon"
            class="menu-icon iconfont"
            :class="item.icon"
          />
          <span
            class="title-label"
            style="vertical-align: middle;"
          >
            {{ item.title }}
          </span>
          <a-badge
            class="badge"
            :count="item.badge"
            :overflow-count="overflowCount"
          />
          <span
            v-if="item.latestMsgTime"
            class="last-time"
          >
            {{ dateFormat(item.latestMsgTime) }}
          </span>
        </li>
      </template>
    </ul>
  </div>
</template>

<script>
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import { Locale } from '@/constants/locale';
import zhCN from '@/locales/lang/zh-CN';
import enUS from '@/locales/lang/en-US';
import zhHK from '@/locales/lang/zh-HK';

import moment from 'moment';
import { getUserData, getApplicationData } from '@api';
import { getApiLocale } from '@utils/api-locale';

const overflowCount = 99;

Vue.use(VueI18n);

export default {
  name: 'AppSidebar',
  components: {
  },
  provide() {
    return {
      i18n: this.i18n,
    };
  },
  props: {
    // 语言
    locale: {
      type: String,
      default: Locale.zhCN,
    },

    // 服务编码
    serviceCode: {
      type: String,
      default: null,
    },
    // 服务名称
    serviceName: {
      type: String,
      default: null,
    },
    // 获取Token方法
    getAuthorization: {
      type: Function,
      default() {
        return () => {};
      },
    },
    // 接口根目录
    apiBaseUrl: {
      type: String,
      default: null,
    },
    // 菜单
    menu: {
      type: Array,
      default() {
        return [];
      },
    },
    // 默认选中项键值
    selectedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 默认选中项键值
    openKeys: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      userName: null,
      userAccount: null,
      userAvatarUrl: null,
      headerMenu: [],
      sidebarMenu: [],
      sidebarMenuUrlMapper: {},
      applicationListShowed: false,
      loadApplications: false,
      applications: [],
      messageCount: 0,
      loading: true,
      curService: {},

      overflowCount,
      defaultSelectedKeys: [...this.selectedKeys],
      currentSelectedKeys: [...this.selectedKeys],
      defaultOpenKeys: [...this.openKeys],
      currentOpenKeys: [...this.openKeys],

      i18n: new VueI18n({
        locale: this.locale,
        messages: {
          [Locale.zhCN]: zhCN,
          [Locale.enUS]: enUS,
          [Locale.zhHK]: zhHK,
        },
      }),
    };
  },
  watch: {
    selectedKeys(val) {
      this.$nextTick(() => {
        this.currentSelectedKeys = val || [];
      });
    },
    openKeys(val) {
      this.$nextTick(() => {
        this.currentOpenKeys = val || [];
      });
    },
  },
  beforeMount() {
    // this.refreshUserData();
  },
  mounted() {
    const self = this;
    this.menu.forEach((item) => {
      if (item.key === this.currentOpenKeys[0]) {
        self.$set(item, 'open', true);
        // item.open = true;
      } else {
        self.$set(item, 'open', false);
        // item.open = false;
      }
    });
    // console.log('this.menu', this.menu);
  },
  methods: {
    dateFormat(date) {
      moment.locale(this.$i18n.locale);
      const dateMoment = moment(date);
      if (dateMoment.format('M-D') === moment().format('M-D')) {
        return dateMoment.format('HH:mm');
      }
      return dateMoment.format('MMMDo');
    },
    fetchUserData() {
      return new Promise((resolve, reject) => {
        this.loading = true;
        const lang = getApiLocale(this.$i18n.locale);
        const params = { lang, code: this.serviceCode };
        getUserData(this.apiBaseUrl, this.getAuthorization(), params).then((resp) => {
          this.loading = false;
          if (resp.status === 200) {
            if (resp.data.success) {
              const {
                userId,
                name,
                username,
                thumb,
                fmxServiceMenuDashboardList,
                fmxServiceMenuList,
                webMsgUnReadCount,
                curService,
              } = resp.data.model;
              resolve({
                userInfo: {
                  id: userId,
                  account: username,
                  name,
                  avatarUrl: thumb,
                },
                curService,
                menu: this.makeHeaderMenu(fmxServiceMenuDashboardList),
                sidebar: this.makeSidebarMenu(fmxServiceMenuList),
                sidebarUrlMapper: this.makeSidebarMenuUrlMapper(fmxServiceMenuList),
                operations: this.makeOperations(fmxServiceMenuList),
                messageCount: webMsgUnReadCount || 0,
              });
            } else {
              if (resp.data.model.userId) {
                const {
                  userId,
                  name,
                  username,
                  thumb,
                } = resp.data.model;
                resolve({
                  userInfo: {
                    id: userId,
                    account: username,
                    name,
                    avatarUrl: thumb,
                  },
                });
              }
              const error = new Error(resp.data.errorMsg);
              error.errorCode = resp.data.errorCode || 'UNKNOW_ERROR';
              throw error;
            }
          }
        }).catch((err) => {
          this.loading = false;
          reject(err);
        });
      });
    },
    fetchApplicationData() {
      this.loadApplications = true;
      const lang = getApiLocale(this.$i18n.locale);
      const params = { lang };
      getApplicationData(this.apiBaseUrl, this.getAuthorization(), params).then((resp) => {
        this.loadApplications = false;
        if (resp.status === 200) {
          if (resp.data.success) {
            let applications = [];
            if (resp.data && resp.data.model && Array.isArray(resp.data.model)) {
              applications = resp.data.model.map((item) => {
                let children = [];
                if (item.serviceList && Array.isArray(item.serviceList)) {
                  children = item.serviceList.map((subitem) => ({
                    name: subitem.name,
                    icon: subitem.icon,
                    icon1: subitem.icon1,
                    url: subitem.url,
                    target: subitem.urlTarget,
                  }));
                }
                return {
                  key: `category-${item.id}`,
                  name: item.name,
                  children,
                };
              });
            }
            this.applications = applications;
          }
        }
      }).catch(() => {
        this.loadApplications = false;
      });
    },
    refreshUserData() {
      this.fetchUserData().then((data) => {
        const {
          userInfo, menu, sidebar, sidebarUrlMapper, operations, messageCount, curService,
        } = data;
        this.userName = userInfo.name;
        this.userAccount = userInfo.account;
        this.userAvatarUrl = userInfo.avatarUrl;
        this.headerMenu = menu;
        this.sidebarMenu = sidebar;
        this.sidebarMenuUrlMapper = sidebarUrlMapper;
        this.messageCount = messageCount;
        this.curService = curService;
        this.$emit('user-data-fetched', {
          userInfo,
          menu,
          sidebar,
          operations,
          messageCount,
        });
      }).catch((error) => {
        this.$emit('user-data-fetched', {
          error,
        });
      });
    },
    makeHeaderMenu(menuItemList) {
      if (menuItemList && Array.isArray(menuItemList)) {
        return menuItemList.map((item) => ({
          key: item.code,
          title: item.name,
          url: item.url,
          target: item.urlTarget,
        }));
      }
      return [];
    },
    makeSidebarMenu(menuItemList) {
      const menuItems = [];
      if (Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          let children;
          if (Array.isArray(item.nextMenuList)) {
            children = this.makeSidebarMenu(item.nextMenuList);
          } else {
            children = [];
          }

          const base = {
            key: item.code,
            icon: item.thumb || null,
            title: item.name,
            link: item.url,
            badge: item.pendingNum || 0,
          };
          if (children.length > 0) {
            menuItems.push({
              ...base,
              children,
            });
          } else {
            menuItems.push(base);
          }
        });
      }
      return menuItems;
    },
    makeSidebarMenuUrlMapper(menuItemList) {
      const mapper = {};
      if (menuItemList && Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          mapper[item.code] = item.url;
          let childrenMapper = false;
          if (item.nextMenuList && Array.isArray(item.nextMenuList)) {
            childrenMapper = this.makeSidebarMenuUrlMapper(item.nextMenuList);
          }
          if (childrenMapper !== false) {
            Object.assign(mapper, childrenMapper);
          }
        });
      }
      return mapper;
    },
    makeOperations(menuItemList) {
      let operations = [];
      if (Array.isArray(menuItemList)) {
        menuItemList.forEach((item) => {
          if (Array.isArray(item.menuOperationList)) {
            const currOperations = item.menuOperationList.map((menuOperationItem) => menuOperationItem.code);
            operations = operations.concat(currOperations);
          }
          if (Array.isArray(item.nextMenuList)) {
            const currOperations = this.makeOperations(item.nextMenuList);
            operations = operations.concat(currOperations);
          }
        });
      }
      return operations;
    },
    handleOpenChange(openKeys) {
      this.$nextTick(() => {
        this.currentOpenKeys = openKeys;
      });
      this.$emit('open-change', openKeys);
    },
    handleSelect({ key }) {
      this.$nextTick(() => {
        this.currentSelectedKeys = [key];
      });
      this.$emit('select', { key });
    },
    togglePay(item) {
      if (item.open) {
        this.$set(item, 'open', false);
        // item.open = false;
      } else {
        this.$set(item, 'open', true);
        // item.open = true;
      }
    },
    itemSelect(key, parentKey, target) {
      if (target === '_self') {
        this.$nextTick(() => {
          this.currentSelectedKeys = [key];
          this.currentOpenKeys = [parentKey];

          this.menu.forEach((item) => {
            if (item.key === this.currentOpenKeys[0]) {
              this.$set(item, 'open', true);
              // item.open = true;
            } else {
              this.$set(item, 'open', false);
              // item.open = false;
            }
          });
        });
      }

      // if (parentKey) {
      // console.log('parentKey', parentKey);
      // }

      // console.log('itemSelect', key);
      this.$emit('select', { key, target });
    },
  },
};
</script>

<style lang="less" scoped>
@selectedColor: #8D0306;
@activeColor: #8D0306;

ul{
  padding: 0;
}
.sidebar-header {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 15px;
  background: #fff;

  .sidebar-title{
    flex: 1;
    font-size: 14px;
    color: rgba(0, 0, 0, .85);
    font-weight: 500;
  }

  .fold-icon{
    font-size: 18px;
    cursor: pointer;
  }
}

.menu {
  background: transparent;
  font-size: 14px;
  color: rgba(0, 0, 0, .65);
  border-right: none;

  .ant-menu-item {
    height: auto;
    line-height: 1;
    padding-top: 17px;
    padding-bottom: 17px;
    margin: 0;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;

    ::v-deep .ant-menu-submenu-title {
      background: green;
      .ant-menu-submenu-arrow {
        &::before {
          background: #9FB4CB;
        }

        &::after {
          background: #9FB4CB;
        }
      }
    }

    &::after {
      transform: scaleY(1);
      opacity: 1;
      display: none;
    }
    &:hover, &.ant-menu-item-active {
      color: @activeColor;
    }

    &.ant-menu-item-selected {
      background-color: #FAE2E3;
      color: rgba(0, 0, 0, .85);
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: auto;
        display: block;
        width: 3px;
        height: 50px;
        background: @selectedColor;
      }
    }
    .menu-icon{
      font-size: 16px;
      vertical-align: middle;
      color: #171717;
    }
  }

  .ant-menu-submenu {
    ::v-deep .ant-menu-sub {
      display: none;
      .ant-menu-item {
        &.ant-menu-item-selected {
          &::after {
            display: block;
            border-color: @selectedColor;
          }
        }

        &.ant-menu-item-active {
          color: rgba(0, 0, 0, .85);
        }
      }
    }

    &.ant-menu-submenu-open{
      ::v-deep .ant-menu-sub {
        display: block;
      }
    }

    &.ant-menu-submenu-active {
      ::v-deep .ant-menu-submenu-title {
        color: rgba(0, 0, 0, .85);

        .menu-icon {
          color: rgba(0, 0, 0, .85);
        }

        .ant-menu-submenu-arrow {
          &::before {
            background: @activeColor;
          }

          &::after {
            background: @activeColor;
          }
        }
      }
    }

    &.ant-menu-submenu-selected {
      ::v-deep .ant-menu-submenu-title {
        color: rgba(0, 0, 0, .85);

        .menu-icon {
          color: @selectedColor;
        }

        .ant-menu-submenu-arrow {
          &::before {
            background: @selectedColor;
          }

          &::after {
            background: @selectedColor;
          }
        }
      }
    }

    ::v-deep .ant-menu-submenu-title {
      height: auto;
      line-height: 1;
      padding-top: 13px;
      padding-right: 36px;
      padding-bottom: 13px;
      margin: 0;

      .ant-menu-submenu-arrow {
        right: 20px;
      }
    }

    ::v-deep .ant-menu-sub {
      background-color: #f6f8fc;

      .ant-menu-item {
        font-size: 12px;
        color: rgba(0, 0, 0, .65);
        padding-top: 14px;
        padding-bottom: 14px;
        padding-left: 36px;

        &.ant-menu-item-selected {
          color: rgba(0, 0, 0, .85);
        }
      }

      .ant-menu-item-selected {
        background-color: #F5F5F5;
      }
    }
  }
}
.ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical-left>.ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical-left>.ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical-right>.ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical-right>.ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical>.ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical>.ant-menu-submenu-title .ant-menu-submenu-arrow:before{
  width: 4px;
}
.last-time {
  position: absolute;
  right: 5px;
  top: 15px;
  font-size: 12px;
}
::v-deep .ant-badge-count {
  background: #D63C3C;
  min-width: 17px;
  height: 17px;
  line-height: 17px;
}
</style>
