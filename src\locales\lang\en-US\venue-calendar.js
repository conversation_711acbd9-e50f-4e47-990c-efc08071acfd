const venueCalendar = {
  title: 'Venue Calendar',
  simpleSearch: {
    form: {
      label: {
        appointmentDate: 'Booking dates',
        venueCate: 'Venue category',
      },
      selectPlaceHolder: 'Please select',
    },
  },
  tableList: {
    columns: {
      period: 'Time period',
      venue: 'Venue',
    },
  },

  modalReserve: {
    title: 'Booking Information',
    titleView: 'Booking Details',
    form: {
      label: {
        venueNameTime: 'Appointment of venue time',
        venueName: 'Book venue',
        reserveTime: 'Booking time',
        appointerName: 'Name of booking person',
        appointerNumber: 'Work ID of booking person',
        department: 'Department',
        departmentCollege: 'Department/School',
        contact: 'Telephone contact',
        activityName: 'Name of activity',
        applicationInstructions: 'Description of application',

        sn: 'Application number',
        createTime: 'Application time',
        // venueName: '预约场馆', book venue
        reservationDate: 'Booking time',
        userName: 'Name of booking person',
        userPhone: 'Telephone contact',
        createUserName: 'Operator',

        signinTime: 'Sign-in time',
        cancelTime: 'Cancel time',
        remark: 'Remarks',
        enterPlace: 'Please enter',
      },
    },
    action: {
      submit: 'Submit Booking',
    },
    authError: 'You temporarily do not have booking rights, if you have any questions please contact the manager',
  },
  verifySearchPlace: `Please key in your booking number here or use the scanner to
scan the QR code of the booking certificate`,
  verifyTableTips: 'Can click the area with content to check booking details',
  status: {
    pending: 'Pending Signature',
    signed: 'Signed',
    invalid: '已失效',
  },
  msg: {
    reserveError: '请选择同一场馆的连续时段',
    reserveTimeError: '请选择连续时段',
    noTimeError: '请先选择预约时段',
  },
  btn: {
    reserve: '预约',
  },
};

export default venueCalendar;
