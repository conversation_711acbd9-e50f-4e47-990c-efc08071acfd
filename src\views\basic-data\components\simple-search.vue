<template>
  <a-form
    :form="form"
    :layout="'inline'"
    label-align="right"
    :colon="false"
  >
    <a-form-item
      :label="$t('basicData.columns.category')"
    >
      <a-select
        v-decorator="['venueCategoryIds', {
          initialValue: filter.venueCategoryIds,
        }]"
        :get-popup-container="
          triggerNode => {
            return triggerNode.parentNode || document.body;
          }
        "
        :options="venueCategoryOptions"
        style="width: 250px;"
        @change="onInputChange"
      />
    </a-form-item>
    <a-form-item>
      <a-input
        v-decorator="['name', {
          initialValue: filter.name,
        }]"
        allow-clear
        style="width: 250px;"
        :placeholder="$t('basicData.form.keywordPlace')"
        @pressEnter="handleSearch"
        @change="debounce(onInputChange, 1000)()"
      >
        <span
          slot="prefix"
          class="iconfont icon-all_sousuo"
          @click="handleSearch"
        />
      </a-input>
    </a-form-item>

    <!-- <a-form-item>
      <a-button
        html-type="submit"
        type="primary"
      >
        {{ $t('action.search') }}
      </a-button>
    </a-form-item>
    <a-form-item>
      <a-button
        type="default"
        @click="resetSearch"
      >
        {{ $t('action.reset') }}
      </a-button>
    </a-form-item> -->
  </a-form>
</template>
<script>
import { hasOwn } from '@utils/core';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, ...args);
    }, wait);
  };
}

export default {
  name: 'SimpleSearch',
  props: {
    venueCategoryOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    this.form = this.$form.createForm(this);
    return {
      filter: {
        venueCategoryIds: '',
        name: '',
      },
    };
  },
  methods: {
    debounce,
    onInputChange() {
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    resetSearch() {
      this.form.resetFields();
      Object.keys(this.filter).forEach((k) => {
        this.filter[k] = '';
      });
      this.$emit('reset-search', {
        page: 1,
        pageSize: 10,
        filter: {
          venueCategoryIds: '',
          ...this.filter,
        },
      });
    },
    handleSearch() {
      this.form.validateFields((err, values) => {
        if (!err) {
          Object.keys(values).forEach((k) => {
            if (hasOwn(this.filter, k)) {
              this.filter[k] = values[k];
            }
          });
          const payload = {
            ...this.filter,
            ...values,
          };
          this.$emit('handle-search', {
            page: 1,
            filter: payload,
          });
        }
      });
    },
  },
};
</script>
