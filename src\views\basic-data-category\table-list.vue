<template>
  <a-table
    :data-source="dataSource"
    :loading="isLoading"
    :columns="columns"
    :row-key="record => record.id"
    :pagination="pagination"
    class="pima-table"
    :scroll="{ y: 'calc(100vh - 212px)' }"
  >
    <template
      slot="venueVos"
      slot-scope="text"
    >
      <span>{{ text.map(e => e.name).join('、') }}</span>
    </template>
    <template
      slot="updateTime"
      slot-scope="text, record"
    >
      {{ record.updateUserName }} <br>
      {{ $moment(text).format('YYYY-MM-DD HH:mm') }}
    </template>
    <template
      slot="isEnable"
      slot-scope="text"
    >
      <span :class="[text ? 'status_success' : 'status_error']">
        {{ getIsEnbaleMapText(text) }}
      </span>
    </template>
    <template
      slot="operate"
      slot-scope="text, record"
    >
      <a
        v-if="hasP(P => P.BasicData.Category)"
        class="action_link"
        @click="handleEdit(record)"
      >
        {{ $t('action.edit') }}
      </a>
      <a
        v-if="hasP(P => P.BasicData.Category) && record.isCanRemove"
        class="action_link"
        @click="handleRemove(record)"
      >
        {{ $t('action.del') }}
      </a>
    </template>
  </a-table>
</template>

<script>
import OpreationMixin from '@/mixins/operation';
import { AUTO_ADD_TYPE } from '@/constants/venue';
import {
  getIsEnbaleMapText,
  getVenueCategoryI18Text,
} from '../basic-data/components/handler';

export default {
  name: 'BlackRuleList',

  mixins: [OpreationMixin],

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: true,
    },
    pageInfo: {
      type: Object,
      default: () => ({
        page: 1,
        limit: 10,
      }),
    },
    totalSize: {
      type: Number,
      default: 0,
    },
    reservationTypeOptions: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      AUTO_ADD_TYPE,
    };
  },

  computed: {
    pagination() {
      const self = this;
      const showQuickJumper = this.totalSize / this.pageInfo.limit > 1;
      if (this.totalSize < 10) {
        return false;
      }
      return {
        showQuickJumper,
        showSizeChanger: true,
        current: self.pageInfo.page,
        defaultPageSize: self.pageInfo.limit,
        total: self.totalSize || 0,
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, limit) {
          self.$emit('pageChange', page, limit);
        },
        showTotal(total) {
          self.total = total;
          const totalPage = Math.ceil(self.totalSize / self.pageInfo.limit);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        onShowSizeChange(cur, size) {
          self.$emit('pageChange', cur, size);
        },
      };
    },
    columns() {
      return [
        {
          title: this.$t('basicData.columns.categoryName'),
          dataIndex: 'name',
          scopedSlots: { customRender: 'name' },
          align: 'left',
        },
        {
          title: this.$t('basicData.columns.venueVos'),
          dataIndex: 'venueVos',
          scopedSlots: { customRender: 'venueVos' },
          align: 'left',
        },
        {
          title: this.$t('basicData.columns.status'),
          dataIndex: 'isEnable',
          scopedSlots: { customRender: 'isEnable' },
          align: 'left',
        },
        {
          title: this.$t('basicData.columns.updateTime'),
          dataIndex: 'updateTime',
          scopedSlots: { customRender: 'updateTime' },
          align: 'left',
        },
        {
          title: this.$t('basicData.columns.operate'),
          scopedSlots: { customRender: 'operate' },
          align: 'left',
          width: 105,
        },
      ];
    },
  },
  methods: {
    getIsEnbaleMapText(progress) {
      return getIsEnbaleMapText(this, progress);
    },
    getVenueCategoryI18Text(progress) {
      return getVenueCategoryI18Text(progress, this.venueCategoryMap);
    },
    handleEdit(record) {
      this.$emit('edit', record);
    },
    handleRemove(record) {
      this.$emit('remove', record);
    },
    getReservationType(code) {
      const type = this.reservationTypeOptions.filter((item) => item.code === code);
      if (type.length) {
        return type[0].label;
      }
      return '';
    },
  },
};
</script>
