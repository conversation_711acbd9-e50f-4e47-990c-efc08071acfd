import axios from 'axios';
import config from '../config';

const localeMap = {
  'zh-CN': 'zh_CN',
  'zh-HK': 'zh_HK',
  'en-US': 'en_US',
};
function localeInterceptor(requestConfig) {
  const locale = localeMap[window.appLocale] || config.fallbackLocale || 'en_US';
  const params = { lang: locale };
  if (Object.prototype.hasOwnProperty.call(requestConfig, 'params')) {
    Object.assign(requestConfig.params, {
      ...requestConfig.params,
      ...params,
    });
  } else {
    Object.assign(requestConfig, { params });
  }
  return requestConfig;
}

const baseConfig = {
  baseURL: config.serviceApiBaseUrl,
  timeout: config.apiTimeout,
  validateStatus: (status) => (status === 200 || status === 201),
};

const request = axios.create(baseConfig);

request.interceptors.request.use((requestConfig) => {
  // eslint-disable-next-line no-param-reassign
  requestConfig.headers['X-Requested-With'] = 'XMLHttpRequest';
  return requestConfig;
}, (err) => Promise.reject(err));

request.interceptors.request.use((requestConfig) => localeInterceptor(requestConfig));

request.interceptors.response.use((resp) => resp, (err) => {
  const oErr = err;
  if (err && err.response) {
    switch (err.response.status) {
      case 400: oErr.message = '请求错误'; break;
      case 401: oErr.message = '未授权，请重新登录'; break;
      case 403: oErr.message = '拒绝访问'; break;
      case 404: oErr.message = '请求出错'; break;
      case 408: oErr.message = '请求超时'; break;
      case 500: oErr.message = '服务器错误'; break;
      case 501: oErr.message = '服务未实现'; break;
      case 502: oErr.message = '网络错误'; break;
      case 503: oErr.message = '服务不可用'; break;
      case 504: oErr.message = '网络超时'; break;
      default: oErr.message = err.response.data.errorList.message;
    }
  } else {
    oErr.message = '网络异常，请检查网络连接后重试';
  }
  return Promise.reject(oErr);
});

request.interceptors.response.use((resp) => {
  if (resp.config.responseType !== 'blob') {
    if (resp.data && !resp.data.success) {
      const error = {
        code: resp.data.errorCode,
        message: resp.data.errorMsg,
      };
      return Promise.reject(error);
    }
  }
  return resp;
}, (err) => Promise.reject(err));

export default request;

export const blobDownload = (content, name = 'excel') => {
  const blob = new Blob([content]);// 构造一个blob对象来处理数据
  const fileName = `${name}.xlsx`;
  // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
  // IE10以上支持blob但是依然不支持download
  if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
    const link = document.createElement('a');// 创建a标签
    link.download = fileName;// a标签添加属性
    link.style.display = 'none';
    link.href = URL.createObjectURL(blob);
    document.body.appendChild(link);
    link.click();// 执行下载
    URL.revokeObjectURL(link.href); // 释放url
    document.body.removeChild(link);// 释放标签
  } else { // 其他浏览器
    navigator.msSaveBlob(blob, fileName);
  }
};
