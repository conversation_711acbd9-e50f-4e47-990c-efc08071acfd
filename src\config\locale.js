export const Locale = Object.freeze({
  ZH_CN: 'zh-CN',
  EN_US: 'en-US',
  ZH_HK: 'zh-HK',
});

const supportLocales = [];
if (process.env.SUPPORT_LOCALES) {
  const locales = process.env.SUPPORT_LOCALES.split(',').map((l) => l.trim());
  supportLocales.push(...locales);
} else {
  const allLocales = [Locale.ZH_CN, Locale.ZH_HK, Locale.EN_US];
  supportLocales.push(...allLocales);
}

export const SUPPORT_LOCALES = Object.freeze(supportLocales);

// 语言
export const FALLBACK_LOCALE = process.env.FALLBACK_LOCALE || Locale.ZH_CN;
