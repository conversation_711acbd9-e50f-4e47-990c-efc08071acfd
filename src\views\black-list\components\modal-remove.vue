<template>
  <PopModal
    :title="t('modalRemove.title')"
    :width="600"
    :visible.sync="visible"
    @close="onToggleVisible(false)"
  >
    <a-spin
      class="pima-spin"
      :spinning="loading"
    >
      <div class="drawer-bd">
        <a-form
          :form="form"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 15 }"
          :colon="false"
          @submit="onSubmit"
        >
          <a-form-item
            required
            :label="t('form.userId')"
          >
            <span>
              {{ model.userName }}({{ model.userNo }})
            </span>
          </a-form-item>

          <a-form-item
            required
            :label="t('form.reservationType')"
          >
            <span>
              {{ model.type }}
            </span>
          </a-form-item>

          <a-form-item
            :label="t('form.removeReason')"
          >
            <a-textarea
              v-decorator="['removeReason', {
                initialValue: '',
                rules: [
                  { required: true, message: t('place.removeReason') },
                ]
              }]"
              :max-length="200"
              :auto-size="{ minRows: 3, maxRows: 3 }"
              :placeholder="t('place.reason')"
            />
          </a-form-item>
        </a-form>

        <div class="clearfix drawer-ft">
          <a-button @click="onToggleVisible(false)">
            {{ ti('action.close') }}
          </a-button>

          <a-button
            type="primary"
            :loading="submitting"
            @click="onSubmit"
          >
            {{ ti('action.ok') }}
          </a-button>
        </div>
      </div>
    </a-spin>
  </PopModal>
</template>


<script>
import { namespaceT } from '@/helps/namespace-t';

import PopModal from '@/components/base/pop-modal.vue';

export default {
  name: 'ModalRemove',

  components: {
    PopModal,
  },

  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      t: namespaceT('blacklist'),
      ti: namespaceT(),
      visible: false,
      loading: false,
      model: {
        userName: null,
        userNo: null,
        type: null,
      },

    };
  },

  methods: {
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            data: {
              ...values,
            },
            id: this.model.id,
          };
          this.$emit('on-submit', payload);
        }
      });
    },

    onToggleVisible(flag) {
      this.visible = flag;
    },

    setModel(model) {
      this.model = model;
    },


  },
};
</script>
