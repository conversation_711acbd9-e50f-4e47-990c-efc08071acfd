.ant-layout {
  background-color: #fff;
}
.ant-page-header {
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #EFEFEF;
}
.ant-page-header-heading .ant-page-header-heading-tags {
  line-height: 50px;
}
.ant-page-header-heading .ant-page-header-heading-tags .iconfont {
  color: #171717;
  font-size: 14px;
  cursor: pointer;
}
.ant-page-header-heading-title {
  line-height: 50px;
  font-weight: bold;
  font-size: 16px;
}
.ant-page-header-heading-extra {
  color: #D0D0D0;
  font-size: 12px;
}
.ant-page-header-heading-extra .btn_upload_link {
  vertical-align: middle;
}
.ant-page-header-heading-extra .btn_upload_link .ant-upload-list {
  display: inline-block;
}
.ant-page-header-heading-extra > .ant-btn {
  font-size: 16px;
  vertical-align: middle;
}
.ant-page-header-heading-extra > .ant-btn-link {
  margin-left: 0;
}
.tree-panel .ant-page-header-heading-extra {
  font-size: 16px;
}
.content-panel .ant-card-body {
  padding: 0;
}
.ant-card-head {
  padding: 0;
  font-size: 16px;
  font-weight: bold;
}
.ant-card-head-title {
  padding: 0;
  height: 48px;
  line-height: 48px;
}
.ant-input-affix-wrapper .ant-input:not(:first-child) {
  padding-left: 35px;
}
.ant-input-prefix .iconfont {
  font-size: 16px;
  color: #171717;
}
.ant-list-items {
  border-bottom: 1px solid #EFEFEF;
}
.ant-list-split .ant-list-item {
  border-bottom: none;
}
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header,
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header > .arrow {
  cursor: default;
}
.ant-collapse {
  background-color: #FCFCFC;
  border-color: #EFEFEF;
  border-radius: 0;
}
.ant-collapse > .ant-collapse-item {
  border-color: #EFEFEF;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding-left: 8px;
  background-color: #fff;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header .ico {
  padding: 0;
  transform: rotate(-90deg);
  vertical-align: 0;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header .ico .iconfont {
  font-size: 12px;
  color: #171717;
  transform: scale(0.4);
}
.ant-collapse > .ant-collapse-item.ant-collapse-item-active .ico {
  transform: rotate(0deg);
}
.ant-collapse > .ant-collapse-item .ant-collapse-content {
  background: none;
  border-color: #EFEFEF;
}
.ant-row::before,
.ant-row::after {
  clear: both;
}
.ant-form-item-label {
  line-height: 32px;
}
.ant-form-item-control {
  line-height: 32px;
}
.ant-form-horizontal .ant-form-item {
  margin-bottom: 12px;
}
.ant-form-horizontal .ant-form-explain {
  padding-top: 5px;
  font-size: 12px;
  color: #D63C3C;
}
.ant-form-horizontal .ant-form-item-label {
  margin-right: 15px;
}
.ant-form-horizontal .ant-form-item-label.ant-col {
  margin-right: 0;
}
.ant-btn {
  padding: 0 30px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  vertical-align: middle;
  border-radius: 3px;
}
.ant-btn:active,
.ant-btn:hover,
.ant-btn:focus {
  border-color: #8D0306;
  color: #8D0306;
}
.ant-btn-sm {
  padding: 0 15px;
  height: 26px;
  line-height: 26px;
  font-size: 12px;
}
.ant-btn-link {
  padding: 0 10px;
  color: #8D0306;
}
.ant-btn-primary {
  position: relative;
  background-color: #8D0306;
  border-color: #8D0306;
}
.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #8D0306;
  border-color: #8D0306;
}
.ant-btn::before {
  opacity: 0.2;
}
.ant-btn.ant-btn-primary:hover::before {
  display: block;
  background: #ffffff;
}
.ant-btn.ant-btn-primary:active::before {
  display: block;
  background: #000000;
}
.ant-btn.ant-btn-loading::before {
  display: block;
  opacity: 0.5;
}
.ant-btn-primary-disabled,
.ant-btn-primary.disabled,
.ant-btn-primary[disabled],
.ant-btn-primary-disabled:hover,
.ant-btn-primary.disabled:hover,
.ant-btn-primary[disabled]:hover,
.ant-btn-primary-disabled:focus,
.ant-btn-primary.disabled:focus,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary-disabled:active,
.ant-btn-primary.disabled:active,
.ant-btn-primary[disabled]:active,
.ant-btn-primary-disabled.active,
.ant-btn-primary.disabled.active,
.ant-btn-primary[disabled].active {
  background-color: #8D0306;
  border-color: #8D0306;
  color: #fff;
}
.ant-btn-primary-disabled::before,
.ant-btn-primary.disabled::before,
.ant-btn-primary[disabled]::before {
  display: block;
  opacity: 0.5;
}
.ant-btn-primary:hover,
.ant-btn-primary:focus,
.ant-btn-primary:active {
  background-color: #8D0306;
  border-color: #8D0306;
  color: #fff;
}
.ant-btn-link:active,
.ant-btn-link:hover,
.ant-btn-link:focus {
  color: #8D0306;
  border-color: transparent;
}
.ant-btn-default {
  border-color: #8D0306;
  color: #8D0306;
}
.ant-btn-icon-only {
  padding: 0;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
}
.ant-btn-icon-only > i {
  vertical-align: -0.125em;
}
.ant-btn-circle,
.ant-btn-circle-outline {
  border-radius: 50%;
}
.ant-btn .anticon-loading {
  width: 1em;
  height: 1em;
  border: 1px solid #fff;
  border-right-color: transparent;
  border-radius: 50%;
  position: relative;
  animation: loader-rotate 1s linear infinite;
}
.ant-btn .anticon-loading .anticon-spin {
  display: none;
}
@keyframes loader-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ant-tag {
  padding: 0 5px;
  line-height: 18px;
  border-radius: 2px;
}
.ant-input {
  border-color: #EFEFEF;
  font-size: 14px;
  vertical-align: middle;
}
.ant-input:hover {
  border-color: #171717;
}
.ant-input:focus {
  box-shadow: none;
  border-color: #171717;
}
.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: #171717;
}
.ant-input,
.ant-select-selection {
  border-radius: 0;
}
.ant-select {
  vertical-align: middle;
  border-color: #EFEFEF;
  font-size: 14px;
}
.ant-select .anticon {
  font-family: "iconfont" !important;
  font-style: normal;
  font-size: 12px;
  transform: scale(0.5);
  color: #999;
  transition: transform 0.3s;
}
.ant-select .anticon.anticon-down {
  transform: scale(0.4);
}
.ant-select .anticon.anticon-down::after {
  content: "\e64e";
}
.ant-select .anticon.anticon-close {
  transform: scale(0.6);
}
.ant-select .anticon.anticon-close::after {
  content: "\e63f";
}
.ant-select .anticon.anticon-close-circle::after {
  content: "\e63f";
}
.ant-select .anticon svg {
  display: none;
}
.ant-select .ant-select-selection__clear {
  margin-top: -4px;
  background-color: #D6D6D6;
  border-radius: 50%;
}
.ant-select .ant-select-selection__clear .anticon-close-circle {
  color: #fff;
  vertical-align: 0;
}
.ant-select-open .ant-select-arrow-icon.anticon {
  transform: scale(0.4) rotate(180deg);
}
.ant-select-arrow {
  right: 2px;
}
.ant-select-selection {
  border-color: #EFEFEF;
}
.ant-select-selection:hover {
  border-color: #171717;
}
.ant-select-selection--single .ant-select-selection__rendered {
  overflow: hidden;
}
.ant-select-open .ant-select-selection {
  border-color: #171717;
  box-shadow: none;
}
.ant-select-tree li .ant-select-tree-node-content-wrapper {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.ant-select-tree-dropdown {
  max-width: 600px;
}
.ant-select-auto-complete.ant-select .ant-input:focus,
.ant-select-auto-complete.ant-select .ant-input:hover {
  border-color: #171717;
}
.ant-select-focused .ant-select-selection,
.ant-select-selection:focus,
.ant-select-selection:active {
  border-color: #8D0405;
  box-shadow: none;
}
.ant-radio-inner {
  border-radius: 0;
  border-color: #E2E2E2;
}
.ant-radio-checked .ant-radio-inner {
  border-color: #8D0306;
  background-color: #e6effa;
}
.ant-radio-inner::after {
  top: 4px;
  left: 4px;
  width: 6px;
  height: 6px;
  border-radius: 0;
  background-color: #8D0306;
}
.ant-radio-wrapper:hover .ant-radio,
.ant-radio:hover .ant-radio-inner,
.ant-radio-input:focus + .ant-radio-inner {
  border-color: #8D0306;
}
.ant-radio-checked .ant-radio-inner {
  background-color: #faefe6;
}
.ant-radio-input:focus + .ant-radio-inner {
  box-shadow: 0 0 0 3px rgba(255, 144, 24, 0.08);
}
.pima-new-form.ant-form-horizontal .ant-radio-group-default {
  height: 33px;
}
.pima-new-form .ant-radio-inner {
  border-radius: 50%;
  width: 22px;
  height: 22px;
  border-color: #E2E2E2;
}
.pima-new-form .ant-radio-checked .ant-radio-inner {
  border-color: #8D0306;
  background-color: #fff;
  border-width: 7px;
}
.pima-new-form .ant-radio-inner::after {
  display: none;
  top: 4px;
  left: 4px;
  width: 6px;
  height: 6px;
  background-color: #8D0306;
}
.pima-new-form .ant-radio-group {
  display: flex;
}
.pima-new-form .ant-radio-wrapper:hover .ant-radio,
.pima-new-form .ant-radio:hover .ant-radio-inner,
.pima-new-form .ant-radio-input:focus + .ant-radio-inner {
  border-color: #8D0306;
}
.pima-new-form .ant-radio-wrapper {
  display: flex;
  align-items: center;
}
.ant-checkbox-inner::after {
  left: 30%;
}
.ant-checkbox-checked::after {
  border-color: #8D0306;
}
.ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: #8D0306;
}
.ant-checkbox-wrapper {
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
}
.ant-checkbox-wrapper .ant-checkbox + span {
  padding-left: 4px;
}
.ant-checkbox-wrapper .ant-checkbox .ant-checkbox-inner {
  border-color: #E2E2E2;
  border-radius: 0;
  width: 22px;
  height: 22px;
}
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #8D0306;
}
.ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
  background-color: #F5F5F5;
}
.ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled) {
  background-color: #F5F5F5;
}
.ant-select-dropdown-menu-item-selected {
  color: #8D0306;
}
.ant-message {
  top: 56px;
}
.ant-message .anticon {
  display: none;
}
.ant-message .ant-message-notice-content {
  overflow: hidden;
  padding: 0;
}
.ant-message .ant-message-notice-content .ant-message-custom-content {
  padding: 12px 76px;
}
.ant-message .ant-message-notice-content .ant-message-error {
  background-color: #D63C3C;
  color: #fff;
}
.ant-message .ant-message-notice-content .ant-message-success {
  background-color: #0AAF60;
  color: #fff;
}
.ant-table {
  color: rgba(0, 0, 0, 0.85);
}
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 13px 16px;
}
.ant-table-thead > tr > th {
  border-bottom: 2px solid #EFEFEF;
  background: none;
  font-weight: bold;
}
.ant-table-tbody > tr > td {
  border-bottom: 1px solid #EFEFEF;
}
.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #f5f5f5;
}
.ant-pagination.ant-table-pagination {
  margin-right: 24px;
}
.ant-pagination .ant-pagination-item-1 {
  overflow: hidden;
  border-radius: 2px 0 0 2px;
}
.ant-pagination li:nth-last-child(3) {
  overflow: hidden;
  border-radius: 0 2px 2px 0;
}
.ant-pagination .ant-select-selection--single {
  height: 40px;
}
.ant-pagination .ant-select-selection__rendered {
  line-height: 38px;
}
.ant-pagination .ant-pagination-options-quick-jumper input {
  height: 40px;
  border-radius: 2px;
  border-color: #EFEFEF;
}
.ant-pagination .ant-pagination-options-quick-jumper input:focus {
  border-color: #171717;
  box-shadow: none;
}
.ant-pagination .ant-pagination-options-quick-jumper input:hover {
  border-color: #171717;
}
.ant-pagination-total-text {
  margin-right: 24px;
}
.ant-pagination-item {
  margin-right: -1px;
  min-width: 40px;
  height: 40px;
  line-height: 38px;
  border-color: #EFEFEF;
  border-radius: 0;
}
.ant-pagination-item:focus,
.ant-pagination-item:hover {
  border-color: #EFEFEF;
}
.ant-pagination-item:focus a,
.ant-pagination-item:hover a {
  background-color: #8D0306;
  color: #fff;
}
.ant-pagination-item-active a {
  background-color: #8D0306;
  color: #fff;
}
.ant-pagination-item-active:focus a,
.ant-pagination-item-active:hover a {
  background-color: #8D0306;
  color: #fff;
}
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  min-width: 40px;
  height: 40px;
  line-height: 38px;
  border-radius: 0;
}
.ant-pagination-prev {
  margin-right: -1px;
}
.ant-pagination-next {
  margin-left: 0;
}
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  margin-right: 0;
}
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  margin-right: -1px;
  border: 1px solid #EFEFEF;
  background-color: #fff;
}
.ant-pagination-jump-prev a,
.ant-pagination-jump-next a {
  display: block;
}
.ant-pagination-jump-prev:focus a .ant-pagination-item-ellipsis,
.ant-pagination-jump-next:focus a .ant-pagination-item-ellipsis {
  opacity: 1;
}
.ant-pagination-jump-prev:hover a,
.ant-pagination-jump-next:hover a {
  background-color: #8D0306;
  color: #fff;
}
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: #fff;
}
.ant-pagination-prev:focus .ant-pagination-item-link,
.ant-pagination-next:focus .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  background-color: #8D0306;
  color: #fff;
  border-color: #EFEFEF;
}
.ant-pagination-disabled a,
.ant-pagination-disabled:hover a,
.ant-pagination-disabled :focus a,
.ant-pagination-disabled .ant-pagination-item-link,
.ant-pagination-disabled:hover .ant-pagination-item-link,
.ant-pagination-disabled:focus .ant-pagination-item-link {
  color: rgba(0, 0, 0, 0.25);
  background-color: #fff;
  border-color: #d9d9d9;
}
.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  padding: 0 15px;
  border-radius: 2px;
  border-color: #EFEFEF;
}
.ant-popover.dark-theme .ant-popover-inner {
  background-color: #E8EFFB;
}
.ant-popover.dark-theme .ant-popover-content .ant-popover-inner-content {
  color: rgba(0, 0, 0, 0.85);
}
.ant-popover.dark-theme .ant-popover-arrow {
  background: #E8EFFB;
}
.ant-popover.dark-theme .ant-popover-placement-right > .ant-popover-content > .ant-popover-arrow,
.ant-popover.dark-theme .ant-popover-placement-rightTop > .ant-popover-content > .ant-popover-arrow,
.ant-popover.dark-theme .ant-popover-placement-rightBottom > .ant-popover-content > .ant-popover-arrow {
  border-bottom-color: #E8EFFB;
  border-left-color: #E8EFFB;
}
.ant-switch {
  background-color: #fff;
  border: 1px solid #B3B3B3;
}
.ant-switch::after {
  width: 14px;
  height: 14px;
  top: 3px;
  left: 26px;
  background-color: #B3B3B3;
}
.ant-switch-checked {
  background-color: #fff;
  border: 1px solid #8D0306;
}
.ant-switch-checked::after {
  width: 14px;
  height: 14px;
  top: 3px;
  background-color: #8D0306;
  left: 17px;
}
.ant-calendar-today .ant-calendar-date {
  color: #8D0306;
  font-weight: bold;
  border-color: #8D0306;
}
.ant-calendar-selected-day .ant-calendar-date {
  background: rgba(254, 236, 230, 0.6);
}
.ant-calendar-date:hover {
  background: rgba(254, 236, 230, 0.6);
  cursor: pointer;
}
.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: #8D0306;
}
.ant-calendar .ant-calendar-ok-btn {
  background-color: #8D0306;
  border-color: #8D0306;
  color: #fff;
}
.ant-calendar .ant-calendar-ok-btn:hover,
.ant-calendar .ant-calendar-ok-btn:focus {
  color: #fff;
  background-color: #8D0306;
  border-color: #8D0306;
}
.ant-calendar-time-picker-select li:hover {
  background: rgba(254, 236, 230, 0.6);
}
.ant-calendar-time-picker-select li:focus {
  color: #8D0306;
}
.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date,
.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
  background: #8D0306;
}
.ant-calendar-range .ant-calendar-in-range-cell::before {
  background-color: rgba(254, 236, 230, 0.6);
}
.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover,
.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover {
  background: #8D0306;
}
.ant-calendar-picker:focus .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: #8D0306;
  box-shadow: none;
}
.ant-fullcalendar-today .ant-fullcalendar-value,
.ant-fullcalendar-month-panel-current-cell .ant-fullcalendar-value {
  box-shadow: 0 0 0 1px rgba(254, 236, 230, 0.6) inset;
}
.ant-fullcalendar-value:hover {
  background: rgba(254, 236, 230, 0.6);
}
.ant-fullcalendar-selected-day .ant-fullcalendar-value,
.ant-fullcalendar-month-panel-selected-cell .ant-fullcalendar-value {
  background: #8D0306;
}
.ant-time-picker-input:hover {
  border-color: #8D0306;
}
.ant-time-picker-panel-select li:hover {
  background: rgba(254, 236, 230, 0.6);
}
.ant-time-picker-panel-select li:focus {
  color: #8D0306;
}
.ant-calendar-header a:hover {
  color: #8D0306;
}
.ant-calendar-year-panel-header a:hover {
  color: #8D0306;
}
.ant-calendar-year-panel-year:hover {
  background: rgba(254, 236, 230, 0.6);
}
.ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year {
  background: #8D0306;
}
.ant-calendar-month-panel-month:hover {
  background: rgba(254, 236, 230, 0.6);
}
.ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month {
  background: #8D0306;
}
.ant-calendar-decade-panel-selected-cell .ant-calendar-decade-panel-decade,
.ant-calendar-decade-panel-selected-cell .ant-calendar-decade-panel-decade:hover {
  background: #8D0306;
}
.ant-calendar-decade-panel-decade:hover {
  background: rgba(254, 236, 230, 0.6);
}
.ant-input-number:hover {
  border-color: #8D0306;
}
.ant-input-number:focus {
  border-color: #8D0306;
  box-shadow: none;
}
.ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: #8D0306;
}
.ant-input-number-input-wrap:focus {
  outline-color: #8D0306;
}
.ant-input-number-focused {
  border-color: #8D0306;
  box-shadow: none;
}
.ant-input-number-disabled:hover {
  border-color: #d9d9d9;
}
#app {
  overflow: hidden;
  word-break: break-word;
}
.blue {
  color: #8D0306;
}
.layout-header {
  height: 40px;
  background-color: #8D0306;
  padding: 0;
  position: relative;
}
.page-layout-sider {
  background-color: #F9F9F9;
  border-right: 1px solid #EFEFEF;
}
.page-panel {
  overflow: hidden;
}
.tree-panel {
  min-height: calc(100vh - 40px);
  background-color: #fff;
  overflow: hidden;
}
.tree-panel .page-header {
  border-bottom: 1px solid #EFEFEF;
  margin-right: -5px;
  margin-bottom: 0;
}
.data-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.tree-panel--body {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 40px - 50px);
}
.tree-panel--body .ant-tree {
  overflow: hidden;
  padding-right: 10px;
}
.content-panel {
  min-height: calc(100vh - 40px);
}
.content-panel .panel-body {
  background-color: #fff;
  height: calc(100vh - 40px - 50px);
  overflow: auto;
  padding: 0 24px;
}
.content-panel .panel-body .footer_btns {
  position: fixed;
  bottom: 0;
  left: 240px;
  width: 100%;
  height: 60px;
  padding-left: 234px;
  line-height: 60px;
  background: #fff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);
  z-index: 99;
}
.content-side {
  border-right: 1px solid #EFEFEF;
}
.content-tip {
  padding: 20px 0;
}
.info-list .info-list--items {
  display: block;
}
.info-list li {
  list-style: none;
  padding: 12px 0;
}
.info-list--label {
  text-align: right;
  color: rgba(0, 0, 0, 0.85);
}
.pima-confrim {
  /* start---垂直居中展示 Modal */
  /* end---垂直居中展示 Modal */
}
.pima-confrim .ant-modal {
  width: 288px !important;
}
.pima-confrim .ant-modal-confirm-body {
  text-align: center;
}
.pima-confrim .ant-modal-confirm-body > .anticon {
  float: none;
  display: block;
  margin: 0 auto 12px;
  line-height: 1;
  font-family: "iconfont" !important;
  font-size: 44px;
  font-style: normal;
  color: #f2890f;
}
.pima-confrim .ant-modal-confirm-body > .anticon::after {
  content: "\e63c";
}
.pima-confrim .ant-modal-confirm-body > .anticon svg {
  display: none;
}
.pima-confrim .ant-modal-confirm-body > .anticon + .ant-modal-confirm-title + .ant-modal-confirm-content {
  margin-left: 0;
}
.pima-confrim .ant-modal-confirm-body .ant-modal-confirm-title {
  font-size: 16px;
  font-weight: normal;
}
.pima-confrim .ant-modal-confirm-body .ant-modal-confirm-content {
  font-size: 12px;
}
.pima-confrim .ant-modal-confirm-btns {
  float: none;
  text-align: center;
}
.pima-confrim .ant-modal-confirm-btns .ant-btn:first-child {
  position: absolute;
  right: 5px;
  top: 0;
  border: none;
  background: url(../assets/img/ico_close.png) no-repeat 50% 50%;
  box-shadow: none;
  padding: 0 15px;
}
.pima-confrim .ant-modal-confirm-btns .ant-btn:first-child span {
  display: none;
}
.pima-confrim .ant-modal-confirm-btns .ant-btn-primary {
  width: 90px;
  height: 30px;
  background-color: #F2890F;
  border-color: #F2890F;
  font-size: 14px;
}
.pima-confrim.ant-modal-success .ant-modal-confirm-body > .anticon {
  color: #8D0306;
}
.pima-confrim.ant-modal-success .ant-modal-confirm-body > .anticon::after {
  content: "\e64c";
}
.pima-confrim.ant-modal-success .ant-modal-confirm-btns .ant-btn:first-child {
  width: auto;
}
.pima-confrim.ant-modal-error .ant-modal-confirm-body > .anticon {
  color: #B13F3F;
}
.pima-confrim.ant-modal-error .ant-modal-confirm-body > .anticon::after {
  content: "\e63d";
}
.pima-confrim.ant-modal-error .ant-modal-confirm-btns .ant-btn:first-child {
  width: auto;
}
.pima-confrim .ant-modal-wrap {
  text-align: center;
}
.pima-confrim .ant-modal-wrap::before {
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
  content: '';
}
.pima-confrim .ant-modal-wrap .ant-modal {
  top: 0;
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}
.no-uploadlist {
  margin: 0;
}
.no-uploadlist .ant-upload-list {
  display: none;
}
.status_success {
  color: #52C41A;
}
.status_error {
  color: #FF4D4F;
}
.action_link {
  padding: 0 2px;
}
.drawer-bd .ant-form {
  padding: 12px 0;
}
.list-filter-2 {
  padding: 5px 0;
  font-size: 14px;
}
.list-filter-2 .ant-form-inline .ant-form-item {
  margin-right: 8px;
}
.list-filter-2 .ant-form label {
  font-size: 14px;
}
.list-filter-2 .ant-input {
  height: 30px;
  font-size: 14px;
}
.list-filter-2 .ant-select {
  font-size: 14px;
}
.list-filter-2 .ant-select-selection--single {
  height: 30px;
}
.list-filter-2 .ant-select-selection__rendered {
  line-height: 26px;
}
.list-filter-2 .submit {
  margin-left: 8px;
}
.drawer-ft {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid #EFEFEF;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}
.drawer_footer {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #EFEFEF;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  border-radius: 0 0 4px 4px;
}
.modal-wrap .ant-modal-close-x {
  width: 36px;
  height: 36px;
  line-height: 36px;
  font-size: 12px;
}
.modal-wrap .ant-modal-header {
  padding: 7px 16px;
  border-color: #EFEFEF;
}
.modal-wrap .ant-modal-header .ant-modal-title {
  font-size: 16px;
  font-weight: bold;
}
.modal-wrap .ant-modal-body {
  padding: 0;
  padding-bottom: 44px;
}
.modal-wrap .ant-modal-body .drawer-bd {
  height: 100%;
  padding: 16px;
}
.modal-wrap .drawer-side {
  height: 100%;
  min-height: 100%;
  overflow: auto;
}
.modal-wrap .drawer-side .ant-tree {
  max-height: 100%;
}
.modal-wrap .drawer-side .tree-panel--body {
  overflow: hidden;
}
.modal-wrap .drawer-main {
  height: 100%;
  overflow: auto;
}
.modal-wrap.modal-wrap-post-select .ant-modal .transfer-list .box-main {
  min-height: 100px;
  height: calc(100vh - 240px);
}
.modal-wrap.modal-wrap-post-select .ant-modal .transfer-target .box-main {
  min-height: 100px;
  height: calc(100vh - 240px);
}
.modal-wrap.modal-wrap-height .ant-modal .ant-modal-body {
  min-height: 100px;
  height: calc(100vh - 120px);
}
.modal-wrap.modal-wrap-scroll .ant-modal-body {
  overflow: auto;
}
.modal-wrap.modal-np .ant-modal-body .drawer-bd {
  padding: 0;
}
.font-12.ant-select {
  font-size: 12px;
}
.font-12 .ant-select-dropdown-menu-item {
  font-size: 12px;
}
.select_in_modal {
  position: relative;
  display: block;
}
.auth-collapse .ant-collapse-content {
  overflow: visible;
}
.high-search {
  margin: 0 0 10px;
  padding: 16px 24px;
  border-bottom: 1px solid #EFEFEF;
  background: #FCFCFC;
}
.high-search .high-search-form {
  margin: 0 auto;
  max-width: 1200px;
}
.high-search .subbox {
  margin-top: 8px;
}
.high-search .ant-btn {
  margin: 0 5px;
}
.mobile-view {
  display: none;
}
@media (max-width: 768px) {
  .PC-view {
    display: none;
  }
  .mobile-view {
    display: block !important;
  }
  .layout-header,
  .page-layout-sider {
    display: none;
  }
}
/* ant css edit*/
a {
  color: #8D0306;
}
a:hover,
a:focus {
  color: #8D0306;
}
a:active {
  color: #8D0306;
}
.goback {
  display: flex;
  align-items: center;
}
.goback .icon {
  display: inline-flex;
  align-items: center;
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  justify-content: center;
  border: 1px solid #d0d0d0;
  margin-right: 12px;
  cursor: pointer;
}
.goback .icon .left {
  width: 5px;
  height: 5px;
  border-top: 1px solid #d0d0d0;
  border-left: 1px solid #d0d0d0;
  transform: rotate(-45deg);
}
