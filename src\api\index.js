import request from './request';

export function getUserData(baseURL, auth, params) {
  return new Promise((resolve, reject) => {
    request({
      baseURL,
      url: 'users/auths',
      method: 'get',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

export function getApplicationData(baseURL, auth, params) {
  return new Promise((resolve, reject) => {
    request({
      baseURL,
      url: 'users/services',
      method: 'get',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

export function getPagesApi(baseURL, params) {
  return new Promise((resolve, reject) => {
    request({
      baseURL,
      url: 'pages',
      method: 'get',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export function getUnreadMsgApi(baseURL, params) {
  return new Promise((resolve, reject) => {
    request({
      baseURL,
      url: 'msgs/unreadCount',
      method: 'get',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
