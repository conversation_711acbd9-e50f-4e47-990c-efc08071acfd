// 配置table columns
export default function getColumns(vm) {
  return [
    {
      title: vm.$t('reserveRule.columns.name'),
      dataIndex: 'name',
    },
    {
      title: vm.$t('reserveRule.columns.venueList'),
      dataIndex: 'venueList',
      scopedSlots: { customRender: 'venueList' },
    },
    {
      title: vm.$t('reserveRule.columns.time'),
      scopedSlots: { customRender: 'time' },
    },
    {
      title: vm.$t('reserveRule.columns.status'),
      dataIndex: 'isEnable',
      scopedSlots: { customRender: 'status' },
    },
    {
      title: vm.$t('reserveRule.columns.operate'),
      scopedSlots: { customRender: 'operate' },
    },
  ];
}
