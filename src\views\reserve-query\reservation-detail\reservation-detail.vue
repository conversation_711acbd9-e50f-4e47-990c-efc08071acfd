<template>
  <div class="page-panel">
    <div class="content-panel">
      <GoBack
        :title="$t('reserveQuery.title.reserveDetail')"
        @back="goBack"
      />
      <div class="panel-body">
        <a-spin
          :spinning="submiting"
          style="overflow:hidden;"
        >
          <div class="panel-container">
            <a-card
              :title="$t('reserveQuery.title.reserveDetail')"
              class="card"
            >
              <div class="approval-status-box">
                <!-- 当前状态 -->
                <div>
                  <span
                    class="status-icon"
                    :class="{
                      'pending': ['pending', 'approving', 'pending_sign'].includes(approvalStatus),
                      'pass': ['pass', 'auto-pass', 'signed'].includes(approvalStatus),
                      'reject': ['reject'].includes(approvalStatus),
                      'cancel': ['cancel', 'off'].includes(approvalStatus),
                    }"
                  />
                  <span
                    class="approve-status"
                    :class="{
                      'pending': ['pending', 'approving', 'pending_sign'].includes(approvalStatus),
                      'pass': ['pass', 'auto-pass', 'signed'].includes(approvalStatus),
                      'reject': ['reject'].includes(approvalStatus),
                      'cancel': ['cancel', 'off'].includes(approvalStatus),
                    }"
                  >
                    {{ getreserveStatusI18Text(approvalStatus) }}
                    <span v-if="approvalStatus===ReserveStatus.OFF">
                      {{ t('offTime', { count: initData.offCount }) }}
                    </span>
                  </span>
                </div>
                <!-- 申请时间 -->
                <div class="approve-createtime">
                  <span>{{ t('time') }}：</span>
                  <span>{{ formatDate(initData.createTime, 'middle') }}</span>
                </div>
              </div>
              <a-form
                :form="form"
                layout="vertical"
                class="form-info"
                :colon="false"
              >
                <h2 class="title">
                  {{ t('info') }}
                </h2>
                <div v-if="OrderTypeMap.ACTIVITY === initData.reservationType">
                  <!-- 申请单号 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('sn') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.sn }}
                    </span>
                  </a-form-item>
                  <!-- 预约场馆 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('venue') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.venueName }}
                    </span>
                  </a-form-item>
                  <!-- 预约时间 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('orderTime') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ formatDate(initData.reservationDate, 'short') }}
                      {{ initData.startTime }}
                      <span v-if="initData.endTime">-</span>
                      {{ initData.endTime }}
                    </span>
                  </a-form-item>

                  <!-- 使用积分 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('integralUsed') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.costPoints || EMPTY_TEXT }}
                    </span>
                  </a-form-item>

                  <!-- 是否学生组织预约 -->
                  <a-form-item v-if="!initData.isAdmin && initData.isNeedApproval">
                    <template #label>
                      <span class="item-title">{{ t('isStudentReservation') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ getIsStudentReservationText(initData.isStudentReservation) }}
                    </span>
                  </a-form-item>
                  <template v-if="initData.isStudentReservation && !initData.isAdmin">
                    <!-- 组织名称 -->
                    <a-form-item>
                      <template #label>
                        <span class="item-title">{{ t('organization') }}</span>
                      </template>
                      <span
                        class="item-value"
                      >
                        {{ initData.activityOrgan }}
                      </span>
                    </a-form-item>
                    <!-- 指导老师 -->
                    <a-form-item v-if="initData.isNeedApproval">
                      <template #label>
                        <span class="item-title">{{ t('teacherName') }}</span>
                      </template>
                      <span
                        class="item-value"
                      >
                        {{ initData.teacherName }}
                      </span>
                    </a-form-item>
                  </template>
                  <!-- 预约人姓名 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('orderPeople') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.userName }}({{ initData.userNo }})
                    </span>
                    <a-button
                      v-if="hasP(P => P.ReserveQuery.Blacklist)
                        && approvalStatus===ReserveStatus.OFF
                        && !initData.isBlacklist"
                      type="primary"
                      style="padding: 0 10px;margin-left: 10px"
                      @click="handleShowBlackListModel"
                    >
                      {{ t('addToBlackList') }}
                    </a-button>
                  </a-form-item>
                  <!-- 部门名称 -->
                  <a-form-item v-if="initData.isAdmin">
                    <template #label>
                      <span class="item-title">{{ t('belongTo') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.deptName }}
                    </span>
                  </a-form-item>
                  <!-- 联系电话 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('phone') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.userPhone }}
                    </span>
                  </a-form-item>
                  <!-- 活动名称 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('activityName') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.activityName }}
                    </span>
                  </a-form-item>
                  <!-- 活动参与人数 -->
                  <a-form-item v-if="!initData.isAdmin">
                    <template #label>
                      <span class="item-title">{{ t('activityPeoples') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.activityPeoples }}
                    </span>
                  </a-form-item>
                  <!-- 外来嘉宾 -->
                  <a-form-item
                    v-if="!initData.isAdmin"
                    class="block-content"
                  >
                    <template #label>
                      <span class="item-title">{{ t('activityGuests') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.activityGuests }}
                    </span>
                  </a-form-item>
                  <!-- 申请说明 -->
                  <a-form-item class="block-content">
                    <template #label>
                      <span class="item-title">{{ t('note') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.notes }}
                    </span>
                  </a-form-item>
                  <!-- 操作人 -->
                  <a-form-item v-if="initData.isAdmin && initData.createUserName">
                    <template #label>
                      <span class="item-title">{{ t('createUserName') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.createUserName }}
                    </span>
                  </a-form-item>
                </div>
                <div v-else>
                  <!-- 申请单号 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('sn') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.sn }}
                    </span>
                  </a-form-item>
                  <!-- 预约场馆 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('venue') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.venueName }}
                    </span>
                  </a-form-item>
                  <!-- 预约时间 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('orderTime') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ formatDate(initData.reservationDate, 'short') }}
                      {{ initData.startTime }}
                      <span v-if="initData.endTime">-</span>
                      {{ initData.endTime }}
                    </span>
                  </a-form-item>

                  <!-- 使用积分 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('integralUsed') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.costPoints || EMPTY_TEXT }}
                    </span>
                  </a-form-item>

                  <!-- 预约人姓名 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('orderPeople') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.userName }}({{ initData.userNo }})
                    </span>
                    <a-button
                      v-if="hasP(P => P.ReserveQuery.Blacklist)
                        && approvalStatus===ReserveStatus.OFF
                        && !initData.isBlacklist"
                      type="primary"
                      style="padding: 0 10px;margin-left: 10px"
                      @click="handleShowBlackListModel"
                    >
                      {{ t('addToBlackList') }}
                    </a-button>
                  </a-form-item>
                  <!-- 所属部门 -->
                  <!-- <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('belongTo') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.deptName }}
                    </span>
                  </a-form-item> -->
                  <!-- 联系电话 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('phone') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.userPhone }}
                    </span>
                  </a-form-item>
                  <!-- 活动名称 -->
                  <a-form-item v-if="initData.activityName">
                    <template #label>
                      <span class="item-title">{{ t('activityName') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.activityName }}
                    </span>
                  </a-form-item>
                  <!-- 申请说明 -->
                  <a-form-item>
                    <template #label>
                      <span class="item-title">{{ t('note') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.notes }}
                    </span>
                  </a-form-item>
                  <!-- 操作人 -->
                  <a-form-item v-if="initData.isAdmin && initData.createUserName">
                    <template #label>
                      <span class="item-title">{{ t('createUserName') }}</span>
                    </template>
                    <span
                      class="item-value"
                    >
                      {{ initData.createUserName }}
                    </span>
                  </a-form-item>
                </div>
              </a-form>
            </a-card>
            <step-list
              :process-steps="initSteps"
              :type="initData.reservationType"
              class="operation-log"
            />
          </div>
        </a-spin>
        <div>
          <div
            v-if="hasP(P => P.ReserveQuery.Cancel) && isCancelAble(approvalStatus)"
            class="footer_btns"
          >
            <a-button
              type="primary"
              style="margin-left: 10px;"
              @click="goCancelReserve()"
            >
              <span>
                {{ $t('reserveQuery.action.cancel') }}
              </span>
            </a-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 取消预约Modal -->
    <CancelModal
      :id="id"
      ref="CancelModal"
      v-model="isShowCancelModal"
      :submiting="formLoading"
      @handle-cancel-submit="handleCancelSubmit"
    />
    <!--  -->
    <AddBlackListModel
      v-model="isShowBlacklistModal"
      :user="user"
      @update="fetchDetail"
    />
  </div>
</template>

<script>
import { EMPTY_TEXT } from '@/constants/empty-text';
import { nsI18n } from '@/mixins/ns-i18n';
import GoBack from '@components/base/go-back-title.vue';
import operation from '@mixins/operation';
import { formatDate } from '@utils/dateformat';
import { ReserveStatus, OrderTypeMap } from '@/constants/venue';
import stepList from '@components/step-list';
import { getReservationDetailApi, postCancelReservationApi } from '@api/reserve-query-api';
import { getreserveStatusI18Text, getIsStudentReservationText } from '../reservation-query/components/handler';
import CancelModal from '../reservation-query/components/cancel-modal.vue';
import AddBlackListModel from '../reservation-query/components/add-blacklist-model.vue';

const statusColor = {
  pending: '#F49B00',
  pending_sign: '#F49B00',
  approving: '#F49B00',
  pass: '#009944',
  'auto-pass': '#009944',
  signed: '#009944',
  cancel: '#000000',
  off: '#000000',
  reject: '#9B0000',
};

export default {
  name: 'ReservationDetail',
  components: {
    stepList,
    GoBack,
    CancelModal,
    AddBlackListModel,
  },
  mixins: [
    operation,
    nsI18n('t', 'reserveQuery.reserveDetail'),
  ],

  data() {
    this.statusColor = statusColor;
    this.form = this.$form.createForm(this);
    return {
      isShowCancelModal: false,
      isShowBlacklistModal: false,
      ReserveStatus,
      saving: false,
      OrderTypeMap,
      submiting: false,
      formLoading: false,
      initData: {},
      initSteps: {},
      id: 0,
      user: {},
      EMPTY_TEXT,
    };
  },

  computed: {
    approvalStatus() {
      if (this.initData.approvalStatus === 'cancel' && this.initData.cancelType === 'off') {
        return this.initData.cancelType;
      }
      return this.initData.approvalStatus;
    },
  },

  mounted() {
    this.fetchDetail();
  },
  methods: {
    goBack() {
      this.$router.push({
        name: 'ReservationQuery',
        query: null,
      });
    },
    formatDate,
    getIsStudentReservationText(progress) {
      return getIsStudentReservationText(this, progress);
    },
    // 判断是否显示取消预约按钮
    isCancelAble(value) {
      let isCancel;
      switch (value) {
        case ReserveStatus.PENDING:
          isCancel = true;
          break;
        case ReserveStatus.APPROVING:
          isCancel = true;
          break;
        case ReserveStatus.PASS:
          isCancel = true;
          break;
        case ReserveStatus.AUTO_PASS:
          isCancel = true;
          break;
        default:
          isCancel = false;
          break;
      }
      return isCancel;
    },
    // 打开取消预约modal
    goCancelReserve() {
      this.isShowCancelModal = true;
    },
    // 关闭取消预约modal
    closeModal() {
      this.isShowCancelModal = false;
    },
    // 提交编辑表单
    handleCancelSubmit(formData) {
      this.formLoading = true;
      postCancelReservationApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.$t('reserveQuery.msg.submitSuccess'));
          // 关闭modal
          this.closeModal();
          // 刷新页面
          this.fetchDetail();
        })
        .catch(() => {
          this.formLoading = false;
          // 关闭modal
          this.closeModal();
          // 刷新页面
          this.fetchDetail();
        });
    },
    getreserveStatusI18Text(progress) {
      return getreserveStatusI18Text(this, progress);
    },
    fetchDetail() {
    // 消息中心跳转详情
      const id = Number(this.$route.query.id);
      if (id) {
        this.id = id;
        this.submiting = true;
        getReservationDetailApi(id)
          .then(({ model = {} }) => {
            if (model.processSteps) {
              this.initSteps = JSON.parse(model.processSteps);
            }
            this.initData = model;
            this.submiting = false;
          })
          .catch((error) => {
            this.$message.error(error.message);
            this.submiting = false;
          });
      }
    },
    handleShowBlackListModel() {
      this.isShowBlacklistModal = true;
      this.user = {
        name: this.initData.userName,
        reservationType: this.initData.reservationType,
        id: this.initData.userId,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.item-title{
  font-size: 15px;
  font-weight: 300;
  color: rgba(0,0,0,0.65);
  line-height: 21px;
}
.item-value{
  font-size: 15px;
  font-weight: 400;
  color: #222222;
  line-height: 21px;
}
.form-info {
  padding: 20px 105px 40px;
  .title {
    font-size: 18px;
    font-weight: 500;
    color: #9B0000;
    line-height: 25px;
    margin-bottom: 10px;
  }
  ::v-deep .ant-form-item{
    border-bottom: 1px solid #EDEDED;
    padding: 14px 20px;
    display: flex;
    margin-bottom: 0;
    .ant-form-item-label {
      width: 200px;
      text-align: left;
      line-height: 1;
      padding: 0;
    }
    .ant-form-item-control-wrapper {
      flex: 1;
      text-align: right;
      line-height: 1;
    }
  }
  .block-content {
    display: block;
    ::v-deep .ant-form-item-control-wrapper {
      flex: none;
      text-align: left;
      margin-top: 8px;
    }
  }
}
.panel-container {
  display: flex;
  margin-left: 15%;
}
.card {
  // float: left;
  margin-top: 10px;
  // margin-left: 15%;
  width: 850px;
  ::v-deep .ant-card-head {
    border-bottom: 1px solid #9B0000;
  }
  :deep(.ant-card-head-title) {
    height: 85px;
    line-height: 85px;
    font-size: 24px;
    text-align: center;
  }
}
 .panel-body .footer_btns {
  text-align: center;
  text-align: left;
  padding-left: calc(15% + 425px);
  >div {
    display: inline-block;
    transform: translate(-50%);
  }
}
.form-info-PC {
  padding: 20px 105px 0;
}
.mobile-submit {
  width: 100%;
  height: 40px;
}
@media (max-width: 1200px) {
  .panel-body {
    padding-bottom: 80px !important;
  }
  .panel-container {
    display: block;
    margin-left: 0;
    .card {
      width: 100%;
      padding-top: 10px;
      .form-info, .form-info-PC {
        padding: 20px 22px 0;
      }
    }
    .operation-log {
      margin-left: 0;
      margin-top: 10px;
      background-color: #fff;
      padding: 0 23px;
      .title {
        font-size: 16px;
      }
      /deep/ .ant-steps {
        margin-left: 0;
        width: 100%;
      }
    }
  }
   .panel-body .footer_btns {
    padding-left: 0;
    text-align: center;
  }
}
@media (max-width: 768px) {
  .panel-container {
    .card {
      border: 0;
      margin: 0;
      padding: 0;
      .border-content {
        background: #FCFCFC;
        height: 50px;
        line-height: 50px;
        padding: 0 21px 0 21px;
      }
      /deep/ .ant-card-head {
        display: none;
      }
    }
    .operation-log {
      border: 0;
    }
  }
   .panel-body .footer_btns {
    left: 0;
    height: 100px;
    padding-top: 20px;
    >div {
      transform: none;
      .ant-btn:first-child {
        margin-right: 10px;
      }
    }
    .ant-btn {
      width: 157px;
      height: 50px;
    }
  }
}
@media (max-width: 340px) {
  .border-content {
    padding: 0 15px;
  }
   .panel-body .footer_btns {
    .ant-btn {
      width: 140px;
    }
  }
}
.panel-body {
  padding-bottom: 80px !important;
  .operation-log {
  margin-left: 30px;
  margin-top: 95px;
  padding: 0 13px;
  border: 1px solid #f6f6f6;
  border-radius: 3px;
  }
}
</style>
