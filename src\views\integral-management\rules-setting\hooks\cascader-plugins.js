import moment from 'moment';
import { namespaceT } from '@/helps/namespace-t';
import { IntegralSysParams } from '@/constants/integral-management';

export function GetMonthDayOptions() {
  const t = namespaceT('integralManagement');
  const options = [];

  // eslint-disable-next-line no-plusplus
  for (let m = 1; m <= 12; m++) {
    const month = m;
    const monthLabel = t('text.months', { month });
    const totalDays = moment(`2024-${m}`, 'YYYY-M').daysInMonth();
    const children = Array.from({ length: totalDays }, (_, i) => {
      const day = (i + 1);
      return {
        label: t('text.days', { day }),
        value: day,
      };
    });

    options.push({
      label: monthLabel,
      value: month,
      children,
    });
  }
  return options;
}


export function displayRender(e) {
  return e.labels.map((item) => item).join('');
}


function getDataMap(key) {
  const map = new Map([
    ['date', IntegralSysParams.COST_POINTS_REST_DATE],
    ['initialIntegral', IntegralSysParams.DEFAULT_COST_POINTS],
    ['deduceIntegral', IntegralSysParams.APPLY_DEDUCTION_OF_POINTS],
  ]);

  if (map.has(key)) {
    return map.get(key);
  }

  return null;
}

export function handleInitData(list) {
  if (!(Array.isArray(list) && list.length > 0)) {
    return;
  }

  Object.keys(this.initData).forEach((key) => {
    const value = list.find((item) => item.code === getDataMap(key))?.value || null;
    if (key === 'date') {
      this.initData[key] = (value || []).split('-').map((item) => +item);
    } else {
      this.initData[key] = +value;
    }
  });
}


export function handleSubmitData(formData) {
  const paramList  = 
  console.log('%c [ formData ]-68', 'font-size:13px; background:pink; color:#bf2c9f;', formData);
}
