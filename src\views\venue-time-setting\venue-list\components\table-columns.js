/* eslint-disable arrow-body-style */
import { namespaceT } from '@/helps/namespace-t';


export const createTableColumns = () => {
  const t = namespaceT('venueTimeSetting.tableList.columns');
  return [
    {
      title: t('venueNumber'),
      dataIndex: 'sn',
      scopedSlots: { customRender: 'venueNumber' },
    },
    {
      title: t('venueName'),
      scopedSlots: { customRender: 'venueName' },
    },
    {
      title: t('venueCategory'),
      scopedSlots: { customRender: 'venueCategory' },
    },
    {
      title: t('capacity'),
      dataIndex: 'capacity',
    },
    {
      title: t('appointmentRule'),
      scopedSlots: { customRender: 'appointmentRule' },
    },
    {
      title: t('lastAction'),
      scopedSlots: { customRender: 'lastAction' },
    },
    {
      title: t('status'),
      dataIndex: 'isEnable',
      scopedSlots: { customRender: 'status' },
    },
    {
      title: t('action'),
      scopedSlots: { customRender: 'action' },
    },
  ];
};

export default createTableColumns;
