<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.BlackList.Setting)"
      class="content-panel"
    >
      <GoBack
        :title="$t('blacklist.title.setting')"
        @back="handleBack"
      />
      <div class="panel-body">
        <TableList
          :data-source="dataSource"
          :total-size="totalSize"
          :is-loading="isLoading"
          :page-info.sync="queryInfo"
          :reservation-type-options="reservationTypeOptions"
          @pageChange="onPageChange"
          @edit="handleEditblacklist"
          @update="handleTableUpdated"
        />
      </div>
    </div>
    <SettingForm
      v-if="visibles"
      v-model="visibles"
      :data-source="record"
      :reservation-type-options="reservationTypeOptions"
      @update="search"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import GoBack from '@components/base/go-back-title.vue';
import operation from '@mixins/operation';
import { getReservationTypeApi } from '@/api/service-query-api';
import TableList from './table-list.vue';
import SettingForm from './setting-form.vue';

export default {
  name: 'BlackListSetting',

  components: {
    GoBack,
    TableList,
    SettingForm,
  },

  mixins: [operation],

  data() {
    return {
      queryInfo: {
        limit: 10,
        page: 1,
      },
      record: {},
      type: '',
      visibles: false,
      reservationTypeOptions: [],
    };
  },

  computed: {
    ...mapState({
      dataSource: (state) => state.blacklist.ruleList,
      totalSize: (state) => state.blacklist.ruleTotal,
      isLoading: (state) => state.blacklist.ruleLoading,
    }),
  },

  mounted() {
    getReservationTypeApi()
      .then(({ model }) => {
        this.reservationTypeOptions = model.map((item) => ({
          code: item.code,
          value: item.code,
          name: item.name,
          label: item.name,
        }));
      });
    this.search('search');
  },

  methods: {
    handleBack() {
      this.$router.push({
        name: 'balckList',
      });
    },
    onPageChange(page, limit) {
      this.queryInfo.page = page;
      this.queryInfo.limit = limit;
      this.search('changePage');
    },
    handleEditblacklist(record) {
      this.record = record;
      this.type = 'edit';
      this.visibles = true;
    },
    handleTableUpdated(formType) {
      if (formType === 'delete') {
        /* eslint-disable-next-line */
        this.queryInfo.page = this.queryInfo.page > 1
          ? ((this.queryInfo.page - 1) * this.queryInfo.limit < this.totalSize - 1
            ? this.queryInfo.page
            : this.queryInfo.page - 1)
          : 1;
      }
      this.search();
    },
    search(isChangePage) {
      if (isChangePage === 'search') {
        this.queryInfo.page = 1;
      }
      this.$store.dispatch('blacklist/fetchBlackRuleList', {
        ...this.queryInfo,
      });
    },
  },
};
</script>
