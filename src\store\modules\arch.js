import { fetchUsers } from '@api/arch-api';

const initState = {
  users: [], // 获取教职工
};

const mutations = {
  setUsers(state, payload) {
    state.users = payload;
  },
  clearUsers(state) {
    state.users = [];
  },
};

const actions = {
  fetchUserList({ commit }, payload) {
    const params = {
      limit: -1,
      ...payload,
    };
    return new Promise((resolve, reject) => {
      fetchUsers(params)
        .then((res) => {
          commit('setUsers', res.data);
          resolve(res);
        })
        .catch(reject);
    });
  },
};

export default {
  namespaced: true,
  state: initState,
  mutations,
  actions,
};
