/* eslint-disable arrow-body-style */
import TableHeaderPeriod from './table-header-period.vue';
import PeriodProcessItem from './period-process-item.vue';


export const createTableColumns = (vm, columns, venues, selectedDate, selectedTimeList) => {
  // columnIndex为列的下标，在执行customCell方法时手动指定
  const customCell = (record, rowIndex, columnIndex) => {
    const classList = [];
    if (columnIndex === 0) {
      classList.push('td-period-title-wrap');
    } else {
      classList.push('td-period-item-wrap');
    }

    return {
      class: classList,
      on: {
        mouseenter: () => {
          const tds = document.getElementsByClassName(`venue${columnIndex - 1}`);
          for (let i = 0; i < tds.length; i += 1) {
            tds[i].classList.add('bg-hover');
          }
        },
        mouseleave: () => {
          const tds = document.getElementsByClassName(`venue${columnIndex - 1}`);
          for (let i = 0; i < tds.length; i += 1) {
            tds[i].classList.remove('bg-hover');
          }
        },
      },
    };
  };

  const tableColumns = [
    {
      title() {
        return vm.$createElement(TableHeaderPeriod);
      },
      dataIndex: 'period',
      scopedSlots: { customRender: 'period' },
      align: 'center',
      width: 68,
      fixed: 'left',
      customHeaderCell(column) {
        const classList = [];
        classList.push('th-period-title-wrap');
        if (column.key === 'period') {
          classList.push('th-column-first');
        }

        return {
          class: classList,
        };
      },
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 0);
      },
    },
  ];

  if (Array.isArray(columns)) {
    columns.forEach((title, index) => {
      const item = {
        title,
        key: `row-${index}`,
        className: `venue${index}`,
        scopedSlots: { customRender: 'dayPeriod' },
        align: 'center',
        // width: 172,
        customCell(record, rowIndex) {
          return customCell(record, rowIndex, index + 1);
        },
        customRender(text, record, rowIndex) {
          return vm.$createElement(PeriodProcessItem, {
            props: {
              value: record,
              rowIndex,
              columnIndex: index + 1,
              venues,
              selectedDate,
              selectedTimeList,
            },
          }, []);
        },
      };
      if (columns.length > 9) {
        item.width = 172;
      }
      tableColumns.push(item);
    });
  }

  return tableColumns;
};

export default createTableColumns;
