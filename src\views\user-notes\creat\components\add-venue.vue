<template>
  <div class="drawer-bd">
    <a-spin :spinning="submiting">
      <a-form
        :form="form"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 12 }"
        :colon="false"
        @submit="handleSubmit"
      >
        <!-- 中文名 -->
        <a-form-item
          :label="tf('title')"
        >
          <a-input
            v-decorator="['title', {
              initialValue: initData.title,
              rules: [
                { required: true, message: tf('titleTip') },
                { max: 50, message: $t('form.maxLength', {num: 50}) },
              ]
            }]"
          />
        </a-form-item>
        <!-- 英文名 -->
        <a-form-item
          :label="tf('enTitle')"
        >
          <a-input
            v-decorator="['enTitle', {
              initialValue: initData.enTitle,
              rules: [
                { max: 200, message: $t('form.maxLength', {num: 200}) },
              ]
            }]"
          />
        </a-form-item>

        <!-- 正文中文 -->
        <a-form-item
          field-decorator-id="content"
          :field-decorator-options="{
            initialValue: initData.content,
            rules: [
            ],
          }"
          :label="tf('content')"
          :required="true"
        >
          <u-editor
            v-if="hasLoad"
            v-model="initData.content"
            :locale-serviceurl="serverConfig"
          />
          <div
            v-if="contentTip"
            class="ant-form-explain has-error"
          >
            {{ tf('contentTip') }}
          </div>
        </a-form-item>

        <!-- 正文英文 -->
        <a-form-item
          field-decorator-id="enContent"
          :field-decorator-options="{
            initialValue: initData.enContent,
            rules: [
            ],
          }"
          :label="tf('enContent')"
        >
          <u-editor
            v-if="hasLoad"
            v-model="initData.enContent"
            :locale-serviceurl="serverConfig"
          />
        </a-form-item>

        <!-- 附件 -->
        <div
          class="ant-row ant-form-item"
        >
          <div class="ant-col ant-col-3 ant-form-item-label">
            <label
              :title="tf('attachmentIds')"
              class="ant-form-item-no-colon"
            >
              {{ tf('attachmentIds') }}
            </label>
          </div>
          <div
            class="ant-col ant-col-12 ant-form-item-control-wrapper"
            style="position: relative;"
          >
            <div style="position:absolute; left: 130px; top: 5px;">
              <span class="t12">
                {{ $t('userNotes.form.attachmentIdsTip') }}
              </span>
            </div>
            <a-upload
              v-decorator="['withAttIds']"
              class="icon-uploader"
              accept=".xls,.xlsx,.doc,.docx,.pdf"
              :disabled="uploading"
              :file-list="fileList"
              :show-upload-list="{showPreviewIcon: false}"
              :before-upload="beforeUpload"
              :custom-request="handleUploadIconRequest"
              :remove="handleRemoveImg"
            >
              <a-button
                type="primary"
              >
                <a-icon :type="uploading ? 'loading' : 'plus'" />
                {{ uploading ? $t('msg.uploading') : $t('action.upload') }}
              </a-button>
            </a-upload>
          </div>
        </div>
        <!-- <a-form-item
          :label="tf('attachmentIds')"
        >
          <a-upload
            v-decorator="['withAttIds']"
            class="icon-uploader"
            accept=".xls,.xlsx,.doc,.docx,.pdf"
            :disabled="uploading"
            :file-list="fileList"
            :show-upload-list="{showPreviewIcon: false}"
            :before-upload="beforeUpload"
            :custom-request="handleUploadIconRequest"
            :remove="handleRemoveImg"
          >
            <a-button
              type="primary"
            >
              <a-icon :type="uploading ? 'loading' : 'plus'" />
              {{ uploading ? $t('msg.uploading') : $t('action.upload') }}
            </a-button>
            <span class="t12">
              {{ $t('userNotes.form.attachmentIdsTip') }}
            </span>
          </a-upload>
        </a-form-item> -->

        <!-- 排序号 -->
        <a-form-item
          :label="tf('sortOrder')"
        >
          <a-input
            v-decorator="['sortOrder', {
              initialValue: initData.sortOrder,
              rules: [
                { required: true, message: tf('sortOrderTip') },
                { validator: isNumber },
              ]
            }]"
            :place="tf('sortOrderPlace')"
          />
        </a-form-item>

        <!-- 是否启用 -->
        <a-form-item
          :label="tf('isEnable')"
        >
          <a-radio-group
            v-decorator="['isEnable', {
              initialValue: initData.isEnable,
              rules: [
                { required: true, message: tf('isEnable') },
              ]
            }]"
          >
            <a-radio :value="VenueStatus.ENABLED">
              {{ getIsTrueStatusText(VenueStatus.ENABLED) }}
            </a-radio>
            <a-radio :value="VenueStatus.DISABLED">
              {{ getIsTrueStatusText(VenueStatus.DISABLED) }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { VenueStatus } from '@/constants/venue';
import { nsI18n } from '@/mixins/ns-i18n';
import UEditor from '@components/ueditor.vue';
import {
  removeAttachment,
  uploadAttachment,
  getNotesDetailApi,
} from '@/api/user-notes-api';
import { isInRangeOfFileSize } from '@/utils';
import { getIsTrueStatusText } from './handler';

const uploadIconTypes = ['.xls', '.xlsx', '.doc', '.docx', '.pdf'].map((ext) => ext.toLowerCase());
const testFileTypeRegExp = /^[^.][\W\w]*(\.[\W\w]+)$/g;
// const allowFileSize = 1024 * 1024 * 5;

export default {
  name: 'AddVenue',
  components: {
    UEditor,
  },
  mixins: [
    nsI18n('tf', 'userNotes.form'),
  ],

  props: {
    // initData: {
    //   type: Object,
    //   default: () => {},
    // },
    submiting: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      // 多选组件 开始
      fetching: false,
      defaultPersonalKeys: [],
      selectedKeys: [],
      selectedUsers: [],
      changedVenueOptions: [],
      // 多选组件 结束
      VenueStatus,
      saving: false,
      formModel: this.createFormModel(),
      initData: {
        isEnable: true,
      },
      contentTip: false,

      uploading: false,
      fileList: [],
      fileSize: 10,
      hasLoad: false,
    };
  },
  computed: {
    ...mapState({
      serverConfig: (state) => state.userNotes.serverConfig,
    }),
    personalData() {
      return [...this.$store.state.uniData.userList].map((item) => ({
        ...item,
        key: item.id,
        value: item.id,
        title: item.name,
      }));
    },
  },
  watch: {
    initData: {
      handler(newVal) {
        if (newVal.content) {
          this.contentTip = false;
        }

        // this.fileList = [];
        // newVal.attachments.forEach((item) => {
        //   this.fileList.push({
        //     id: item.id,
        //     uid: item.id,
        //     name: item.fileName,
        //     url: item.filePath.normal,
        //     status: 'done',
        //   });
        // });
      },
      deep: true,
    },
  },
  beforeMount() {
    this.$store.dispatch('userNotes/fetchLoginUser').then(({ model }) => {
      this.$store.dispatch('userNotes/fetchToken', model.username)
        .then(() => {
          this.hasLoad = true;
        });
    });
  },
  mounted() {
    if (this.$route.query.id) {
      this.id = Number(this.$route.query.id);
      // this.contentType = SHOW_CONTENT_TYPE.EDIT;
      this.formLoading = true;
      getNotesDetailApi(this.id)
        .then((res) => {
          this.formLoading = false;
          this.initData = res.model;

          this.initData.attachments.forEach((item) => {
            this.fileList.push({
              id: item.id,
              uid: item.id,
              name: item.fileName,
              url: item.filePath.normal,
              status: 'done',
            });
          });
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.formLoading = false;
        });
    }
  },

  methods: {
    // 判断是否为大于等于0的整数
    isNumber(rule, value, cb) {
      if (value) {
        const reg = /^[0-9]*$/;
        if (!reg.test(value)) {
          return cb(this.tf('isNumber'));
        }
        if (value < 0) {
          return cb(this.tf('isNumber'));
        }
      }
      return cb();
    },
    // 显示是否
    getIsTrueStatusText(process) {
      return getIsTrueStatusText(this, process);
    },
    createFormModel() {
      return {
        title: '',
        enTitle: '',
        content: '',
        enContent: '',
        attachmentIds: [],
        sortOrder: '',
        isEnable: '',
      };
    },
    handleSubmit() {
      this.contentTip = !this.initData.content;

      this.form.validateFields((err, values) => {
        const { id } = this.initData;
        if (!err && !this.contentTip) {
          const payload = {
            ...values,
            id,
            content: this.initData.content,
            enContent: this.initData.enContent,
            attachmentIds: this.fileList.length ? this.fileList.map((f) => f.id) : [],
          };
          this.$emit('handle-add-submit', payload);
        }
      });
    },
    handleCancel() {
      this.$emit('handle-cancel-submit');
    },

    beforeUpload(file) {
      testFileTypeRegExp.test(file.name);
      const ext = RegExp.$1;
      const isAllowType = uploadIconTypes.includes(ext.toLowerCase());
      testFileTypeRegExp.lastIndex = -1;
      if (!isAllowType) {
        this.$message.error(this.$t('msg.fileTypeMsg', { types: uploadIconTypes.toString() }));
        return false;
      }
      if (!isInRangeOfFileSize(file, this.fileSize * 1024 * 1024)) {
        this.$message.error(this.$t('msg.fileSizeMsg', { size: this.fileSize }));
        return false;
      }
      return true;
    },
    handleUploadIconRequest({ file }) {
      this.uploading = true;
      uploadAttachment({ fileData: file, relateType: 'USE_NOTES' })
        .then(({ model }) => {
          const imgItem = {
            id: model.id,
            uid: model.id,
            name: model.fileName,
            url: model.filePath.normal,
            status: 'done',
          };
          this.fileList.push(imgItem);
          this.uploading = false;
        })
        .catch((err) => {
          this.uploading = false;
          if (err.message) {
            this.$message.error(err.message);
          }
        });
    },
    handleRemoveImg({ id }) {
      const loading = this.$message.loading(this.$t('msg.loading'));
      removeAttachment(id)
        .then(() => {
          loading();
          this.$message.success(this.$t('msg.delSucc'));
          const fileIndex = this.fileList.map((f) => f.id).indexOf(id);
          this.fileList.splice(fileIndex, 1);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
}

</style>
