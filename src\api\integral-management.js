import request from './request';

// 获取积分管理列表
export function integralManagementListApi(params) {
  return new Promise((resolve, reject) => {
    request({
      url: '/reservation-users',
      method: 'get',
      params,
    }).then((resp) => {
      resolve(resp.data);
    }).catch((err) => {
      reject(err);
    });
  });
}


// 积分详情 列表
export function integralDetailApi({ id, params }) {
  return new Promise((resolve, reject) => {
    request({
      url: `/reservation-users/${id}/points-logs`,
      method: 'get',
      params,
    }).then((resp) => {
      resolve(resp.data);
    }).catch((err) => {
      reject(err);
    });
  });
}


// 积分调整
export function adjustIntegralApi({ id, data }) {
  return new Promise((resolve, reject) => {
    request({
      url: `/reservation-users/${id}/adjust-point`,
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}
