// 配置table columns
export default function getColumns(vm) {
  return [
    {
      title: vm.$t('basicData.columns.name'),
      dataIndex: 'name',
      width: '10%',
    },
    {
      title: vm.$t('basicData.columns.number'),
      dataIndex: 'sn',
      width: '10%',
    },
    {
      title: vm.$t('basicData.columns.category'),
      scopedSlots: { customRender: 'category' },
      width: '10%',
    },
    {
      title: vm.$t('basicData.columns.location'),
      dataIndex: 'address',
      width: '10%',
    },
    {
      title: vm.$t('basicData.columns.peopleNumber'),
      dataIndex: 'capacity',
      width: '10%',
    },
    {
      title: vm.$t('basicData.columns.administrator'),
      scopedSlots: { customRender: 'administrator' },
      width: '15%',
    },
    {
      title: vm.$t('basicData.columns.time'),
      scopedSlots: { customRender: 'time' },
      width: '15%',
    },
    {
      title: vm.$t('basicData.columns.status'),
      scopedSlots: { customRender: 'status' },
      width: '10%',
    },
    {
      title: vm.$t('basicData.columns.operate'),
      scopedSlots: { customRender: 'action' },
      width: '10%',
    },
  ];
}
