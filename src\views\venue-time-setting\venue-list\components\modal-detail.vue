<template>
  <PopModal
    :title="t('title')"
    :visible.sync="realValue"
    @close="onClose"
  >
    <a-spin :spinning="isLoadingVenueDetail">
      <div class="drawer-bd">
        <a-form-model
          ref="formRef"
          layout="horizontal"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 15 }"
          :colon="false"
          :model="formModel"
          :rules="rules"
        >
          <a-form-item
            required
            :label="tl('venueChnName')"
          >
            {{ detailModel.name }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('venueEngName')"
          >
            {{ detailModel.enName }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('venueNumber')"
          >
            {{ detailModel.sn }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('venueCategory')"
          >
            {{ detailModel.venueCategoryName }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('venueProperty')"
          >
            {{ getAttribute(detailModel.attribute) }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('venueManagerUsers')"
          >
            {{ getUserText(detailModel.keeperList) }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('capacity')"
          >
            {{ detailModel.capacity }}
          </a-form-item>

          <a-form-item
            required
            :label="tl('venueLocation')"
          >
            {{ detailModel.address }}
          </a-form-item>

          <a-form-item
            :label="tl('lockid')"
          >
            {{ detailModel.lockid }}
          </a-form-item>

          <a-form-model-item
            prop="isEnable"
            required
            :label="tl('whetherToEnable')"
          >
            <a-radio-group v-model="formModel.isEnable">
              <a-radio :value="VenueStatus.ENABLED">
                {{ VenueStatus.ENABLED | venueStatusI18n }}
              </a-radio>
              <a-radio :value="VenueStatus.DISABLED">
                {{ VenueStatus.DISABLED | venueStatusI18n }}
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>

        <div class="clearfix drawer-ft">
          <a-button @click="onClose">
            {{ $t('action.close') }}
          </a-button>
          <a-button
            type="primary"
            :loading="isSavingVenueStatus"
            @click="onSubmit"
          >
            {{ $t('action.ok') }}
          </a-button>
        </div>
      </div>
    </a-spin>
  </PopModal>
</template>


<script>
import _ from 'lodash';
import { mapState } from 'vuex';
import PopModal from '@/components/base/pop-modal.vue';
import { venueStatusI18n, venuePropertyI18n } from '@/filters/venue';
import { nsI18n } from '@/mixins/ns-i18n';
import { VenueStatus } from '@/constants/venue';
import { getVenueAttributeI18Options } from '@/views/basic-data/components/handler';


export default {
  components: {
    PopModal,
  },

  filters: {
    venueStatusI18n,
    venuePropertyI18n,
  },

  mixins: [
    nsI18n('t', 'venueTimeSetting.modalDetail'),
    nsI18n('tl', 'venueTimeSetting.modalDetail.form.label'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    payload: {
      type: Object,
      default() {
        return null;
      },
    },
  },

  data() {
    return {
      VenueStatus,

      realValue: this.value,
      detailModel: this.createDetailModel(),
      formModel: this.createFormModel(),
    };
  },

  computed: {
    ...mapState({
      isLoadingVenueDetail: (state) => state.venueTimeSetting.isLoadingVenueDetail,
      venueDetail: (state) => state.venueTimeSetting.venueDetail,
      isSavingVenueStatus: (state) => state.venueTimeSetting.isSavingVenueStatus,
    }),

    rules() {
      return {
        isEnable: [
          {
            required: true,
            type: 'boolean',
            message: this.$t('hint.required'),
          },
        ],
      };
    },

    venueAttributeptions() {
      return getVenueAttributeI18Options(this);
    },
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }

      if (val) {
        this.formModel = this.createFormModel();
        await this.$store.dispatch('venueTimeSetting/fetchVenueDetail', this.payload);
        this.detailModel = this.createDetailModel(this.venueDetail);

        const formKeys = Object.keys(this.formModel);
        const formModel = _.pick(this.venueDetail, formKeys);
        Object.assign(this.formModel, formModel);
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    getAttribute(value) {
      const curItem = this.venueAttributeptions.filter((item) => item.key === value);
      if (curItem.length) {
        return curItem[0].label;
      }
      return value;
    },
    createDetailModel(value) {
      return {
        name: '',
        enName: '',
        sn: '',
        category: '',
        attribute: '',
        keeperList: [],
        capacity: 0,
        address: '',
        ...value,
      };
    },

    createFormModel() {
      return {
        isEnable: null,
      };
    },

    getUserText(users) {
      if (Array.isArray(users)) {
        const userNameList = users.map((user) => user.name);
        return userNameList.join('、');
      }

      return null;
    },

    onSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          const { id } = this.payload;
          try {
            await this.$store.dispatch('venueTimeSetting/updateVenueStatus', {
              id,
              data: this.formModel,
            });
            this.realValue = false;
            this.$message.success(this.$t('hint.dataSaved'));
            this.$emit('reload');
          } catch (error) {
            this.$message.error(error.message);
          }
        }
      });
    },

    onClose() {
      this.realValue = false;
    },
  },
};
</script>
