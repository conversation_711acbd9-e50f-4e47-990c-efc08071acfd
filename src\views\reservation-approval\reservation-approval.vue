<template>
  <div class="page-panel">
    <div
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <SimpleSearch
          v-if="!isAdvancedSearch"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
          @toggle-advanced-search="() => { isAdvancedSearch = true }"
        />
      </a-page-header>

      <div class="panel-body">
        <!--基础数据查看-->
        <AdvancedSearch
          v-if="isAdvancedSearch"
          ref="AdvancedSearch"
          :depts="depts"
          :venue-options="venueIds"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
          @toggle-advanced-search="() => { isAdvancedSearch = false }"
        />
        <TableList
          :scroll="tableScroll"
          @detail-reserve="onDetailReserve"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { hasOwn } from '@utils/core';
import operation from '@mixins/operation';
import { getVenuesListApi } from '@api/basic-data-api';
import { getDepts } from '@api/arch-api';
import { nsI18n } from '@/mixins/ns-i18n';
import { getTimeStamp } from '@utils/dateformat';
import { TimeRangeType } from '@/constants/venue';
import { RouteName as RN } from '@/constants/route';

import SimpleSearch from './components/simple-search.vue';
import AdvancedSearch from './components/advanced-search.vue';
import TableList from './components/table-list.vue';

export default {
  name: 'ReserveApprovalList',

  components: {
    TableList,
    SimpleSearch,
    AdvancedSearch,
  },

  mixins: [
    operation,
    nsI18n('t', 'reserveQuery'),
  ],

  data() {
    return {
      TimeRangeType,
      filter: {
        keyword: null,
        statusList: null,
        venueIds: [],
        timeRangeType: TimeRangeType.APPROVALTIME,
      },
      id: 0,
      depts: [],
      venueIds: [],
      isAdvancedSearch: false,
    };
  },

  computed: {
    tableScroll() {
      if (this.isAdvancedSearch) {
        return { y: 'calc(100vh - 425px)' };
      }
      return { y: 'calc(100vh - 214px)' };
    },
  },

  mounted() {
    const urlQuery = this.$route.query;
    Object.keys(urlQuery).forEach((k) => {
      if (hasOwn(this.filter, k)) this.filter[k] = urlQuery[k];
    });
    this.fetchReservationApprovalList({
      page: urlQuery.page || 1,
      pageSize: urlQuery.pageSize || 10,
      filter: {
        ...this.filter,
      },
    });
    // 获取部门
    getDepts()
      .then(({ model }) => {
        this.depts = [
          {
            key: 'all',
            value: '',
            title: this.$t('common.all'),
            label: this.$t('common.all'),
          },
          ...model.map((m) => ({
            label: m.name,
            key: m.id,
            value: m.id,
          })),
        ];
      });
    // 获取场馆名称
    const filter = { ...this.filter, isAll: true };
    getVenuesListApi(filter)
      .then(({ data = [] }) => {
        this.venueIds = [
          ...data.map((m) => ({
            label: m.name,
            key: m.id,
            value: m.name,
          })),
        ];
      });
  },

  methods: {
    getTimeStamp,
    ...mapActions({
      fetchReservationApprovalList: 'reserveApproval/fetchReservationApprovalList',
    }),
    resetSearch(filter) {
      this.filter = filter;
      this.fetchReservationApprovalList(filter);
    },
    handleSearch(filter) {
      this.filter = filter;
      this.fetchReservationApprovalList(filter);
    },

    // 监听预约详情事件
    onDetailReserve(row) {
      const { id } = row;
      this.$router.push({
        name: RN.ReserveApprovalDetail,
        query: { id },
      });
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header {
  min-height: 50px;
  height: auto;
}
::v-deep .ant-page-header-content {
  padding-top: 0;
  overflow: visible;
  padding-right: 120px;
}
::v-deep .ant-page-header-heading {
  position: absolute;
  right: 0;
  padding-right: 24px;
}
::v-deep .ant-form-inline .ant-form-item {
  margin: 7px 8px 7px 0;
}
</style>
