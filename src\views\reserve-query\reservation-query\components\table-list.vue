<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :scroll="scroll"
    :pagination="pagination"
  >
    <!-- 单号 -->
    <template
      slot="sn"
      slot-scope="text, row"
    >
      <a
        @click="goDetailReserve(row)"
      >
        {{ row.sn }}
      </a>
    </template>
    <!-- 预约时间 -->
    <template
      slot="orderTime"
      slot-scope="text, row"
    >
      <div>
        {{ formatDate(row.reservationDate, 'short') }}
      </div>
      <div>
        {{ row.startTime }}
        <span v-if="row.endTime">-</span>
        {{ row.endTime }}
      </div>
    </template>
    <!-- 申请时间 -->
    <template
      slot="createTime"
      slot-scope="text, row"
    >
      {{ formatDate(row.createTime, 'middle') }}
    </template>
    <!-- 状态 -->
    <template
      slot="approvalStatus"
      slot-scope="text, row"
    >
      <span :style="{color: statusColor[row.approvalStatus]}">
        {{ getreserveStatusI18Text(row) }}
      </span>
    </template>
    <!-- 操作 -->
    <template
      slot="action"
      slot-scope="text, row"
    >
      <div
        v-if="hasP(P => P.ReserveQuery.Cancel)"
      >
        <a-button
          v-if="isCancelAble(row.approvalStatus)"
          type="link"
          style="padding-left: 0"
          @click="goCancelReserve(row)"
        >
          {{ $t('reserveQuery.action.cancel') }}
        </a-button>
        <span v-else />
      </div>
    </template>
  </a-table>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { formatDate } from '@utils/dateformat';
import operation from '@mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';
import { ReserveStatus, CancelType } from '@/constants/venue';
import getColumns from './table-columns';
import {
  getreserveStatusI18Text,
} from './handler';

const statusColor = {
  pending: '#F49B00',
  pending_sign: '#F49B00',
  approving: '#F49B00',
  pass: '#009944',
  'auto-pass': '#009944',
  signed: '#009944',
  cancel: '#000000',
  off: '#000000',
  reject: '#9B0000',
};

export default {
  name: 'TableList',
  mixins: [
    operation,
    nsI18n('t', 'reserveQuery'),
  ],
  props: {
    imgs: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    slideIndex: {
      type: Number,
      default: 0,
    },
    scroll: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    this.columns = getColumns(this);
    this.statusColor = statusColor;
    return {
      ReserveStatus,
      selectedRowKeys: [],
      missingCheckinIds: [],
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.reserveQuery.dataSource,
      page: (state) => state.reserveQuery.page,
      pageSize: (state) => state.reserveQuery.pageSize,
      total: (state) => state.reserveQuery.total,
      loading: (state) => state.reserveQuery.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < 10) {
        return false;
      }
      return {
        current: this.page,
        showQuickJumper: true,
        showSizeChanger: true,
        defaultPageSize: this.pageSize,
        pageSize: this.pageSize,
        total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchMyReservationsList({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchMyReservationsList({
            page: current,
            pageSize: size,
          });
        },
      };
    },
    rowSelection() {
      const self = this;
      return {
        onChange(selectedRowKeys, val) {
          self.setRowSelects(val);
          self.selectedRowKeys = selectedRowKeys;
        },
        onSelect(record, selected, selectedRows) {
          const rows = selected ? selectedRows : [record];
          self.selectRow(rows, selected);
        },
        getCheckboxProps: (record) => ({
          props: {
            name: record.name,
          },
        }),
        selectedRowKeys: self.selectedRowKeys,
        onSelectAll(selected, selectedRows, changeRows) {
          self.selectRow(changeRows, selected);
        },
      };
    },
  },
  methods: {
    ...mapActions({
      fetchMyReservationsList: 'reserveQuery/fetchMyReservationsList',
    }),
    // 判断是否显示取消预约按钮
    isCancelAble(value) {
      let isCancel;
      switch (value) {
        case ReserveStatus.PENDING:
        case ReserveStatus.APPROVING:
        case ReserveStatus.PASS:
        case ReserveStatus.AUTO_PASS:
        case ReserveStatus.PEDNING_SIGN:
          isCancel = true;
          break;
        default:
          isCancel = false;
          break;
      }
      return isCancel;
    },
    getreserveStatusI18Text(row) {
      if (row.approvalStatus === ReserveStatus.CANCEL) {
        let cancelStatus;
        switch (row.cancelType) {
          case CancelType.OFF:
            cancelStatus = ReserveStatus.OFF;
            break;
          case CancelType.OTHER:
            cancelStatus = ReserveStatus.CANCEL;
            break;
          default:
            cancelStatus = ReserveStatus.CANCEL;
            break;
        }
        return getreserveStatusI18Text(this, cancelStatus);
      }
      return getreserveStatusI18Text(this, row.approvalStatus);
    },
    formatDate,
    goCancelReserve(row) {
      this.$emit('cancel-reserve', row);
    },
    goDetailReserve(row) {
      this.$emit('detail-reserve', row);
    },
    setRowSelects(val) {
      const tempArr = [];
      val.forEach((value) => {
        tempArr.push(value.id);
      });
      this.missingCheckinIds = tempArr;
      this.$emit('on-export', this.missingCheckinIds);
    },
    // 联动选择服务及模块
    selectRow() {
    },
  },
};
</script>
