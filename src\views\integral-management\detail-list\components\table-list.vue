<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :scroll="scroll"
    :pagination="pagination"
  >
    <!-- 积分变更 -->
    <template
      slot="changePoints"
      slot-scope="text,row"
    >
      {{ getIntegralChangeTypeText(row.changeType) }}{{ text }}
    </template>

    <!-- 最后操作人/操作时间 -->
    <template
      slot="operatorAndTime"
      slot-scope="text, row"
    >
      {{ row.updateUserName }}
      <br>
      {{ formatDate(row.updateTime, 'middle') }}
    </template>
  </a-table>
</template>


<script>
import { mapActions, mapState } from 'vuex';

import { formatDate } from '@utils/dateformat';
import { getIntegralChangeTypeText } from '@/helps/get-consts-text';
import { getColumns } from '../hooks/columns';


export default {
  name: 'TableList',

  props: {
    scroll: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      columns: getColumns(),
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.integralMgmt.detail.dataSource,
      page: (state) => state.integralMgmt.detail.page,
      pageSize: (state) => state.integralMgmt.detail.pageSize,
      total: (state) => state.integralMgmt.detail.total,
      loading: (state) => state.integralMgmt.detail.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < 10) {
        return false;
      }
      return {
        current: self.page,
        showQuickJumper: true,
        showSizeChanger: true,
        // defaultPageSize: this.pageSize,
        // pageSize: this.pageSize,
        // total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchIntegralDetailList({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchIntegralDetailList({
            page: current,
            pageSize: size,
          });
        },
      };
    },
  },
  methods: {
    ...mapActions({
      fetchIntegralDetailList: 'integralMgmt/fetchIntegralDetailList',
    }),
    getIntegralChangeTypeText,

    formatDate,
  },
};
</script>
