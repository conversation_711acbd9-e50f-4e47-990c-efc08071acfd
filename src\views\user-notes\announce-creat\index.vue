<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.UserNotes.AnnouncePublish)"
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <template #title>
          <GoBack
            @click.native="onGoBack"
          />
          {{ headerTitle }}
        </template>
      </a-page-header>

      <div class="panel-body">
        <!-- 查看发布公告页面 -->
        <ViewAnnounce
          v-if="contentType === SHOW_CONTENT_TYPE.VIEW"
          ref="ViewAnnounce"
          :init-data="initData"
          @handle-edit-submit="handleEditSubmit"
          @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
        >
          <div class="center-view">
            <template
              v-if="hasP(P => P.UserNotes.AnnounceEdit)"
            >
              <a-button
                type="primary"
                @click="handleToEditAnnounce"
              >
                {{ t('action.change') }}
              </a-button>
            </template>
          </div>
        </ViewAnnounce>
        <!-- 新增发布公告页面 -->
        <AddAnnounce
          v-if="contentType === SHOW_CONTENT_TYPE.ADD"
          ref="AddAnnounce"
          :init-data="{}"
          :submiting="formLoading"
          @handle-add-submit="handleAddSubmit"
          @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
        >
          <div class="center">
            <template
              v-if="hasP(P => P.UserNotes.AnnounceEdit)"
            >
              <div>
                <a-button
                  @click="handleCancel"
                >
                  {{ t('action.cancel') }}
                </a-button>
                <a-button
                  type="primary"
                  @click="handleSubmit"
                >
                  {{ t('action.save') }}
                </a-button>
              </div>
            </template>
          </div>
        </AddAnnounce>
        <!-- 编辑发布公告页面 -->
        <EditAnnounce
          v-if="contentType === SHOW_CONTENT_TYPE.EDIT"
          ref="EditAnnounce"
          :init-props-data="initData"
          @handle-edit-submit="handleEditSubmit"
          @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
        >
          <div class="center">
            <template
              v-if="hasP(P => P.UserNotes.AnnounceEdit)"
            >
              <div>
                <a-button
                  @click="handleCancel"
                >
                  {{ t('action.cancel') }}
                </a-button>
                <a-button
                  type="primary"
                  @click="handleSubmit"
                >
                  {{ t('action.save') }}
                </a-button>
              </div>
            </template>
          </div>
        </EditAnnounce>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import operation from '@mixins/operation';
import {
  updateNoticeApi,
} from '@api/user-notes-api';
import { nsI18n } from '@/mixins/ns-i18n';
import GoBack from '@components/base/go-back.vue';

import AddAnnounce from './components/add-announce.vue';
import EditAnnounce from './components/edit-announce.vue';
import ViewAnnounce from './components/view-announce.vue';

const SHOW_CONTENT_TYPE = {
  ADD: 'ADD', // 新增
  VIEW: 'VIEW', // 查看
  EDIT: 'EDIT', // 编辑
};

export default {
  name: 'UserNotes',

  components: {
    AddAnnounce,
    EditAnnounce,
    ViewAnnounce,
    GoBack,
  },

  mixins: [operation,
    nsI18n('t', 'userNotes'),
    nsI18n('tp', 'userNotes.publishAnnounce')],

  data() {
    this.SHOW_CONTENT_TYPE = SHOW_CONTENT_TYPE;
    return {
      id: null,
      rowId: 0,
      filter: {
        keyword: '',
        status: '',
      },
      // 编辑页初始数据
      initData: {},
      isAdvancedSearch: false,
      record: {},
      contentType: '',
      modalType: '',
      formLoading: false,
      activeRowRecord: {},
    };
  },

  computed: {
    headerTitle() {
      let title;
      switch (this.contentType) {
        case SHOW_CONTENT_TYPE.VIEW:
          title = this.t('title.announceInfo');
          break;
        case SHOW_CONTENT_TYPE.EDIT:
          title = this.t('title.publishAnnounceEdit');
          break;
        default:
          title = this.t('title.publishAnnounce');
          break;
      }
      return title;
    },
  },
  beforeMount() {
  },

  mounted() {
    this.loadData();
  },

  methods: {
    ...mapActions({
      fetchNoticeApi: 'userNotes/fetchNoticeApi',
    }),
    onGoBack() {
      this.$router.back();
    },
    loadData() {
      this.fetchNoticeApi()
        .then(({ model }) => {
          if (model) {
            this.initData = model;
            this.handleShowContentType(this.SHOW_CONTENT_TYPE.VIEW);
          } else {
            this.handleShowContentType(this.SHOW_CONTENT_TYPE.ADD);
          }
        })
        .catch(() => {
        });
    },
    handleSubmit() {
      if (this.contentType === this.SHOW_CONTENT_TYPE.ADD) {
        this.$refs.AddAnnounce.handleSubmit();
      } else {
        this.$refs.EditAnnounce.handleSubmit();
      }
    },
    // 提交新增表单
    handleAddSubmit(formData) {
      this.formLoading = true;
      updateNoticeApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.tp('msg.submitSuccess'));
          this.loadData();
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.formLoading = false;
        });
    },
    // 提交编辑表单
    handleEditSubmit(formData) {
      this.formLoading = true;
      updateNoticeApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.tp('msg.submitSuccess'));
          this.loadData();
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.formLoading = false;
        });
    },
    // 修改显示类型
    handleShowContentType(type, cb) {
      new Promise((resolve) => {
        const res = cb ? cb(this) : null;
        resolve(res);
      })
        .then(() => {
          this.contentType = type;
        });
    },
    handleToEditAnnounce() {
      this.handleShowContentType(this.SHOW_CONTENT_TYPE.EDIT);
    },
    handleCancel() {
      this.handleShowContentType(this.SHOW_CONTENT_TYPE.VIEW);
    },
    // 关闭表单
    closeModal() {
      this.modalType = '';
      this.activeRowRecord = {};
      this.formLoading = false;
      this.contentType = SHOW_CONTENT_TYPE.VIEW;
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-heading-title {
  display: flex;
  align-items: center;
}

.center {
  margin-bottom: 35px;
  left: 0;
  width: 100%;
  text-align: center;
  .ant-btn {
    height: 32px;
    line-height: 32px;
  }
}
.center-view {
  left: 0;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 35px;
  .ant-btn {
    height: 38px;
    line-height: 38px;
  }
}
</style>
