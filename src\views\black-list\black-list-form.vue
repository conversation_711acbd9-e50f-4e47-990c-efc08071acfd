<template>
  <div class="drawer-bd">
    <a-form
      :form="form"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      :colon="false"
    >
      <a-form-item
        :label="label.userId"
      >
        <a-select
          v-decorator="['userId', {
            initialValue: formData.userId,
            rules: [
              { required: true, message: $t('blacklist.place.userId') },
            ]
          }]"
          show-search
          placeholder=""
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          @search="handleSearch"
          @change="handleChange"
        >
          <a-select-option
            v-for="d in data"
            :key="d.value"
          >
            {{ d.text }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        :label="label.reservationType"
      >
        <a-radio-group
          v-decorator="['reservationType', {
            initialValue: formData.reservationType,
            rules: [
              { required: true, message: $t('blacklist.place.reservationType') },
            ]
          }]"
          :options="reservationTypeOptions"
        />
      </a-form-item>
      <a-form-item
        :label="label.reason"
      >
        <a-textarea
          v-decorator="['reason', {
            initialValue: formData.reason,
            rules: [
              { required: true, message: $t('blacklist.place.reasonPlace') },
              { max: 200, message: $t('form.maxLength', { num: 200 }) },
            ]
          }]"
          :placeholder="$t('blacklist.place.reason')"
          :auto-size="{ minRows: 4, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
    <div class="drawer_footer">
      <a-button
        type="link"
        @click="onClose"
      >
        {{ $t('action.cancle') }}
      </a-button>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ $t('action.ok') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { fetchUsers } from '@/api/arch-api';
import { getReservationTypeApi } from '@/api/service-query-api';

export default {
  name: 'BlackListForm',

  props: {
    type: {
      type: String,
      default: 'add',
    },
    id: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      label: {
        userId: this.$t('blacklist.form.userId'),
        reason: this.$t('blacklist.form.reason'),
        reservationType: this.$t('blacklist.form.reservationType'),
      },
      formData: {
        userId: [],
        reservationType: '',
        reason: '',
      },
      fetching: false,
      data: [],
      value: [],
      reservationTypeOptions: [],
    };
  },

  computed: {
    ...mapState({
      loading: (state) => state.blacklist.saving,
    }),
  },

  mounted() {
    getReservationTypeApi()
      .then(({ model }) => {
        this.reservationTypeOptions = model.map((item) => ({
          code: item.code,
          value: item.code,
          name: item.name,
          label: item.name,
        }));
      });
    if (this.type === 'edit') {
      this.$store.dispatch('blacklist/fetchBlackListDetail', {
        id: this.id,
      })
        .then(({ model }) => {
          const data = {
            ...model,
          };
          this.form.setFieldsValue(data);
        });
    }
  },

  methods: {
    handleSearch(value) {
      fetchUsers({
        page: 1,
        limit: -1,
        keyword: value,
      })
        .then((body) => {
          const data = body.data.map((user) => ({
            text: user.name,
            value: user.id,
          }));
          this.data = data;
        });
    },
    handleChange(value) {
      // this.value = value;
      this.form.setFieldsValue({
        userId: value,
      });
      fetchUsers({
        page: 1,
        limit: -1,
        keyword: value,
      })
        .then((body) => {
          const data = body.data.map((user) => ({
            text: user.name,
            value: user.id,
          }));
          this.data = data;
        });
    },
    onClose() {
      this.$emit('close');
    },
    handleSubmit() {
      const formate = {
        reason: (r) => r,
        userId: (r) => r,
      };
      const { form } = this;
      form.validateFields((err, values) => {
        if (!err) {
          const payload = {};
          const r = Object.keys(values);
          r.forEach((key) => {
            if (values[key] || values[key] === 0 || values[key] === false) {
              if (formate[key]) {
                const val = formate[key](values[key]);
                payload[key] = val;
              } else {
                payload[key] = values[key];
              }
            }
          });
          if (this.type === 'add') {
            // 新增
            this.$store.dispatch('blacklist/createBlackList', payload)
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success(this.$t('blacklist.msg.saveSucc'));
                  this.$emit('update');
                  this.onClose();
                }
              });
          } else if (this.type === 'edit') {
            // 编辑
            this.$store.dispatch('blacklist/editBlackList', {
              data: payload,
              id: this.id,
            })
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success(this.$t('blacklist.msg.editSucc'));
                  this.$emit('update');
                  this.onClose();
                }
              });
          }
        }
      });
    },
  },
};
</script>
