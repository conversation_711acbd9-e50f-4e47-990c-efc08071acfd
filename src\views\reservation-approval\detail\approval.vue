<template>
  <div class="wrapper">
    <div
      id="venue-reservation-approval"
    />
  </div>
</template>


<script>
import {
  defineComponent, getCurrentInstance, onMounted, onBeforeUnmount,
} from 'vue';
import { useService } from '@/helps/service';
import { getToken } from '@/helps/get-token';
import { getNewCurrentLocale } from '@/helps/locale';
import { goBack } from '@/helps/navigation';


export default defineComponent({
  name: 'VenueReservationApprovalDetail',

  setup() {
    const SERVICE_CODE = 'VenueReservationApproval';
    const MOUNT_ID = '#venue-reservation-approval';
    const vm = getCurrentInstance();
    const router = vm.proxy.$router;
    const route = vm.proxy.$route;
    const service = useService.getInstance();

    function onGoBack() {
      goBack(router);
    }

    function injectProps(services) {
      return services.map((item) => ({
        ...item,
        props: {
          state: {
            getToken,
            goBack: onGoBack,
            injectStyle: true,
            dataId: route.query.id,
            locale: getNewCurrentLocale(),
          },
        },
      }));
    }

    onMounted(() => {
      service.initQkm();
      service.registerMicroApps(injectProps([{
        code: SERVICE_CODE,
        serviceUrl: process.env.MICROSERVICE_VENUE_RESERVATION_DETAIL_URL,
      }]), MOUNT_ID);
      service.mountService(SERVICE_CODE);
    });

    onBeforeUnmount(() => {
      service.unmountService(SERVICE_CODE);
    });

    return {};
  },
});
</script>


<style scoped lang="less">
.wrapper {
  position: relative;
}

#venue-reservation-approval {
  height: calc(100vh - 50px);

  :deep(> div) {
    width: 100%;
    height: 100%;
  }
}
</style>
