/* eslint-disable camelcase */
const { loadAccessTokenData, loadRefreshTokenData } = require('./request');
const { btoa, accessTokenHasExpired } = require('./utils');

const ErrorCode = Object.freeze({
  GET_TOKEN_FAILED: 'F_GET_TOKEN_FAILED',
  REFRESH_TOKEN_FAILED: 'F_REFRESH_TOKEN_FAILED',
  LOCK_FAILED: 'F_LOCK_FAILED',
});

module.exports = function (options) {
  const {
    clientId,
    clientSecret,
    baseUrl,
    casUserSessionName,
    tokenSessionName,
    accessTokenCookieName,
    redisPrefix,
    redlock,
    cookieSecure,
    cookieSameSite,
  } = options;
  const config_clientId = clientId;
  const config_clientSecret = clientSecret;
  const config_baseUrl = baseUrl;
  const config_casUserSessionName = casUserSessionName;
  const config_tokenSessionName = tokenSessionName || 'appToken';
  const config_accessTokenCookieName = accessTokenCookieName || '__ut';
  const config_redisPrefix = redisPrefix || 'pima';
  const config_cookieSecure = cookieSecure || false;
  const config_cookieSameSite = cookieSameSite || 'Lax';

  if (!config_clientId) {
    throw new Error('clientId not configured');
  }
  if (!config_clientId) {
    throw new Error('clientSecret not configured');
  }
  if (!config_baseUrl) {
    throw new Error('baseUrl not configured');
  }
  if (!config_casUserSessionName) {
    throw new Error('casUserSessionName not configured');
  }

  redlock.on('clientError', (err) => {
    // eslint-disable-next-line no-console
    console.error('A redis error has occurred:', err);
  });

  // 保存Token信息到Session
  function saveAuthTokenToSession(req, authInfo) {
    const {
      tokenType,
      accessToken,
      refreshToken: refreshTokenValue,
      expiresIn,
    } = authInfo;
    const interval = expiresIn * 1000;
    // const interval = 30 * 1000;
    const expiresInValue = new Date(Date.now() + interval).toISOString();
    // eslint-disable-next-line no-param-reassign
    req.session[config_tokenSessionName] = {
      tokenType,
      accessToken,
      refreshToken: refreshTokenValue,
      expiresIn: expiresInValue,
    };
    req.session.save((error) => {
      if (error) {
        // eslint-disable-next-line no-console
        console.error('Save session error', error);
      }
    });
  }

  // 保存用户Access Token到Cookie
  function saveAccessTokenToCookie(res, authInfo) {
    const { tokenType, accessToken } = authInfo;
    if (accessTokenCookieName && accessToken) {
      const data = btoa(`${tokenType} ${accessToken}`);
      res.cookie(config_accessTokenCookieName, data, {
        httpOnly: false,
        secure: config_cookieSecure,
        sameSite: config_cookieSameSite,
      });
    }
  }

  // 当用户不在登录状态时，返回401状态码
  function blockTokenMiddleware(req, res, next) {
    if (req.xhr) {
      if (!req.session || (req.session && !req.session[config_casUserSessionName])) {
        next({
          status: 401,
        });
        // res.status(401).end();
      }
    }

    next();
  }

  async function getToken(req, res) {
    try {
      const response = await loadAccessTokenData(
        config_baseUrl,
        config_clientId,
        config_clientSecret,
        req.session[config_casUserSessionName],
      );
      const { success, model } = response.data;
      if (!success) {
        const error = new Error('Get token failed');
        error.response = response;
        throw error;
      }

      saveAuthTokenToSession(req, model);
      saveAccessTokenToCookie(res, model);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('get token error', error);
      if (req.xhr) {
        if (error.response && error.response.data) {
          res.json(error.response.data).end();
        } else {
          res.json({
            success: false,
            errorCode: ErrorCode.GET_TOKEN_FAILED,
          }).end();
        }
      } else {
        throw error;
      }
    }
  }

  async function refreshToken(req, res) {
    try {
      const response = await loadRefreshTokenData(
        config_baseUrl,
        req.session[config_tokenSessionName].refreshToken,
      );
      const { success, model } = response.data;
      if (!success) {
        const error = new Error('Refresh token failed');
        error.response = response;
        throw error;
      }

      saveAuthTokenToSession(req, model);
      saveAccessTokenToCookie(res, model);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('refresh token error', error);
      // eslint-disable-next-line no-param-reassign
      delete req.session[config_tokenSessionName]; // 刷新失败时，删除Session里的Token信息，防止刷新死循环
      if (req.xhr) {
        if (error.response && error.response.data) {
          res.json(error.response.data);
        } else {
          res.json({
            success: false,
            errorCode: ErrorCode.REFRESH_TOKEN_FAILED,
          });
        }
      } else {
        throw error;
      }
    }
  }

  function reloadSession(req) {
    return new Promise((resolve, reject) => {
      try {
        req.session.reload(resolve);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 更新Token
  async function renewTokenMiddleware(req, res, next) {
    if (req.session && req.session[config_casUserSessionName]) {
      if (!req.session[config_tokenSessionName]) {
        await getToken(req, res);
      } else if (accessTokenHasExpired(req.session[config_tokenSessionName])) {
        const resource = `${config_redisPrefix}locks:account:${req.session[config_casUserSessionName]}`;
        const ttl = 3000;
        try {
          const lock = await redlock.lock(resource, ttl);
          await reloadSession(req);
          if (accessTokenHasExpired(req.session[config_tokenSessionName])) {
            await refreshToken(req, res);
          }
          await redlock.unlock(lock);
        } catch (error) {
          if (req.xhr) {
            if (!error.code) {
              res.json({
                success: false,
                errorCode: ErrorCode.LOCK_FAILED,
              });
            }
            res.end();
          } else {
            throw error;
          }
        }
      }
    }

    next();
  }

  // 替换AJAX请求头部Authorization值
  function replaceRequestTokenMiddleware(req, res, next) {
    if (req.xhr
      && req.session
      && req.session[config_casUserSessionName]
      && req.session[config_tokenSessionName]
      && req.session[config_tokenSessionName].tokenType
      && req.session[config_tokenSessionName].accessToken) {
      const { tokenType, accessToken } = req.session[config_tokenSessionName];
      const authorization = `${tokenType} ${accessToken}`;
      Object.assign(req.headers, {
        authorization,
      });
    }

    next();
  }

  // 删除Token信息
  function removeTokenMiddleware(req, res, next) {
    if (req.session) {
      if (req.session[config_casUserSessionName]) {
        // eslint-disable-next-line no-param-reassign
        delete req.session[config_casUserSessionName];
      }
      if (req.session[config_tokenSessionName]) {
        // eslint-disable-next-line no-param-reassign
        delete req.session[config_tokenSessionName];
      }
    }
    next();
  }

  return {
    blockTokenMiddleware,
    renewTokenMiddleware,
    replaceRequestTokenMiddleware,
    removeTokenMiddleware,
  };
};
