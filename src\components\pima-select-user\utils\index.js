/* eslint-disable */
/**
 * PimaUI-Select-User
 */

/**
 * 左侧选项
 * @type {{SELECT_ALL: string, SELECT_PERSONAL: string, SELECT_DEPARTMENT: string}}
 */
export const SELECT_TYPE = {
  SELECT_ALL: 'SELECT_ALL',
  SELECT_DEPARTMENT: 'SELECT_DEPARTMENT',
  SELECT_PERSONAL: 'SELECT_PERSONAL',
};

/**
 * 获取筛选后的节点
 * @param treeData：Array<TreeNode> 元数据
 * @param keyword：String 搜索字符
 * @returns Array<TreeNode> Tree
 */
export function getFilterTree(treeData, keyword = '', fieldName = 'title') {
  const groups = [];
  if (Array.isArray(treeData)) {
    treeData.forEach((group) => {
      let flag = 0;
      // eslint-disable-next-line eqeqeq
      if (group[fieldName] && group[fieldName].toLowerCase().indexOf(keyword.toLowerCase()) !== -1) {
        flag += 1;
      } else if (Array.isArray(group.children) && group.children.length > 0) {
        const children = getFilterTree(group.children, keyword, fieldName);
        if (children.length > 0) {
          flag += 1;
        }
      }
      if (flag > 0) {
        const children = getFilterTree(group.children, keyword, fieldName);
        groups.push({
          ...group,
          children,
        });
      }
    });
  }
  return groups;
}

/**
 * 扁平树结构
 * @param treeData: Array<TreeNode>
 * @param resultList: Array<Node>
 * @return Array<Node>
 */
export function flatTreeNodeList(treeData, resultList = []) {
  if (treeData && treeData.length) {
    treeData.forEach(item => {
      resultList.push(item);
      return flatTreeNodeList(item.children, resultList);
    });
  }
  return resultList;
}

/**
 * 获取所有已经被选中的节点
 * @param treeData：Array<TreeNode> 元数据
 * @param fieldName：String 判断选中的字段
 * @param resultList：Array<TreeNode> 结果
 * @returns Array<TreeNode>
 */
export function getSelectedTreeNodeList(treeData, selectedKey = []) {
  const flatNodeList = flatTreeNodeList(treeData);
  let i = 0;
  let result = [];
  while (i < selectedKey.length) {
    // eslint-disable-next-line no-loop-func
    const currentNode = flatNodeList.filter(node => node.key === selectedKey[i]);
    result = [...result, ...currentNode];
    // eslint-disable-next-line no-plusplus
    i++;
  }
  return result;
}

/**
 * 获取当前节点所有子节点（包含此节点）
 * @param treeData: {Array<treeNode>} 树
 * @param id: {number} 当前节点id|key
 * @return {*[]}
 */
export function getTreeNodeChildrenList(treeData, id) {
  const flagTree = flatTreeNodeList(treeData);
  const curNode = flagTree.filter((node) => node.id === id);
  return flatTreeNodeList(curNode);
}

export default {};
