import Vue from 'vue';
import 'core-js/features/object/values';
import 'core-js/features/object/entries';
import 'core-js/features/array/includes';
import 'core-js/features/string/includes';
import 'core-js/features/map/map-keys';
import 'core-js/features/array/find';
import 'core-js/features/array/find-index';
import 'core-js/features/symbol';
import 'core-js/stable';
import 'regenerator-runtime/runtime';
// eslint-disable-next-line
// import PimaUI from 'pimaUi';
// eslint-disable-next-line
// import PimaUISider from 'pimaUiSider';
// eslint-disable-next-line
import PimaUiImport from 'pimaUiImport';
// eslint-disable-next-line
import PimaUISelectPost from 'pimaUiSelectPost';

import NProgress from 'nprogress';
import createApp from './main';
import config from './config';

NProgress.configure({ showSpinner: false });

const importIconfont = () => import('./assets/font/iconfont');
if (process.env.VUE_ENV !== 'server') {
  importIconfont();
}

// eslint-disable-next-line no-underscore-dangle
const piniaStateValue = window.__INITIAL_STATE__;
const { locale } = piniaStateValue.i18n;
const { loggedIn, username } = piniaStateValue.loginStatus;

const {
  app, router, store, i18n, pinia,
} = createApp({ loggedIn, username, locale });

// Vue.use(PimaUI, { i18n });
// Vue.use(PimaUISider, { i18n });
Vue.use(PimaUiImport, { i18n });
Vue.use(PimaUISelectPost, { i18n });

// eslint-disable-next-line no-underscore-dangle
if (window.__INITIAL_STATE__) {
  // eslint-disable-next-line no-underscore-dangle
  store.replaceState(window.__INITIAL_STATE__);
  // eslint-disable-next-line no-underscore-dangle
  pinia.state.value = window.__INITIAL_STATE__;
}

if (locale && config.supportLocales.indexOf(locale) !== -1) {
  i18n.locale = locale;
}

router.onReady(() => {
  app.$mount('#app');
});

router.beforeEach((to, from, next) => {
  NProgress.start();
  next();
});

router.afterEach(() => {
  NProgress.done();
});
