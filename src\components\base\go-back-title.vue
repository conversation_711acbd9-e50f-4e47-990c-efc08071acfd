<template>
  <a-page-header
    class="PC-view"
    :ghost="false"
  >
    <template
      slot="title"
    >
      <div class="goback">
        <div
          class="icon"
          @click="handleBack"
        >
          <span class="left" />
        </div>

        <span class="title">
          {{ title }}
        </span>
      </div>
    </template>
    <template #extra>
      <slot name="extra" />
    </template>
  </a-page-header>
</template>

<script>
export default {
  name: 'GoBackTitle',

  props: {
    title: {
      type: String,
      default: '',
    },
  },

  methods: {
    handleBack() {
      this.$emit('back');
    },
  },
};
</script>
