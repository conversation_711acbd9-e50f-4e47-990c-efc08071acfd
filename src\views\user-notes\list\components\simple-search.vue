<template>
  <a-form
    :form="form"
    :label-col="{span: 5}"
    :wrapper-col="{span:15}"
    :layout="'inline'"
    label-align="right"
  >
    <a-form-item>
      <a-input
        v-decorator="['keyword', {
          initialValue: filter.keyword,
        }]"
        allow-clear
        style="width: 250px;"
        :placeholder="$t('userNotes.form.keywordPlace')"
        @pressEnter="handleSearch"
        @change="debounce(onInputChange, 1000)()"
      >
        <span
          slot="prefix"
          class="iconfont icon-all_sousuo"
          @click="handleSearch"
        />
      </a-input>
    </a-form-item>

    <!-- <a-form-item>
      <a-button
        html-type="submit"
        type="primary"
      >
        {{ $t('action.search') }}
      </a-button>
    </a-form-item>
    <a-form-item>
      <a-button
        type="default"
        @click="resetSearch"
      >
        {{ $t('action.reset') }}
      </a-button>
    </a-form-item> -->
  </a-form>
</template>

<script>
import { hasOwn } from '@utils/core';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, ...args);
    }, wait);
  };
}

export default {
  name: 'SimpleSearch',
  props: {
  },
  data() {
    this.form = this.$form.createForm(this);
    return {
      filter: {
        category: '',
        keyword: '',
      },
    };
  },
  methods: {
    debounce,
    onInputChange() {
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    resetSearch() {
      this.form.resetFields();
      Object.keys(this.filter).forEach((k) => {
        this.filter[k] = '';
      });
      this.$emit('reset-search', {
        page: 1,
        pageSize: 10,
        filter: {
          category: '',
          ...this.filter,
        },
      });
    },
    handleSearch() {
      // e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          Object.keys(values).forEach((k) => {
            if (hasOwn(this.filter, k)) {
              this.filter[k] = values[k];
            }
          });
          const payload = {
            ...this.filter,
            ...values,
          };
          this.$emit('handle-search', {
            page: 1,
            filter: payload,
          });
        }
      });
    },
  },
};
</script>
