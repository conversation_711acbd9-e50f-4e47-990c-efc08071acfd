<template>
  <a-table
    :data-source="dataSource"
    :loading="isLoading"
    :columns="columns"
    :row-key="record => record.id"
    :pagination="pagination"
    :scroll="{ y: 'calc(100vh - 212px)' }"
    class="pima-table"
  >
    <template
      slot="time"
      slot-scope="text"
    >
      {{ $moment(text).format('YYYY/MM/DD HH:mm') }}
    </template>
    <template
      slot="reservationType"
      slot-scope="text"
    >
      {{ getReservationType(text) }}
    </template>
    <template
      slot="createUserName"
      slot-scope="text, record"
    >
      {{ text }} <br>
      {{ $moment(record.createTime).format('YYYY/MM/DD HH:mm') }}
    </template>
    <template
      slot="isAutoRemove"
      slot-scope="text, record"
    >
      {{ text ? $t('blacklist.isAutoRemove.yes', {days: record.autoRemoveDays}) : $t('blacklist.isAutoRemove.no') }}
    </template>
    <template
      slot="operation"
      slot-scope="text, record"
    >
      <a
        v-if="hasP(P => P.BlackList.Del)"
        style="margin-left: 10px;"
        @click="handleDel(record)"
      >
        {{ $t('action.moveout') }}
      </a>
    </template>
  </a-table>
</template>

<script>
import OpreationMixin from '@/mixins/operation';
import { getReservationTypeApi } from '@/api/service-query-api';

export default {
  name: 'SpecialDayList',

  mixins: [OpreationMixin],

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: true,
    },
    pageInfo: {
      type: Object,
      default: () => ({
        page: 1,
        limit: 10,
      }),
    },
    totalSize: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      reservationTypeOptions: [],
    };
  },

  computed: {
    pagination() {
      const self = this;
      const showQuickJumper = this.totalSize / this.pageInfo.limit > 1;
      if (this.totalSize < 10) {
        return false;
      }
      return {
        showQuickJumper,
        showSizeChanger: true,
        current: self.pageInfo.page,
        defaultPageSize: self.pageInfo.limit,
        total: self.totalSize || 0,
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, limit) {
          self.$emit('pageChange', page, limit);
        },
        showTotal(total) {
          self.total = total;
          const totalPage = Math.ceil(self.totalSize / self.pageInfo.limit);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        onShowSizeChange(cur, size) {
          self.$emit('pageChange', cur, size);
        },
      };
    },
    columns() {
      return [
        {
          title: this.$t('blacklist.columns.userName'),
          dataIndex: 'userName',
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.userNo'),
          dataIndex: 'userNo',
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.reservationType'),
          dataIndex: 'reservationType',
          scopedSlots: { customRender: 'reservationType' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.reason'),
          dataIndex: 'reason',
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.createTime'),
          dataIndex: 'createTime',
          scopedSlots: { customRender: 'time' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.isAutoRemove'),
          dataIndex: 'isAutoRemove',
          scopedSlots: { customRender: 'isAutoRemove' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.createUserName'),
          dataIndex: 'createUserName',
          scopedSlots: { customRender: 'createUserName' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.operation'),
          scopedSlots: { customRender: 'operation' },
          align: 'left',
          width: 105,
        },
      ];
    },
  },

  mounted() {
    getReservationTypeApi()
      .then(({ model }) => {
        this.reservationTypeOptions = model.map((item) => ({
          code: item.code,
          value: item.code,
          name: item.name,
          label: item.name,
        }));
      });
  },

  methods: {
    handleEdit(record) {
      this.$emit('edit', record);
    },
    handleDel(record) {
      const type = this.getReservationType(record.reservationType);

      this.$emit('on-remove', {
        ...record,
        type,
      });
    },
    getReservationType(code) {
      const type = this.reservationTypeOptions.filter((item) => item.code === code);
      if (type.length) {
        return type[0].label;
      }
      return '';
    },
  },
};
</script>
