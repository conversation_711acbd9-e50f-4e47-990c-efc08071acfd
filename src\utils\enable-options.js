import enableStatus from '../constants/enable';

const enableStatusList = [
  enableStatus.ENABLE_ALL,
  enableStatus.ENABLE_TRUE,
  enableStatus.ENABLE_FALSE,
];

const enableStatusMapper = {
  [enableStatus.ENABLE_ALL]: 'special.enable.all',
  [enableStatus.ENABLE_TRUE]: 'special.enable.true',
  [enableStatus.ENABLE_FALSE]: 'special.enable.false',
};

export function i18nSingle(vue, text) {
  return vue.i18n ? vue.i18n.t(text) : vue.$t(text);
}

export function getEnableStatusOptions(vue) {
  return enableStatusList.map((key) => ({
    key: String(key),
    value: String(key),
    title: i18nSingle(vue, enableStatusMapper[key]),
    label: i18nSingle(vue, enableStatusMapper[key]),
  }));
}
