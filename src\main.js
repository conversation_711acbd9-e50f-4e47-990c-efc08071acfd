import Vue from 'vue';
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia';
import moment from 'moment';
import lodash from 'lodash';
import {
  ConfigProvider,
  Layout,
  Input,
  InputNumber,
  AutoComplete,
  Button,
  Switch,
  Radio,
  Checkbox,
  Select,
  Cascader,
  Card,
  Form,
  FormModel,
  Row,
  Col,
  Modal,
  Table,
  Tabs,
  Collapse,
  Icon,
  Badge,
  Popover,
  Dropdown,
  List,
  Avatar,
  Breadcrumb,
  Steps,
  Spin,
  Menu,
  Drawer,
  Tooltip,
  Alert,
  Tag,
  Divider,
  DatePicker,
  TimePicker,
  Calendar,
  Upload,
  Progress,
  Skeleton,
  Popconfirm,
  PageHeader,
  Result,
  Statistic,
  Descriptions,
  message,
  Empty,
  Tree,
  TreeSelect,
} from 'ant-design-vue';
import 'moment/locale/zh-cn';
import 'nprogress/nprogress.css';
import './styles/global.less';
import './styles/pima2.less';
import './styles/tree.less';
import './styles/style.less';
import { useI18nStore } from '@/store/i18n';
import eventBus from './event-bus';
import request from './api/request';
import createRouter from './router';
import createStore from './store';
import i18n from './locales';
import { useLoginStatusStore } from './store/login-status';
import App from './App.vue';

Vue.config.productionTip = false;

moment.locale('zh-cn');
Vue.prototype.$moment = moment;
Vue.prototype._ = lodash;
Vue.prototype.$eventBus = eventBus;

Vue.use(PiniaVuePlugin);
Vue.use(ConfigProvider);
Vue.use(Layout);
Vue.use(Input);
Vue.use(InputNumber);
Vue.use(AutoComplete);
Vue.use(Button);
Vue.use(Switch);
Vue.use(Radio);
Vue.use(Checkbox);
Vue.use(Select);
Vue.use(Cascader);
Vue.use(Card);
Vue.use(Form);
Vue.use(FormModel);
Vue.use(Row);
Vue.use(Col);
Vue.use(Modal);
Vue.use(Table);
Vue.use(Tabs);
Vue.use(Collapse);
Vue.use(Icon);
Vue.use(Badge);
Vue.use(Popover);
Vue.use(Dropdown);
Vue.use(List);
Vue.use(Avatar);
Vue.use(Breadcrumb);
Vue.use(Steps);
Vue.use(Spin);
Vue.use(Menu);
Vue.use(Drawer);
Vue.use(Tooltip);
Vue.use(Alert);
Vue.use(Tag);
Vue.use(Divider);
Vue.use(DatePicker);
Vue.use(TimePicker);
Vue.use(Calendar);
Vue.use(Upload);
Vue.use(Progress);
Vue.use(Skeleton);
Vue.use(Popconfirm);
Vue.use(PageHeader);
Vue.use(Result);
Vue.use(Statistic);
Vue.use(Descriptions);
Vue.use(Empty);
Vue.use(Tree);
Vue.use(TreeSelect);
Vue.prototype.$message = message;
Vue.prototype.$confirm = Modal.confirm;
Vue.prototype.$info = Modal.info;
Vue.prototype.$success = Modal.success;
Vue.prototype.$warning = Modal.warning;
Vue.prototype.$error = Modal.error;

message.config({
  maxCount: 1,
});

// 监听接口出错
request.interceptors.response.use((resp) => resp, (err) => {
  eventBus.$emit('api-error', err);
  return Promise.reject(err);
});

// { loggedIn, username }
export default function createApp({ loggedIn, username, locale }) {
  const pinia = createPinia();
  const store = createStore();
  const i18nStore = useI18nStore(pinia);
  i18nStore.locale = locale;
  const loginStatusStore = useLoginStatusStore(pinia);
  loginStatusStore.loggedIn = loggedIn; // 该值的设置必须早于创建 router
  loginStatusStore.username = username; // 该值的设置必须早于创建 router

  const router = createRouter();
  const app = new Vue({
    router,
    store,
    i18n,
    pinia,
    render: (h) => h(App),
  });
  return {
    app,
    router,
    store,
    i18n,
    pinia,
  };
}
