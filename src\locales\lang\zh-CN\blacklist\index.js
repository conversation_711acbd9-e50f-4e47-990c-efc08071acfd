export default {
  title: {
    add: '新增黑名单人员',
    set: '设置',
    setting: '黑名单机制设置',
    editSetting: '修改黑名单机制',
  },

  tabs: {
    blackList: '黑名单信息',
    removeBlackList: '移出黑名单信息',
  },

  form: {
    keywordsPlace: '黑名单人员姓名或工号',
    userId: '姓名',
    reason: '加入黑名单的原因',
    isAutoBlacklist: '是否启用自动加入黑名单机制',
    isCancelBlacklist: '是否启用解除黑名单机制',
    rule: '设置规则',
    usefulType: '适用类型',
    isAutoAdd: '是否自动加入黑名单',
    autoAddType: '自动加入黑名单规则',
    isAutoRemove: '是否自动移出黑名单',
    reservationType: '不可预约类型',
    removeReason: '移出黑名单的原因',
  },
  place: {
    reason: '备注请控制200个字以內',
    reasonPlace: '请填写加入黑名单的原因',
    removeReason: '请填写移出黑名单的原因',
    userId: '请选择人员',
    systemMonth: '请填写自然月数',
    systemCount: '请填写爽约次数',
    systemDay: '请填写天数',
    isAutoAdd: '请选择是否自动加入黑名单',
    autoAddType: '请选择自动加入黑名单规则',
    isAutoRemove: '请选择是否自动移出黑名单',
    reservationType: '请选择不可预约类型',
  },
  action: {
    add: '新增',
    setting: '黑名单机制设置',
  },
  msg: {
    delTip: '是否确认将此人移出{type}场馆的黑名单呢？',
    delSucc: '操作成功！',
    saveSucc: '保存成功！',
    editSucc: '修改成功！',
    autoTip: [
      '个自然月累计爽约 ',
      ' 次，则自动加入黑名单。',
    ],
    cancelTip: [
      '自动加入黑名单, ',
      '天后，移出黑名单。(从加入的那一天算起，对手动加入的无效）',
    ],
    setTip: '示例：自动加入2天后自动移出，9月13日 15:00加入黑名单，9月15日0:00移出黑名单',
    autoAddMsg: [
      '个自然月累计爽约',
      '次，则自动加入黑名单。',
      '累计爽约',
      '次，则自动加入黑名单。',
    ],
  },
  columns: {
    userName: '姓名',
    userNo: '学号/工号',
    reason: '加入黑名单原因',
    removeReason: '移出黑名单原因',
    createTime: '加入黑名单时间',
    removeCreateTime: '移出黑名单时间',
    createUserName: '操作人/操作时间',
    operation: '操作',
    usefulType: '适用类型',
    blackName: '加入黑名单机制',
    removeName: '移出黑名单机制',
    lastModName: '最后操作人/最后操作时间',
    reservationType: '不可预约类型',
    isAutoRemove: '是否自动移出',
  },
  isAutoRemove: {
    yes: '自动加入黑名单，{days}天后，移出黑名单',
    no: '不自动移出',
  },
  autoAddType: {
    month: '{mon}个自然月累计爽约{count}次，则自动加入黑名单',
    stat: '累计爽约{count}次，则自动加入黑名单',
    no: '不自动加入黑名单',
  },

  modalRemove: {
    title: '移出黑名单',
  },
};
