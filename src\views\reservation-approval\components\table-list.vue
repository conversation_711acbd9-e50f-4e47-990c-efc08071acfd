<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :scroll="scroll"
    :pagination="pagination"
  >
    <!-- 单号 -->
    <template
      slot="sn"
      slot-scope="text, row"
    >
      <a
        @click="goDetailReserve(row)"
      >
        {{ row.sn }}
      </a>
    </template>
    <!-- 预约时间 -->
    <template
      slot="orderTime"
      slot-scope="text, row"
    >
      <div>
        {{ formatDate(row.reservationDate, 'short') }}
      </div>
      <div>
        {{ row.startTime }}
        <span v-if="row.endTime">-</span>
        {{ row.endTime }}
      </div>
    </template>
    <!-- 申请时间 -->
    <template
      slot="createTime"
      slot-scope="text, row"
    >
      {{ formatDate(row.applyTime, 'middle') }}
    </template>
    <!-- 状态 -->
    <template
      slot="approvalStatus"
      slot-scope="text, row"
    >
      <span :style="{color: statusColor[row.status]}">
        {{ getreserveStatusI18Text(row) }}
      </span>
    </template>
    <!-- 操作 -->
    <template
      slot="action"
      slot-scope="text, row"
    >
      <div
        v-if="row.isCanApproval "
      >
        <a-button

          type="link"
          style="padding-left: 0"
          @click="goDetailReserve(row)"
        >
          {{ $t('reserveQuery.action.toApproval') }}
        </a-button>
      </div>
    </template>
  </a-table>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { formatDate } from '@utils/dateformat';
import operation from '@mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';
import { ReserveStatus, CancelType } from '@/constants/venue';
import getColumns from '../helps/table-columns';
import {
  getreserveStatusI18Text,
} from '../helps/handler';

const statusColor = {
  pending: '#F49B00',
  pass: '#009944',
  cancel: '#000000',
  reject: '#9B0000',
};

export default {
  name: 'TableList',
  mixins: [
    operation,
    nsI18n('t', 'reserveQuery'),
  ],
  props: {
    imgs: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    slideIndex: {
      type: Number,
      default: 0,
    },
    scroll: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    this.columns = getColumns(this);
    this.statusColor = statusColor;
    return {
      ReserveStatus,
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.reserveApproval.dataSource,
      page: (state) => state.reserveApproval.page,
      pageSize: (state) => state.reserveApproval.pageSize,
      total: (state) => state.reserveApproval.total,
      loading: (state) => state.reserveApproval.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < 10) {
        return false;
      }
      return {
        current: this.page,
        showQuickJumper: true,
        showSizeChanger: true,
        defaultPageSize: this.pageSize,
        pageSize: this.pageSize,
        total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchReservationApprovalList({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchReservationApprovalList({
            page: current,
            pageSize: size,
          });
        },
      };
    },
  },
  methods: {
    ...mapActions({
      fetchReservationApprovalList: 'reserveApproval/fetchReservationApprovalList',
    }),
    getreserveStatusI18Text(row) {
      if (row.status === ReserveStatus.CANCEL) {
        let cancelStatus;
        switch (row.cancelType) {
          case CancelType.OFF:
            cancelStatus = ReserveStatus.OFF;
            break;
          case CancelType.OTHER:
            cancelStatus = ReserveStatus.CANCEL;
            break;
          default:
            cancelStatus = ReserveStatus.CANCEL;
            break;
        }
        return getreserveStatusI18Text(this, cancelStatus);
      }
      return getreserveStatusI18Text(this, row.status);
    },
    formatDate,
    goDetailReserve(row) {
      this.$emit('detail-reserve', row);
    },
  },
};
</script>
