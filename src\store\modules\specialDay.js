import {
  specialDayListApi,
  addSpecialDayApi,
  editSpecialDayApi,
  specialDayDetailApi,
} from '@/api/special-day-api';

const initialState = {
  list: [],
  total: 0,
  loading: false,
  saving: false,
  detail: null,
};

const getters = {};

const mutations = {
  setLoading(state, payload) {
    state.loading = payload;
  },
  setSpecialDayList(state, payload) {
    state.loading = false;
    state.total = payload.total;
    state.list = payload.data;
  },
  setSaving(state, payload) {
    state.saving = payload;
  },
  setDetail(state, payload) {
    state.detail = payload;
  },
};

const actions = {
  // 获取特殊日期列表
  fetchSpecialDayList({ commit }, payload) {
    commit('setLoading', true);
    return new Promise((resolve, reject) => {
      specialDayListApi(payload)
        .then((r) => {
          commit('setSpecialDayList', r.data);
          resolve(r.data);
        })
        .catch((e) => reject(e));
    });
  },
  // 新建特殊日期
  createSpecialDay({ commit }, payload) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      addSpecialDayApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setSaving', false);
        });
    });
  },
  // 修改特殊日期
  editSpecialDay({ commit }, payload) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      editSpecialDayApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setSaving', false);
        });
    });
  },
  // 获取特殊日期详情
  fetchSpecialDayDetail({ commit }, payload) {
    return new Promise((resolve, reject) => {
      specialDayDetailApi(payload)
        .then((r) => {
          commit('setDetail', r.data.model);
          resolve(r.data);
        })
        .catch((e) => reject(e));
    });
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  mutations,
  actions,
};
