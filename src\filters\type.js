import type from '../constants/unavailable-type';
import vueI18n from '../locales/index';

// eslint-disable-next-line import/prefer-default-export
export const typeI18n = (value) => {
  const mapper = new Map([
    [type.RESERVATION_UNAVAILABLE, vueI18n.t('special.type.reservation_unavailable')],
    [type.VENUE_UNAVAILABLE, vueI18n.t('special.type.venue_unavailable')],
  ]);
  return mapper.get(value);
};
