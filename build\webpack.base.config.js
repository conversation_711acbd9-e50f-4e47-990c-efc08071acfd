// import path from 'path';
import webpack from 'webpack';
import VueLoaderPlugin from 'vue-loader/lib/plugin';
import DotenvWebpackPlugin from 'dotenv-webpack';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import CopyPlugin from 'copy-webpack-plugin';
import dotenvConfig from '../setup-dotenv';

const path = require('path');
const Dotenv = require('dotenv-webpack');

const isProd = process.env.NODE_ENV === 'production';

const resolve = (...args) => path.resolve(__dirname, ...args);

const dotenvFilePath = () => {
  const f = process.env.NODE_ENV === 'test' ? '.env.test' : '.env';
  return resolve('../', f);
};

const webpackConfig = {
  mode: !isProd ? 'development' : 'production',
  // node: {
  //   net: 'empty',
  //   tls: 'empty',
  //   dns: 'empty',
  // },
  devtool: !isProd ? 'inline-source-map' : 'source-map',
  target: ['web', 'es5'],
  output: {
    path: resolve(__dirname, '../dist'),
    filename: !isProd ? 'static/js/[name].js' : 'static/js/[name].[contenthash:8].js',
    chunkFilename: !isProd ? 'static/js/[name].js' : 'static/js/[name].[chunkhash:8].js',
    publicPath: `${process.env.SERVICE_URL}${process.env.PUBLIC_PATH}`,
    environment: {
      // 是否使用箭头函数
      arrowFunction: false,
    },
  },
  resolve: {
    extensions: ['.js', '.vue', '.json', '.less'],
    alias: {
      '@': resolve(__dirname, '../src'),
      '@assets': resolve(__dirname, '../src/assets'),
      '@styles': resolve(__dirname, '../src/styles'),
      '@components': resolve(__dirname, '../src/components'),
      '@api': resolve(__dirname, '../src/api'),
      '@config': resolve(__dirname, '../src/config'),
      '@mixins': resolve(__dirname, '../src/mixins'),
      '@utils': resolve(__dirname, '../src/utils'),
      '@views': resolve(__dirname, '../src/views'),
      '@server': resolve(__dirname, '../server'),
      '~': resolve(__dirname, '../node_modules'),
    },
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: file => /node_modules/.test(file) && !/(\.vue|\.js)/.test(file),
        use: [
          {
            loader: 'babel-loader',
          },
        ],
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10240,
          name: !isProd ? 'static/img/[name].[ext]' : 'static/img/[name].[hash:8].[ext]',
          esModule: false,
        },
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10240,
          name: !isProd ? 'static/fonts/[name].[ext]' : 'static/fonts/[name].[hash:8].[ext]',
          esModule: false,
        },
      },
    ],
  },
  externals: {
    // pimaUi: 'pimaUi',
    // pimaUiSider: 'pimaUiSider',
    pimaUiImport: 'pimaUiImport',
    pimaUiSelectPost: 'pimaUiSelectPost',
  },
  plugins: [
    new VueLoaderPlugin(),
    new Dotenv({
      path: dotenvFilePath(),
    }),
    new DotenvWebpackPlugin({
      path: dotenvConfig.path,
    }),
    new MiniCssExtractPlugin({
      filename: 'static/css/[name].[contenthash:8].css',
      chunkFilename: 'static/css/[name].[chunkhash:8].css',
    }),
    new CopyPlugin({
      patterns: [
        {
          from: resolve('../public/'),
          to: resolve('../dist/static/'),
        },
      ],
    }),
    // new CopyWebpackPlugin([
    //   {
    //     from: path.resolve(__dirname, '../public'),
    //     to: path.resolve(__dirname, '../dist/static'),
    //   },
    // ]),
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
  ],
  performance: {
    hints: 'warning',
  },
};

export default webpackConfig;
