{"name": "pkusz-venue-reservation-nodejs", "version": "2.0.0", "description": "场馆预约", "author": "doocom <<EMAIL>>", "private": true, "scripts": {"build:client": "cross-env NODE_ENV=production webpack --config build/webpack.client.js --progress", "build:server": "cross-env NODE_ENV=production webpack --config build/webpack.server.js --progress", "build": "rimraf dist && npm run build:client && npm run build:server", "server": "nodemon -w app.js -w server app.js", "start": "cross-env NODE_ENV=production node app.js", "test": "cross-env NODE_ENV=test jest --forceExit --runInBand", "analyz": "cross-env NODE_ENV=production npm_config_report=true npm run build:client", "coverage": "cat ./coverage/lcov.info | coveralls", "lint": "eslint --ext .js --ext .vue ./", "commit": "git-cz"}, "dependencies": {"@babel/polyfill": "^7.8.7", "@babel/runtime": "^7.8.3", "@doocom/pima-cas-middleware": "^1.4.11", "@doocom/pima-locale-middleware": "^1.4.1", "@doocom/pima-proxy-middleware": "^1.3.4", "@doocom/pima-vue-ssr-middleware": "^1.5.1", "@popperjs/core": "^2.5.4", "FormData": "^0.10.1", "ant-design-vue": "^1.6.5", "axios": "^0.24.0", "body-parser": "^1.19.0", "cas-authentication": "^0.0.8", "connect-redis": "^6.0.0", "cookie-parser": "^1.4.5", "core-js": "^3.18.3", "dotenv": "^8.2.0", "express": "^4.18.2", "express-session": "^1.17.3", "jquery": "^3.4.1", "js-base64": "^3.7.2", "js-cookie": "^2.2.1", "js-md5": "^0.8.3", "lodash": "^4.17.15", "mammoth": "^1.4.21", "md5": "^2.3.0", "moment": "^2.24.0", "morgan": "^1.10.0", "nprogress": "^0.2.0", "pinia": "^2.0.27", "qiankun": "^2.10.16", "redis": "^3.1.2", "redlock": "^4.2.0", "regenerator-runtime": "0.13.9", "request": "~2.33.0", "urijs": "^1.19.2", "uuid": "^8.3.2", "vue": "^2.7.14", "vue-client-only": "^2.1.0", "vue-i18n": "^8.26.5", "vue-jest": "^3.0.5", "vue-router": "^3.5.3", "vue-server-renderer": "^2.7.14", "vue-ueditor-wrap": "2.x", "vuex": "^3.6.2", "xml2js": "^0.4.23"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/eslint-parser": "^7.16.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.8.3", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/register": "^7.15.8", "@loadable/babel-plugin": "^5.13.2", "@loadable/webpack-plugin": "^5.15.1", "@mapbox/stylelint-processor-arbitrary-tags": "^0.4.0", "@vue/test-utils": "^1.0.0-beta.31", "autoprefixer": "^9.7.4", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-jest": "^25.1.0", "babel-loader": "^8.2.3", "babel-plugin-dynamic-import-node": "^2.3.0", "babel-plugin-dynamic-import-webpack": "^1.1.0", "babel-plugin-import": "^1.13.3", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-transform-require-context": "^0.1.1", "clean-webpack-plugin": "^4.0.0", "commitizen": "^4.0.3", "copy-webpack-plugin": "^9.0.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "css-loader": "^5.2.7", "cz-conventional-changelog": "^3.1.0", "dotenv-webpack": "^7.0.3", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-stylelint": "^15.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-html": "^6.2.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jest": "^25.2.4", "eslint-plugin-vue": "^8.0.3", "eslint-webpack-plugin": "^3.0.1", "file-loader": "^6.2.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^5.4.0", "http-proxy-middleware": "^2.0.3", "identity-obj-proxy": "^3.0.0", "indexof": "^0.0.1", "jest": "^27.3.1", "less": "^3.13.1", "less-loader": "^10.2.0", "memory-fs": "^0.5.0", "mime-types": "^2.1.26", "mini-css-extract-plugin": "^2.4.2", "mockjs": "^1.1.0", "nodemon": "^2.0.2", "open": "^7.0.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "postcss": "^8.3.10", "postcss-less": "^5.0.0", "postcss-loader": "^6.2.0", "postcss-preset-env": "^6.7.0", "rimraf": "^3.0.1", "style-loader": "^3.3.0", "stylelint": "^14.0.1", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^24.0.0", "stylelint-less": "^1.0.1", "stylelint-order": "^5.0.0", "stylelint-processor-html": "^1.0.0", "stylelint-webpack-plugin": "^3.1.0", "stylus": "^0.54.7", "stylus-loader": "^2.4.0", "terser-webpack-plugin": "^5.2.4", "typescript": "^3.7.5", "url-loader": "^4.1.1", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "vue-template-compiler": "~2.6.11", "webpack": "^5.61.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.1", "webpack-dev-middleware": "^5.2.1", "webpack-federation-module-id-plugin": "^1.0.0", "webpack-federation-stats-plugin": "^1.0.1", "webpack-hot-client": "^4.2.0", "webpack-hot-middleware": "^2.25.1", "webpack-merge": "^5.8.0", "webpack-node-externals": "^3.0.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "engines": {"node": ">= 14.0.0", "npm": ">= 6.0.0"}}