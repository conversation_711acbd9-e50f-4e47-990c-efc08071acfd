<template>
  <div class="emtry-result">
    <img
      src="./emtry-result.png"
      class="emtry-result-icon"
    >
    <div
      v-if="notice"
      class="emtry-result-notice"
    >
      {{ notice }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmtryResult',

  props: {
    notice: {
      type: String,
      default() {
        return this.$t('msg.emtryData');
      },
    },
  },
};
</script>

<style lang="less">
.emtry-result {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.emtry-result-icon {
  width: 235px;
  height: 174px;
}
.emtry-result-notice {
  font-size: 14px;
  font-weight: 300;
  color: rgba(0, 0, 0, 0.65);
  line-height: 20px;
  margin-top: 22px;
}
.ant-table .emtry-result {
  height: 400px;
}
</style>
