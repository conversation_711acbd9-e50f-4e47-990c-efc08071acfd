<template>
  <div class="table-header-period">
    <div class="table-header-period-wrap">
      <div class="split-title split-title-vuene">
        {{ t('date') }}
      </div>
      <div class="split-title split-title-period">
        {{ t('period') }}
      </div>
    </div>
  </div>
</template>


<script>
import { nsI18n } from '@/mixins/ns-i18n';

export default {
  mixins: [
    nsI18n('t', 'venueTimeSetting.timeSetting.tableList.columns'),
  ],
};
</script>

<style lang="less" scoped>
.table-header-period {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top right, #f8f8f8, #f8f8f8 48.5%, #e5e5e5, #f8f8f8 50.5%, #f8f8f8);

  .table-header-period-wrap {
    position: relative;
    width: 100%;
    height: 100%;

    .split-title {
      position: absolute;
      color: fadeout(#000, 15%);
      font-size: 12px;
      font-weight: 300;
      width: 100%;
      height: auto;

      &.split-title-vuene {
        top: 6px;
        right: 8px;
        text-align: right;
      }

      &.split-title-period {
        width: 60px;
        bottom: 4px;
        left: 8px;
        text-align: left;
      }
    }
  }
}
</style>
