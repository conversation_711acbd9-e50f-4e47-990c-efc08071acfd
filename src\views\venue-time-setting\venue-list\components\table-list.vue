<template>
  <a-table
    :data-source="dataSource"
    :loading="isLoading"
    :columns="columns"
    :row-key="record => record.id"
    :pagination="pagination"
    class="pima-table"
    :scroll="{ y: 'calc(100vh - 212px)' }"
  >
    <template
      slot="venueNumber"
      slot-scope="text, record"
    >
      <a @click="onClickVenueNumber(record)">
        {{ text }}
      </a>
    </template>

    <template
      slot="venueName"
      slot-scope="text, record"
    >
      {{ record.name }}
    </template>

    <template
      slot="venueCategory"
      slot-scope="text, record"
    >
      <span>
        {{ record.venueCategoryName }}
      </span>
    </template>

    <template
      slot="appointmentRule"
      slot-scope="text, record"
    >
      {{ record.reservationRule && record.reservationRule.name }}
    </template>

    <template
      slot="lastAction"
      slot-scope="record"
    >
      {{ record.updateUserName }}<br>
      {{ record.updateTime }}
    </template>

    <template
      slot="status"
      slot-scope="text"
    >
      <span :class="text?'status_success':'status_error'">
        {{ text | venueStatusI18n }}
      </span>
    </template>

    <template
      slot="action"
      slot-scope="text, record"
    >
      <a
        v-if="hasP((P) => P.VenueTimeSetting.TimeSetting)"
        class="action_link nowrap"
        @click="onClickTimeSetting(record)"
      >
        {{ t('action.timeSetting') }}
      </a>
    </template>
  </a-table>
</template>


<script>
import _ from 'lodash';
import { venueStatusI18n } from '@/filters/venue';
import OpreationMixin from '@/mixins/operation';
import {
  getVenueCategoryApi,
} from '@api/service-query-api';
import FormatDateMixin from '@/mixins/formatDate';
import { getVenueCategoryI18Text } from '@views/basic-data/components/handler';
import { nsI18n } from '@/mixins/ns-i18n';
import { createTableColumns } from './table-columns';

export default {
  name: 'VenueTimeSettingTableList',

  filters: {
    venueStatusI18n,
  },

  mixins: [
    OpreationMixin,
    FormatDateMixin,
    nsI18n('t', 'venueTimeSetting.tableList'),
  ],

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: true,
    },
    pageInfo: {
      type: Object,
      default: () => ({
        page: 1,
        limit: 10,
      }),
    },
    totalSize: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      venueCategoryMap: {},
      detail: {
        show: false,
        id: null,
      },
    };
  },

  computed: {
    columns() {
      return createTableColumns();
    },
    pagination() {
      const self = this;
      const showQuickJumper = this.totalSize / this.pageInfo.limit > 1;
      if ((self.totalSize || 0) < 10) {
        return false;
      }
      return {
        showQuickJumper,
        showSizeChanger: true,
        current: self.pageInfo.page,
        defaultPageSize: self.pageInfo.limit,
        total: self.totalSize || 0,
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, limit) {
          self.$emit('pageChange', page, limit);
        },
        showTotal(total) {
          self.total = total;
          const totalPage = Math.ceil(self.totalSize / self.pageInfo.limit);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        onShowSizeChange(cur, size) {
          self.$emit('pageChange', cur, size);
        },
      };
    },
  },
  beforeMount() {
  // 动态获取场馆类别
    getVenueCategoryApi()
      .then(({ model = [] }) => {
        const venueCategoryMap = new Map();
        (model.map((item) => [item.code, item.name])).forEach((item) => {
          venueCategoryMap.set(...item);
        });
        this.venueCategoryMap = venueCategoryMap;
      })
      .catch((err) => {
        this.$message.error(err.response.data.errorMsg);
      });
  },

  methods: {
    getVenueCategoryI18Text(progress) {
      return getVenueCategoryI18Text(progress, this.venueCategoryMap);
    },
    onClickVenueNumber(record) {
      this.$emit('view', _.cloneDeep(record));
    },

    onClickTimeSetting(record) {
      const { id } = record;
      this.$router.push({
        name: 'venue-time-list.time-setting',
        query: {
          id,
        },
      });
    },
  },
};
</script>
