<template>
  <PopModal
    :title="$t('blacklist.title.editSetting')"
    :width="940"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="drawer-bd">
      <a-form
        :form="form"
        :colon="false"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item
          :label="$t('blacklist.form.usefulType')"
        >
          {{ getReservationType(formData.reservationType) }}
        </a-form-item>
        <a-form-item
          :label="$t('blacklist.form.isAutoAdd')"
        >
          <a-radio-group
            v-decorator="['isAutoAdd', {
              initialValue: formData.isAutoAdd,
              rules: [
                { required: true, message: $t('blacklist.place.isAutoAdd') },
              ]
            }]"
            @change="isAutoBlacklistChange"
          >
            <a-radio :value="true">
              {{ $t('common.YES') }}
            </a-radio>
            <a-radio :value="false">
              {{ $t('common.NO') }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formData.isAutoAdd"
          :label="$t('blacklist.form.autoAddType')"
        >
          <div class="card">
            <a-radio-group
              v-decorator="['autoAddType', {
                initialValue: formData.autoAddType,
                rules: [
                  { required: true, message: $t('blacklist.place.autoAddType') },
                ]
              }]"
              @change="isAutoAddRuleChange"
            >
              <a-radio
                :value="AUTO_ADD_TYPE.MONTH"
                class="radio-box"
              >
                <a-form-item
                  label=""
                  class="inline"
                >
                  <div class="card-item-one">
                    <a-input-number
                      v-decorator="['months', {
                        initialValue: formData.months,
                        rules: [
                          { validator: checkMonth }
                        ]
                      }]"
                      :disabled="formData.autoAddType!==AUTO_ADD_TYPE.MONTH"
                      :min="1"
                      :max="12"
                      :precision="0"
                    />
                    <span class="require-class">{{ $t('blacklist.msg.autoAddMsg[0]') }}</span>
                  </div>
                </a-form-item>
                <a-form-item
                  label=""
                  class="inline"
                >
                  <div class="card-item-one">
                    <a-input-number
                      v-decorator="['monOffTimes', {
                        initialValue: formData.monOffTimes,
                        rules: [
                          { validator: checkTimesMonth },
                        ]
                      }]"
                      :disabled="formData.autoAddType!==AUTO_ADD_TYPE.MONTH"
                      :min="1"
                      :precision="0"
                    />
                    <span class="require-class">{{ $t('blacklist.msg.autoAddMsg[1]') }}</span>
                  </div>
                </a-form-item>
              </a-radio>
              <a-radio
                :value="AUTO_ADD_TYPE.STAT"
                class="radio-box"
              >
                <a-form-item
                  label=""
                  class="inline"
                >
                  <div class="card-item-one">
                    <span class="require-class">{{ $t('blacklist.msg.autoAddMsg[2]') }}</span>
                    <a-input-number
                      v-decorator="['statOffTimes', {
                        initialValue: formData.statOffTimes,
                        rules: [
                          { validator: checkTimesStat },
                        ]
                      }]"
                      :min="1"
                      :precision="0"
                      :disabled="formData.autoAddType!==AUTO_ADD_TYPE.STAT"
                    />
                    <span class="require-class">{{ $t('blacklist.msg.autoAddMsg[3]') }}</span>
                  </div>
                </a-form-item>
              </a-radio>
            </a-radio-group>
          </div>
        </a-form-item>
        <a-form-item
          :label="$t('blacklist.form.isAutoRemove')"
        >
          <a-radio-group
            v-decorator="['isAutoRemove', {
              initialValue: formData.isAutoRemove,
              rules: [
                { required: true, message: $t('blacklist.place.isAutoRemove') },
              ]
            }]"
            @change="isCancelBlacklistChange"
          >
            <a-radio :value="true">
              {{ $t('common.YES') }}
            </a-radio>
            <a-radio :value="false">
              {{ $t('common.NO') }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formData.isAutoRemove"
          :label="$t('blacklist.form.rule')"
          :extra="$t('blacklist.msg.setTip')"
        >
          {{ $t('blacklist.msg.cancelTip[0]') }}
          <a-input-number
            v-decorator="['autoRemoveDays', {
              initialValue: formData.autoRemoveDays,
              rules: [
                {required: true, message: $t('blacklist.place.systemDay')}
              ],
            }]"
            :min="1"
            :precision="0"
          />
          {{ $t('blacklist.msg.cancelTip[1]') }}
        </a-form-item>
      </a-form>
      <div class="drawer_footer">
        <a-button
          type="link"
          @click="onClose"
        >
          {{ $t('action.cancle') }}
        </a-button>
        <a-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ $t('action.ok') }}
        </a-button>
      </div>
    </div>
  </PopModal>
</template>

<script>
import PopModal from '@/components/base/pop-modal.vue';
import { AUTO_ADD_TYPE } from '@/constants/venue';

export default {
  name: 'BlackRuleSetting',

  components: {
    PopModal,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    dataSource: {
      type: Object,
      default: () => {},
    },
    reservationTypeOptions: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      realValue: this.value,
      AUTO_ADD_TYPE,
      formData: {
        autoAddType: AUTO_ADD_TYPE.MONTH,
        autoRemoveDays: 0,
        isAutoAdd: false,
        isAutoRemove: false,
        months: 0,
        statOffTimes: 0,
        monOffTimes: 0,
        reservationType: 'common',
      },
      loading: false,
    };
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  mounted() {
    this.$store.dispatch('blacklist/fetchBlackRuleDetail', {
      id: this.dataSource.id,
    })
      .then(({ model }) => {
        this.formData = model;
        if (model.autoAddType === AUTO_ADD_TYPE.MONTH) {
          this.formData.monOffTimes = model.offTimes;
        }
        if (model.autoAddType === AUTO_ADD_TYPE.STAT) {
          this.formData.statOffTimes = model.offTimes;
        }
        this.form.setFieldsValue(this.formData);
      });
  },

  methods: {
    onClose() {
      this.realValue = false;
    },
    handleConfirm() {
      const { form } = this;
      form.validateFields((err) => {
        if (!err) {
          const values = form.getFieldsValue();
          const payload = {
            ...values,
            reservationType: this.formData.reservationType,
          };
          if (payload.autoAddType === AUTO_ADD_TYPE.MONTH) {
            payload.offTimes = values.monOffTimes;
          }
          if (payload.autoAddType === AUTO_ADD_TYPE.STAT) {
            payload.offTimes = values.statOffTimes;
            payload.months = '';
          }
          this.loading = true;
          this.$store.dispatch('blacklist/editBlackRule', {
            id: this.dataSource.id,
            data: payload,
          })
            .then(() => {
              this.$message.success(this.$t('blacklist.msg.editSucc'));
              this.$emit('update');
              this.onClose();
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    isAutoBlacklistChange(e) {
      this.formData.isAutoAdd = e.target.value;
      if (e.target.value) {
        this.formData.autoAddType = AUTO_ADD_TYPE.MONTH;
      }
    },
    isCancelBlacklistChange(e) {
      this.formData.isAutoRemove = e.target.value;
    },
    isAutoAddRuleChange(e) {
      this.formData.autoAddType = e.target.value;
    },
    getReservationType(code) {
      const type = this.reservationTypeOptions.filter((item) => item.code === code);
      if (type.length) {
        return type[0].label;
      }
      return '';
    },
    checkMonth(rule, value, cb) {
      const autoAddType = this.form.getFieldValue('autoAddType');
      const months = this.form.getFieldValue('months');
      if (autoAddType !== AUTO_ADD_TYPE.MONTH) {
        cb();
      } else if (!months) {
        cb(this.$t('blacklist.place.systemMonth'));
      } else {
        cb();
      }
    },
    checkTimesMonth(rule, value, cb) {
      const autoAddType = this.form.getFieldValue('autoAddType');
      const monOffTimes = this.form.getFieldValue('monOffTimes');
      if (autoAddType !== AUTO_ADD_TYPE.MONTH) {
        cb();
      } else if (!monOffTimes) {
        cb(this.$t('blacklist.place.systemCount'));
      } else {
        cb();
      }
    },
    checkTimesStat(rule, value, cb) {
      const autoAddType = this.form.getFieldValue('autoAddType');
      const statOffTimes = this.form.getFieldValue('statOffTimes');
      if (autoAddType !== AUTO_ADD_TYPE.STAT) {
        cb();
      } else if (!statOffTimes) {
        cb(this.$t('blacklist.place.systemCount'));
      } else {
        cb();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.card {
  background: #FCFCFC;
  border: 1px solid #E4E4E4;
  .card-item-one {
    margin: 10px;
  }
  .inline {
    display: inline-block;
    margin-bottom: 0;
  }
  .radio-box {
    display: inline-flex;
    ::v-deep .ant-radio {
      margin: 18px -8px 0 10px;
    }
  }
}
::v-deep .ant-form-extra {
  font-size: 12px;
  color: #8D0306;
}
</style>
