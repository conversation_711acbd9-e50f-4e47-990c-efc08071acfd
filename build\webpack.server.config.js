// import path from 'path';
import webpack from 'webpack';
import webpackMerge from 'webpack-merge';
import VueSSRServerPlugin from 'vue-server-renderer/server-plugin';
import baseWebpackConfig from './webpack.base.config';

const { ModuleFederationPlugin } = require('webpack').container;

const webpackConfig = webpackMerge(baseWebpackConfig, {
  // entry: path.resolve(__dirname, '../src/entry-server.js'),
  entry: {
    app: './src/entry-server.js',
  },
  target: 'node',
  output: {
    filename: 'server-bundle.js',
    libraryTarget: 'commonjs2',
  },
  externalsPresets: { node: true },
  externals: ['enhanced-resolve'],
  module: {
    rules: [
      {
        test: /\.(c|le)ss$/,
        use: [
          {
            loader: 'css-loader',
          },
          {
            loader: 'less-loader',
            options: {
              lessOptions: {
                javascriptEnabled: true,
              },
            },
          },
        ],
      },
    ],
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'process.env.VUE_ENV': '"server"',
    }),
    new VueSSRServerPlugin(),
    new ModuleFederationPlugin({
      name: 'consumer',
      filename: 'remoteEntry.js',
      remotes: {
        PimaRemoteUI: `PimaRemoteUI@${process.env.PIMA_REMOTE_UI_ENTRY_URL}`,
      },
    }),
  ],
});

export default webpackConfig;
