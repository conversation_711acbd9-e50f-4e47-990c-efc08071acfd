<template>
  <div class="drawer-bd">
    <a-spin :spinning="loading">
      <a-form
        :form="form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :colon="false"
      >
        <a-form-item
          :label="label.name"
        >
          <a-input
            v-decorator="['name', {
              initialValue: formData.name,
              rules: [
                { required: true, message: $t('basicData.place.categoryName') },
                { max: 20, message: $t('form.maxLength', { num: 20 }) },
              ]
            }]"
            :placeholder="$t('basicData.place.categoryName')"
          />
        </a-form-item>
        <a-form-item
          :label="label.enName"
        >
          <a-input
            v-decorator="['enName', {
              initialValue: formData.enName,
              rules: [
                { max: 50, message: $t('form.maxLength', { num: 50 }) },
              ]
            }]"
            :placeholder="$t('basicData.place.categoryNameEn')"
          />
        </a-form-item>

        <a-form-item
          :label="label.categoryIcon"
          :extra="$t('basicData.place.categoryIconExtra')"
        >
          <a-upload
            v-decorator="['categoryIcon', {
              initialValue: formData.categoryIcon,
              rules: [
                { required: true, message: $t('basicData.place.categoryIcon') },
              ]
            }]"
            list-type="picture-card"
            class="icon-uploader"
            accept=".png,.jpg,.jpeg"
            :show-upload-list="false"
            :custom-request="handleUploadIconRequest"
            :remove="handleIconRemove"
          >
            <img
              v-if="categoryIconUrl || formData.categoryIcon"
              :src="categoryIconUrl || formData.categoryIcon"
              alt="icon"
              width="100"
              height="100"
              style="object-fit: contain;"
            >
            <div v-else>
              <a-icon :type="uploading ? 'loading' : 'plus'" />
              <div class="ant-upload-text">
                {{ $t('action.upload') }}
              </div>
            </div>
          </a-upload>
        </a-form-item>

        <a-form-item
          v-if="type === 'edit'"
          :label="$t('basicData.form.venueVos')"
        >
          <span v-if="venueVos">
            {{ venueVos.map(e => e.name).join('、') }}
          </span>
        </a-form-item>

        <a-form-item
          :label="label.isEnable"
          required
        >
          <a-radio-group
            v-decorator="['isEnable', {
              initialValue: formData.isEnable,
            }]"
          >
            <a-radio :value="true">
              {{ $t('basicData.status.TRUE') }}
            </a-radio>
            <a-radio :value="false">
              {{ $t('basicData.status.FALSE') }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-spin>
    <div class="drawer_footer">
      <a-button
        type="link"
        @click="onClose"
      >
        {{ $t('action.cancle') }}
      </a-button>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ $t('action.save') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { getTypeOptions } from '@/utils/unavailable-type-options';

export default {
  name: 'CategoryForm',

  props: {
    type: {
      type: String,
      default: 'add',
    },
    id: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      label: {
        name: this.$t('basicData.form.categoryName'),
        enName: this.$t('basicData.form.categoryNameEn'),
        categoryIcon: this.$t('basicData.form.categoryIcon'),
        isEnable: this.$t('basicData.form.status'),
      },
      formData: {
        name: '',
        enName: '',
        categoryIcon: null,
        isEnable: true,
      },
      categoryIconUrl: '',
      venueVos: '',
      typeOptions: getTypeOptions(this),
      uploading: false,
    };
  },

  computed: {
    ...mapState({
      loading: (state) => state.basicDataCategory.loading,
      saving: (state) => state.basicDataCategory.saving,
    }),
  },

  mounted() {
    if (this.type === 'edit') {
      this.$store.dispatch('basicDataCategory/fetchCategoryDetail', {
        id: this.id,
      })
        .then(({ model }) => {
          const data = {
            ...model,
          };
          // this.form.setFieldsValue(data);
          this.form.setFieldsValue({
            name: data.name,
            enName: data.enName,
            isEnable: data.isEnable,
            categoryIcon: data.attachmentVos[0].id,
          });
          this.categoryIconUrl = data.attachmentVos[0].filePath.normal;
          this.venueVos = data.venueVos;
        });
    }
  },

  methods: {
    ...mapActions({
      uploadCategoryIcon: 'basicDataCategory/uploadCategoryIcon',
    }),
    onClose() {
      this.$emit('close');
    },
    handleUploadIconRequest({ file }) {
      this.uploading = true;
      this.uploadCategoryIcon({ fileData: file, relateType: 'VENUE_CATEGORY' }).then((data) => {
        this.form.setFieldsValue({ categoryIcon: [data.id] });
        this.formData.categoryIcon = data.id;
        this.categoryIconUrl = data.filePath.normal;
        this.uploading = false;
      }).catch((err) => {
        this.uploading = false;
        if (err.message) {
          this.$message.error(err.message);
        }
      });
    },
    handleIconRemove() {
      this.fileList = [];
    },
    handleSubmit() {
      const formate = {
        name: (r) => r,
        enName: (r) => r,
        isEnable: (r) => r,
      };
      const { form } = this;
      form.validateFields((err, values) => {
        if (!err) {
          const payload = {};
          const r = Object.keys(values);
          r.forEach((key) => {
            if (values[key] || values[key] === 0 || values[key] === false) {
              if (formate[key]) {
                const val = formate[key](values[key]);
                payload[key] = val;
              } else {
                payload[key] = values[key];
              }
            }
          });
          payload.attachmentIds = [this.formData.categoryIcon];
          if (this.type === 'add') {
            // 新增
            this.$store.dispatch('basicDataCategory/createCategory', payload)
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success(this.$t('basicData.msg.saveSucc'));
                  this.$emit('update');
                  this.onClose();
                }
              });
          } else if (this.type === 'edit') {
            // 编辑
            this.$store.dispatch('basicDataCategory/editCategory', {
              data: payload,
              id: this.id,
            })
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success(this.$t('basicData.msg.saveSucc'));
                  this.$emit('update');
                  this.onClose();
                }
              });
          }
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
  ::v-deep .ant-checkbox-group {
    display: flex;
    flex-wrap: wrap;
  }
  ::v-deep .ant-checkbox-group-item {
    display: flex;
    width: 31%;
    margin-bottom: 12px;
  }
</style>
