<template>
  <div class="drawer-bd">
    <a-form
      :form="form"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      :colon="false"
    >
      <a-form-item
        :label="label.name"
      >
        <a-input
          v-decorator="['name', {
            initialValue: formData.name,
            rules: [
              { required: true, message: $t('special.place.name') },
              { max: 150, message: $t('form.maxLength', { num: 150 }) },
            ]
          }]"
          :placeholder="$t('special.place.name')"
        />
      </a-form-item>
      <a-form-item
        :label="label.enName"
      >
        <a-input
          v-decorator="['enName', {
            initialValue: formData.enName,
            rules: [
              { required: true, message: $t('special.place.enName') },
              { max: 150, message: $t('form.maxLength', { num: 150 }) },
            ]
          }]"
          :placeholder="$t('special.place.enName')"
        />
      </a-form-item>
      <a-form-item
        :label="label.startDate"
      >
        <a-date-picker
          v-decorator="['startDate', {
            initialValue: formData.startDate,
            rules: [
              { required: true, message: $t('special.place.startDate') },
            ]
          }]"
          :placeholder="$t('special.place.startDate')"
        />
      </a-form-item>
      <a-form-item
        :label="label.endDate"
      >
        <a-date-picker
          v-decorator="['endDate', {
            initialValue: formData.endDate,
            rules: [
              { required: true, message: $t('special.place.endDate') },
            ]
          }]"
          :placeholder="$t('special.place.endDate')"
        />
      </a-form-item>
      <a-form-item
        required
        :label="label.type"
      >
        <a-radio-group
          v-decorator="['type', {
            initialValue: formData.type,
            rules: [
              { required: true, message: $t('special.place.type') },
            ]
          }]"
          :options="typeOptions"
        />
      </a-form-item>
      <a-form-item
        :label="label.venueIds"
      >
        <a-spin
          v-if="loadingVenue"
        />
        <a-checkbox-group
          v-else
          v-decorator="['venueIds', {
            initialValue: formData.venueIds,
            rules: [
              { required: true, message: $t('special.place.venueIds') },
            ]
          }]"
          name="checkboxgroup"
          :options="venueOptions"
        />
      </a-form-item>
      <a-form-item
        :label="label.remark"
      >
        <a-textarea
          v-decorator="['remark', {
            initialValue: formData.remark,
            rules: [
              { max: 255, message: $t('form.maxLength', { num: 255 }) },
            ]
          }]"
          :auto-size="{ minRows: 4, maxRows: 5 }"
        />
      </a-form-item>
      <a-form-item
        :label="label.isEnable"
        required
      >
        <a-radio-group
          v-decorator="['isEnable', {
            initialValue: formData.isEnable,
          }]"
        >
          <a-radio :value="true">
            {{ $t('special.enable.true') }}
          </a-radio>
          <a-radio :value="false">
            {{ $t('special.enable.false') }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
    <div class="drawer_footer">
      <a-button
        type="link"
        @click="onClose"
      >
        {{ $t('action.cancle') }}
      </a-button>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ $t('action.ok') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getTypeOptions } from '@/utils/unavailable-type-options';
import { getVenuesListApi } from '@/api/basic-data-api';

export default {
  name: 'SpecialDayForm',

  props: {
    type: {
      type: String,
      default: 'add',
    },
    id: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      label: {
        name: this.$t('special.form.name'),
        enName: this.$t('special.form.enName'),
        startDate: this.$t('special.form.startDate'),
        endDate: this.$t('special.form.endDate'),
        remark: this.$t('special.form.remark'),
        type: this.$t('special.form.type'),
        isEnable: this.$t('special.form.isEnable'),
        venueIds: this.$t('special.form.venueIds'),
      },
      formData: {
        name: '',
        enName: '',
        startDate: '',
        endDate: '',
        remark: '',
        isEnable: true,
        venueIds: [],
        type: '',
      },
      typeOptions: getTypeOptions(this),
      venueOptions: [],
      loadingVenue: false,
    };
  },

  computed: {
    ...mapState({
      loading: (state) => state.specialDay.saving,
    }),
  },

  mounted() {
    this.loadingVenue = true;
    getVenuesListApi({
      page: 1,
      limit: -1,
    })
      .then((r) => {
        this.venueOptions = r.data.map((item) => ({
          label: item.name,
          value: item.id,
        }));
        this.loadingVenue = false;
      });
    if (this.type === 'edit') {
      this.$store.dispatch('specialDay/fetchSpecialDayDetail', {
        id: this.id,
      })
        .then(({ model }) => {
          const data = {
            ...model,
            startDate: this.$moment(model.startDate),
            endDate: this.$moment(model.endDate),
          };
          this.form.setFieldsValue(data);
        });
    }
  },

  methods: {
    onClose() {
      this.$emit('close');
    },
    handleSubmit() {
      const formate = {
        name: (r) => r,
        enName: (r) => r,
        startDate: (r) => r.format('YYYY-MM-DD'),
        endDate: (r) => r.format('YYYY-MM-DD'),
        remark: (r) => r,
        isEnable: (r) => r,
        venueIds: (r) => r,
        type: (r) => r,
      };
      const { form } = this;
      form.validateFields((err, values) => {
        if (!err) {
          const payload = {};
          const r = Object.keys(values);
          r.forEach((key) => {
            if (values[key] || values[key] === 0 || values[key] === false) {
              if (formate[key]) {
                const val = formate[key](values[key]);
                payload[key] = val;
              } else {
                payload[key] = values[key];
              }
            }
          });
          if (this.type === 'add') {
            // 新增
            this.$store.dispatch('specialDay/createSpecialDay', payload)
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success(this.$t('special.msg.saveSucc'));
                  this.$emit('update');
                  this.onClose();
                }
              });
          } else if (this.type === 'edit') {
            // 编辑
            this.$store.dispatch('specialDay/editSpecialDay', {
              data: payload,
              id: this.id,
            })
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success(this.$t('special.msg.editSucc'));
                  this.$emit('update');
                  this.onClose();
                }
              });
          }
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
  ::v-deep .ant-checkbox-group {
    display: flex;
    flex-wrap: wrap;
  }
  ::v-deep .ant-checkbox-group-item {
    display: flex;
    width: 31%;
    margin-bottom: 12px;
  }
</style>
