import { logStatistic } from '../api/user-log';
import buryPoints from '../constants/log-bury-points';
import buryParams from '../constants/log-bury-params';
import config from '../config';

const mixins = {
  methods: {
    handleLogStatistic(param) {
      if (typeof param === 'function') {
        const data = {
          ...param(buryPoints),
        };
        const payload = this.handleLogParams(param); // 参数

        data.remark = this.handleLogRemark({
          title: data.title,
          payload,
        });
        const apiParams = {
          actionEventCode: data.actionEventCode,
          remark: data.remark,
          serviceCode: config.serviceCode,
          actionResult: data.actionResult || '成功',
        };
        logStatistic(apiParams);
      }
    },
    handleLogParams(param) {
      if (typeof param === 'function') {
        const fieldList = param(buryParams).actionEventCode;
        const { values, langCode, arrValues } = param(buryParams);
        // 如果有传数组参数，就不处理
        if (arrValues) {
          return arrValues;
        }
        if (!values) return undefined;
        const logPayload = {};
        if (fieldList && fieldList.length) {
          fieldList.forEach((field) => {
            if (typeof values[field] !== 'undefined') {
              logPayload[this.$t(`log.${langCode}.${field}`)] = values[field];
            }
          });
          return logPayload;
        }
        return undefined;
      }
      return undefined;
    },
    handleLogRemark(param) {
      const {
        title, payload,
      } = param;
      if (payload) {
        return `${title}\n参数内容：${JSON.stringify(payload || '')}`;
      }
      return `${title}`;
    },
    // 对比不同
    diffFields(oldVal, newVal) {
      if (!Array.isArray(newVal)) {
        // eslint-disable-next-line no-param-reassign
        newVal = this.obj2Array(newVal);
      }
      if (!Array.isArray(oldVal)) {
        // eslint-disable-next-line no-param-reassign
        oldVal = this.obj2Array(oldVal);
      }
      const result = {};
      newVal.forEach((newItem) => {
        const oldItem = oldVal.find((old) => newItem.code === old.code);
        if (typeof oldItem !== 'undefined') {
          const oldValItem = this.changeToString(oldItem.value);
          const newValItem = this.changeToString(newItem.value);
          if (oldValItem !== newValItem) {
            result[newItem.code] = newItem.value;
          }
        }
      });
      return result;
    },
    // 对象变键值数组
    obj2Array(obj) {
      const arr = [];
      Object.keys(obj).forEach((key) => {
        if (obj[key] && obj[key].value) {
          arr.push({
            code: key,
            value: obj[key].value,
          });
        } else {
          arr.push({
            code: key,
            value: obj[key],
          });
        }
      });
      return arr;
    },
    changeToString(val) {
      if (typeof val === 'object') {
        return JSON.stringify(val);
      }
      return String(val);
    },
  },
};

export default mixins;
