export default {
  title: {
    title: '场馆基础数据',
    add: '新增',
    addTitle: '新增场馆',
    editTitle: '编辑场馆',
    category: '类别管理',
    addCategory: '新增类别',
    editCategory: '编辑类别',
  },
  columns: {
    name: '场馆名称',
    number: '场馆编号',
    category: '场馆类别',
    location: '场馆位置',
    peopleNumber: '可容纳人数',
    administrator: '场馆管理员',
    time: '最后操作人/操作时间',
    status: '状态',
    operate: '操作',
    categoryName: '类别名称',
    venueVos: '关联场馆',
    updateTime: '操作人/最后操作时间',
  },
  addVenue: {
    chName: '场馆中文名称',
    enName: '场馆英文名称',
    number: '场馆编号',
    category: '场馆类别',
    attribute: '场馆属性',
    administrator: '场馆管理人员',
    peopleNumber: '可容纳人数',
    location: '场馆位置',
    lockid: '门禁ID',
    remarks: '备注',
    isEnable: '是否启用',
    remarkPlaceHolder: '备注请控制200个字以內',
    selectPlaceHolder: '请选择',
  },
  form: {
    keywordPlace: '场馆名称',
    chName: '请填写场馆中文名称',
    enName: '请填写场馆英文名称',
    number: '请填写场馆编号',
    category: '请选择场馆类别',
    attribute: '请选择场馆属性',
    administrator: '请选择场馆管理人员',
    peopleNumber: '请填写可容纳人数',
    location: '请填写场馆位置',
    remarks: '请填写备注',
    isEnable: '请选择是否启用',
    isNumber: '请输入有效整数',
    cancelType: '请选择取消原因',
    categoryName: '类别中文名称',
    categoryNameEn: '类别英文名称',
    categoryIcon: '类别图标',
    venueVos: '关联场馆',
    status: '状态',
  },
  place: {
    categoryName: '请填写类别中文名称',
    categoryNameEn: '请填写类别英文名称',
    categoryIcon: '请上传类别图标',
    categoryIconExtra: '仅能上传一张png、jpg格式的图片，建议上传图片大小100px*100px',
  },
  action: {
    view: '查看',
    cancel: '取消',
    edit: '编辑',
    addCategory: '新增类别',
  },
  msg: {
    success: '操作成功',
    submitSuccess: '提交成功',
    cancelSuccess: '取消成功',
    confirmCancel: '确定取消报修申请吗？',
    assignSucc: '指派成功',
    handleSucc: '处理成功',
    saveSucc: '保存成功',
    imgType: '只允许上传 {types} 类型图片',
  },
  venueCategory: {
    CLASS_ROOM: '研讨室',
    DANCE_ROOM: '舞蹈室',
    EXERCISE_ROOM: '健身房',
  },
  status: {
    TRUE: '启用',
    FALSE: '停用',
  },
  venueAttribute: {
    INDOOR: '室内',
    OUTDOOR: '室外',
  },
  isTrueStatus: {
    TRUE: '是',
    FALSE: '否',
  },
};
