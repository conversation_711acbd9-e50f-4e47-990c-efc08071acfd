import Qs from 'qs';
import config from '../config';
import httpRequest from './request';

// 批量获取应用参数
export function fetchServiceSysParams(codes) {
  return new Promise((resolve, reject) => {
    httpRequest({
      baseURL: config.serviceApiBaseUrl,
      url: `service-sys-params/code/${config.serviceCode}/batch-get`,
      methods: 'get',
      paramsSerializer(param) {
        return Qs.stringify(param, { indices: false });
      },
      params: { codes },
    })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => reject(e));
  });
}

// 批量更新应用参数
export function updateServiceSysParams(paramList) {
  return new Promise((resolve, reject) => {
    httpRequest({
      baseURL: config.serviceApiBaseUrl,
      url: `service-sys-params/code/${config.serviceCode}/batch-update`,
      method: 'post',
      data: { paramList },
    })
      .then((resp) => {
        resolve(resp);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
