import config from './config';
import createApp from './main';

const isDev = process.env.NODE_ENV !== 'production';

export default (context) => new Promise((resolve, reject) => {
  const start = isDev && Date.now();
  const {
    app,
    router,
    store,
    i18n,
    pinia,
  } = createApp({
    locale: context.appLocale,
    loggedIn: context.appLoggedIn,
    username: context.appUsername,
  });
  const locale = context.appLocale;
  if (locale && config.supportLocales.indexOf(locale) !== -1) {
    i18n.locale = locale;
  }

  router.push(context.url);
  router.onReady(() => {
    const matchedComponents = router.getMatchedComponents();
    // no matched routes
    if (!matchedComponents.length) {
      const err = new Error('Router has not matched compoinents found');
      err.code = 'ROUTE_NOT_FOUND';
      reject(err);
    }

    if (!router.currentRoute?.query?.ticket
      && router.currentRoute?.meta?.requiresAuth !== false
      && context.appLoggedIn === false) {
      const error = new Error('Login required');
      error.code = 'LOGIN_REQUIRED';
      reject(error);
      return;
    }

    // Call fetchData hooks on components matched by the route.
    // A preFetch hook dispatches a store action and returns a Promise,
    // which is resolved when the action is complete and store state has been
    // updated.
    const asyncDataHooks = matchedComponents.filter((c) => c).map(({ asyncData }) => asyncData
        && asyncData({
          store,
          route: router.currentRoute,
        }));
    Promise.all(asyncDataHooks).then(() => {
      if (isDev) {
        // eslint-disable-next-line no-console
        console.log(`Data pre-fetch: ${Date.now() - start}ms`);
      }

      // After all preFetch hooks are resolved, our store is now
      // filled with the state needed to render the app.
      // Expose the state on the render context, and let the request handler
      // inline the state in the HTML response. This allows the client-side
      // store to pick-up the server-side state without having to duplicate
      // the initial data fetching on the client.
      Object.assign(context, {
        state: {
          ...store.state,
          ...pinia.state.value,
        },
        title: i18n.t('title'),
        enableJavaScriptTips: i18n.t('enableJavaScriptTips'),
        appLocale: context.appLocale,
        staticResourcesUrl: config.staticResourcesUrl,
        // state 生成脚本 window.__INITIAL_STATE__
      });

      resolve(app);
    }).catch(reject);
  }, reject);
});
