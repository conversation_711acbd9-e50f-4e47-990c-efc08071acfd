
import { VenueCategory, IsEnbaleMap, VenueAttribute } from '@/constants/venue';

// 场馆类别
const VenueCategoryI18Map = {
  [VenueCategory.CLASS_ROOM]: 'basicData.venueCategory.CLASS_ROOM',
  [VenueCategory.DANCE_ROOM]: 'basicData.venueCategory.DANCE_ROOM',
  [VenueCategory.EXERCISE_ROOM]: 'basicData.venueCategory.EXERCISE_ROOM',
};

export function getVenueCategoryI18Text(progress, venueCategoryMap = new Map()) {
  if (venueCategoryMap.get) {
    return venueCategoryMap.get(progress);
  }
  return null;
}

export function getVenueCategoryI18Options(vm, withAll = false) {
  const opts = Object.entries(VenueCategoryI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      {
        key: 'all',
        value: '',
        title: vm.$t('common.all'),
        label: vm.$t('common.all'),
      },
      ...opts,
    ]
    : [...opts];
}

// 启用状态
const IsEnbaleMapI18Map = {
  [IsEnbaleMap.TRUE]: 'basicData.status.TRUE',
  [IsEnbaleMap.FALSE]: 'basicData.status.FALSE',
};

export function getIsEnbaleMapText(vm, progress) {
  return vm.$t(IsEnbaleMapI18Map[progress]);
}
// 场馆属性
const VenueAttributeI18Map = {
  [VenueAttribute.INDOOR]: 'basicData.venueAttribute.INDOOR',
  [VenueAttribute.OUTDOOR]: 'basicData.venueAttribute.OUTDOOR',
};

export function getVenueAttributeI18Text(vm, progress) {
  return vm.$t(VenueAttributeI18Map[progress]);
}

export function getVenueAttributeI18Options(vm, withAll = false) {
  const opts = Object.entries(VenueAttributeI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      {
        key: 'all',
        value: '',
        title: vm.$t('common.all'),
        label: vm.$t('common.all'),
      },
      ...opts,
    ]
    : [...opts];
}

// 是否状态
const isTrueStatusMapI18Map = {
  [IsEnbaleMap.TRUE]: 'basicData.isTrueStatus.TRUE',
  [IsEnbaleMap.FALSE]: 'basicData.isTrueStatus.FALSE',
};

export function getIsTrueStatusText(vm, progress) {
  return vm.$t(isTrueStatusMapI18Map[progress]);
}

const isTrueEffectMapI18Map = {
  [IsEnbaleMap.TRUE]: 'userNotes.publishAnnounce.form.effect',
  [IsEnbaleMap.FALSE]: 'userNotes.publishAnnounce.form.unEffect',
};

export function getIsTrueEffectText(vm, progress) {
  return vm.$t(isTrueEffectMapI18Map[progress]);
}
