import moment from 'moment';

const formatMap = {
  short: 'YYYY-MM-DD',
  middle: 'YYYY-MM-DD HH:mm',
  long: 'YYYY-MM-DD HH:mm:ss',
  time: 'HH:mm:ss',
};

export function formatDate(date, format = 'long') {
  if (!date) return '';
  return moment(date).format(formatMap[format]);
}

export function formatNull(date, format = 'long') {
  if (!date) {
    return moment(new Date()).format(formatMap[format]);
  }
  return moment(date).format(formatMap[format]);
}

export function getTimeStamp() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1 > 9 ? date.getMonth() : `0${date.getMonth() + 1}`;
  const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`;
  const hours = date.getHours();
  const minutes = date.getMinutes() > 9 ? date.getMinutes() : `0${date.getMinutes()}`;
  // const milliseconds = date.getMilliseconds();
  return `${String(year).substr(2, 2)}${month}${day}${hours}${minutes}`;
}
