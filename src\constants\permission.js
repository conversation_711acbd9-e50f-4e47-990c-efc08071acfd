const permission = Object.freeze({
  // 基础数据
  BasicData: {
    View: 'venue-list-view',
    Add: 'venue-list-insert',
    Edit: 'venue-list-update',
    Category: 'venue-list-category',
  },
  // 预约规则
  ReserveRule: {
    View: 'venue-rule-list-view',
    Add: 'venue-rule-list-insert',
    Edit: 'venue-rule-list-update',
    PeriodSetting: 'venue-rule-list-period-setting',
    Delete: 'venue-rule-list-delete',
  },
  // 预约查询
  ReserveQuery: {
    View: 'venue-reservation-list-view',
    Export: 'venue-reservation-list-export',
    Cancel: 'venue-reservation-list-cancel',
    Blacklist: 'venue-reservation-list-blacklist',
  },
  VenueCalendar: {
    View: 'venue-calendar-view',
    Reserve: 'reservation-calendar-advance',
  },
  VenueTimeSetting: {
    View: 'venue-time-list-view',
    TimeSetting: 'venue-time-list-update',
  },
  // 特殊日期管理
  SpecialDay: {
    View: 'venue-special-date-view',
    Add: 'venue-special-date-add',
    Edit: 'venue-special-date-edit',
    Del: 'venue-special-date-del',
  },
  // 黑名单
  BlackList: {
    View: 'venue-black-list-view',
    ViewRemoveList: 'venue-black-remove-list-view',
    Add: 'venue-black-list-add',
    Del: 'venue-black-list-del',
    Setting: 'venue-black-list-setting',
    SettingEdit: 'venue-black-list-setting-edit',
  },
  VerifyCalendar: {
    View: 'venue-signin-view',
    Signin: 'mini-venue-entry-sign-in',
    Cancel: 'mini-venue-entry-cancel',
  },
  // 说明管理
  UserNotes: {
    View: 'user-notes-view',
    Add: 'user-notes-creat',
    Edit: 'user-notes-edit',
    Delete: 'user-notes-delete',
    AnnouncePublish: 'user-announcement-publish',
    AnnounceEdit: 'user-announcement-edit',
  },

  // 积分管理
  IntegralManagement: {
    View: 'venue-point-manage-view',
    Setting: 'venue-point-manage-rule',
    Detail: 'venue-point-manage-detail',
    Update: 'venue-point-manage-modify',
  },
});

export default permission;
