export const toString = (target) => ((typeof target !== 'object') ? target.toString() : String(target));

export const protoToStr = (target) => Object.prototype.toString.call(target);

export const toPrimitiveType = (target) => protoToStr(target);

export function classOf(target) {
  if (target === null) return 'null';
  if (target === undefined) return 'undefined';
  // eslint-disable-next-line no-self-compare
  if (target !== target) return 'NaN';
  if (!(typeof target === 'object')) return toPrimitiveType(target).slice(8, -1); // slice [object xxx]
  return 'Object';
}

export function mergeOpts(target, origin) {
  if (!origin) return {};
  // eslint-disable-next-line no-restricted-syntax
  for (const key in origin) {
    if (Object.hasOwnProperty.call(origin, key)) {
      if (classOf(key) === 'Object') mergeOpts(key, origin[key]);
      // eslint-disable-next-line no-param-reassign
      target[key] = origin[key];
    }
  }
  return target;
}

export function hasOwn(target, key) {
  if (!target || typeof target !== 'object') return false;
  return Object.prototype.hasOwnProperty.call(target, key);
}
