const { parseString } = require('xml2js');

function log(debug, ...args) {
  if (debug) {
    // eslint-disable-next-line no-console
    console.log.apply(this, ['[pima-cas-middleware][DEBUG]', ...args]);
  }
}

function btoa(str) {
  return Buffer.from(str).toString('base64');
}

function atob(str) {
  return Buffer.from(str, 'base64').toString('ascii');
}

function accessTokenHasExpired({ expiresIn }) {
  if (expiresIn) {
    return Date.now() > new Date(expiresIn).getTime();
  }

  return true;
}

function getJsonFromXML(xmlString) {
  return new Promise((resolve, reject) => {
    parseString(xmlString, (error, result) => {
      if (!error) {
        resolve(result);
      } else {
        reject(error);
      }
    });
  });
}

module.exports = {
  log,
  btoa,
  atob,
  accessTokenHasExpired,
  getJsonFromXML,
};
