<template>
  <a-modal
    :title="title"
    :width="width"
    :destroy-on-close="true"
    :visible="visible"
    :centered="true"
    :footer="null"
    :closable="true"
    :wrap-class-name="`modal-wrap ${className}`"
    @cancel="onClose"
    @ok="onOk"
  >
    <slot />
  </a-modal>
</template>

<script>
export default {
  name: 'Drawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 720,
    },
    title: {
      type: String,
      default: '',
    },
    className: {
      type: String,
      default: '',
    },
  },
  methods: {
    onClose() {
      this.$emit('closeDrawer');
    },
    onOk() {
      this.$emit('closeDrawer');
    },
  },
};
</script>

<style>
</style>
