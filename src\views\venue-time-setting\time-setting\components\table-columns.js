/* eslint-disable arrow-body-style */
import { PeriodType } from '@/constants/venue';
import { namespaceT } from '@/helps/namespace-t';
import TableHeaderPeriod from './table-header-period.vue';
import { getDayByColumnIndex, findCellUnit, convertRecordToType } from './utils';


export const createTableColumns = (vm) => {
  const t = namespaceT('venueTimeSetting.timeSetting.tableList.columns');

  // columnIndex为列的下标，在执行customCell方法时手动指定
  const customCell = (record, rowIndex, columnIndex) => {
    const classList = [];
    if (columnIndex === 8) {
      classList.push('td-enable');
    } else {
      classList.push('td-clickable');

      const today = getDayByColumnIndex(columnIndex);
      const cellData = findCellUnit(record, columnIndex);
      if (cellData) {
        const periodType = convertRecordToType(cellData);
        switch (periodType) {
          case PeriodType.OPENED:
            classList.push('td-status-opened');
            break;
          case PeriodType.CLOSED:
            classList.push('td-status-closed');
            break;
          case PeriodType.LOCKED:
            classList.push('td-status-locked');
            break;
          default:
            break;
        }
      }

      const checked = record.checkedDays.includes(today);
      if (checked) {
        classList.push('td-checked');
      }

      if (columnIndex > 0 && !record.isEnable && !checked) {
        classList.push('td-status-disabled');
      }
    }

    return {
      class: classList,
      props: {
      },
      on: {
        click() {
          if (columnIndex !== 8) {
            vm.onClickCell(record, rowIndex, columnIndex);
          }
        },
        mouseenter: (e) => {
          const tds = document.getElementsByClassName(`venue${columnIndex}`);
          for (let i = 0; i < tds.length; i += 1) {
            tds[i].classList.add('bg-hover');
          }
          const parent = e.target.parentNode;
          parent.classList.add('ant-table-row-hover');
        },
        mouseleave: (e) => {
          const tds = document.getElementsByClassName(`venue${columnIndex}`);
          for (let i = 0; i < tds.length; i += 1) {
            tds[i].classList.remove('bg-hover');
          }
          const parent = e.target.parentNode;
          parent.classList.remove('ant-table-row-hover');
        },
      },
    };
  };

  return [
    {
      title() {
        return vm.$createElement(TableHeaderPeriod);
      },
      dataIndex: 'period',
      scopedSlots: { customRender: 'period' },
      width: 68,
      align: 'center',
      className: 'venue0',
      customHeaderCell(column) {
        const classList = [];
        classList.push('th-period-title-wrap');
        if (column.key === 'period') {
          classList.push('th-column-first');
        }

        return {
          class: classList,
        };
      },
      customCell() {
        return {
          class: 'td-period-title-wrap',
        };
      },
    },
    {
      title: t('monday'),
      dataIndex: 'monday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue1',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 1);
      },
    },
    {
      title: t('tuesday'),
      dataIndex: 'tuesday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue2',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 2);
      },
    },
    {
      title: t('wednesday'),
      dataIndex: 'wednesday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue3',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 3);
      },
    },
    {
      title: t('thursday'),
      dataIndex: 'thursday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue4',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 4);
      },
    },
    {
      title: t('friday'),
      dataIndex: 'friday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue5',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 5);
      },
    },
    {
      title: t('saturday'),
      dataIndex: 'saturday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue6',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 6);
      },
    },
    {
      title: t('sunday'),
      dataIndex: 'sunday',
      scopedSlots: { customRender: 'monday' },
      align: 'center',
      className: 'venue7',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 7);
      },
    },
    {
      title: t('enable'),
      dataIndex: 'enable',
      scopedSlots: { customRender: 'enable' },
      align: 'center',
      className: 'venue8',
      customCell(record, rowIndex) {
        return customCell(record, rowIndex, 8);
      },
    },
  ];
};

export default createTableColumns;
