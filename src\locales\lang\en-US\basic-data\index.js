export default {
  title: {
    title: 'Basic Data of Venue',
    add: 'Addition',
    addTitle: 'Add Venue',
    editTitle: 'Edit Venue',
    category: '类别管理',
    addCategory: '新增类别',
    editCategory: '编辑类别',
  },
  columns: {
    name: 'Venue name',
    number: 'Venue number',
    category: 'Category of venue',
    location: 'Location of venue',
    peopleNumber: 'Number of people that can be accommodated',
    administrator: 'Venue manager',
    time: 'Last operating person/operating time',
    status: 'Status',
    operate: 'Operate',
    categoryName: '类别名称',
    venueVos: '关联场馆',
    updateTime: '操作人/最后操作时间',
  },
  addVenue: {
    chName: 'Chinese name of venue',
    enName: 'English name of venue',
    number: 'Venue number',
    category: 'Category of venue',
    attribute: 'Characteristics of venue',
    administrator: 'Venue manager',
    peopleNumber: 'Number of people that can be accommodated',
    location: 'Location of venue',
    lockid: 'Access control ID',
    remarks: 'Remarks',
    isEnable: 'Do you wish to enable',
    remarkPlaceHolder: 'Please limit remarks to within 200 words',
    selectPlaceHolder: 'Please select',
  },
  form: {
    keywordPlace: 'Venue name',
    chName: 'Please fill in the Chinese name of venue',
    enName: 'Please fill in the English name of venue',
    number: 'Please fill in the venue number',
    category: 'Please fill in the category of venue',
    attribute: 'Please fill in the characteristics of venue',
    administrator: 'Please fill in the venue manager',
    peopleNumber: 'Please fill in the number of people that can be accommodated',
    location: 'Please fill in the location of venue',
    remarks: 'Please fill in the remarks',
    isEnable: 'Please select whether you wish to enable',
    isNumber: 'Please enter a valid integer',
    cancelType: 'Please select reasons for cancellation',
    categoryName: '类别中文名称',
    categoryNameEn: '类别英文名称',
    categoryIcon: '类别图标',
    venueVos: '关联场馆',
    status: '状态',
  },
  place: {
    categoryName: '请填写类别中文名称',
    categoryNameEn: '请填写类别英文名称',
    categoryIcon: '请上传类别图标',
    categoryIconExtra: '仅能上传一张png、jpg格式的图片，建议上传图片大小100px*100px',
  },
  action: {
    view: 'Check',
    cancel: 'Cancel',
    edit: 'Edit',
    addCategory: '新增类别',
  },
  msg: {
    success: 'Operation is successful',
    submitSuccess: 'Successfully submitted',
    cancelSuccess: 'Successfully cancelled',
    confirmCancel: 'Do you confirm you wish to cancel application for repair?',
    assignSucc: 'Successfully assigned',
    handleSucc: 'Successfully completed',
    saveSucc: 'Successfully saved',
    imgType: 'Only {types} types of images can be uploaded',
  },
  venueCategory: {
    CLASS_ROOM: 'Seminar Room',
    DANCE_ROOM: 'Dance Room',
    EXERCISE_ROOM: 'Gym',
  },
  status: {
    TRUE: 'Enable',
    FALSE: 'Disable',
  },
  venueAttribute: {
    INDOOR: 'Indoor',
    OUTDOOR: 'Outdoor',
  },
  isTrueStatus: {
    TRUE: 'Yes',
    FALSE: 'No',
  },
};
