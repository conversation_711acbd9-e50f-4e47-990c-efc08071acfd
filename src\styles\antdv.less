.ant-layout{
  background-color: #fff;
}

.ant-page-header{
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  height: @pageHeaderHeight;
  line-height: @pageHeaderHeight;
  border-bottom: 1px solid @defaultBorderColor;
}
.ant-page-header-heading .ant-page-header-heading-tags{
  line-height: @pageHeaderHeight;
}
.ant-page-header-heading .ant-page-header-heading-tags .iconfont{
  color: #171717;
  font-size: 14px;
  cursor: pointer;
}
.ant-page-header-heading-title{
  line-height: @pageHeaderHeight;
  font-weight: bold;
  font-size: @commonTitleFontSize;
}

.ant-page-header-heading-extra{
  color: #D0D0D0;
  font-size: 12px;
  .btn_upload_link{
    vertical-align: middle;
    .ant-upload-list{
      display: inline-block;
    }
  }
}
.ant-page-header-heading-extra > .ant-btn{
  font-size: @commonTitleFontSize;
  vertical-align: middle;
}
.ant-page-header-heading-extra > .ant-btn-link{
  margin-left: 0;
}
.tree-panel .ant-page-header-heading-extra{
  font-size: @commonTitleFontSize;
}


.content-panel .ant-card-body{
  padding: 0;
}
.ant-card-head{
  padding: 0;
  font-size: @commonTitleFontSize;
  font-weight: bold;
}
.ant-card-head-title{
  padding: 0;
  height: 48px;
  line-height: 48px;
}

.ant-input-affix-wrapper .ant-input:not(:first-child){
  padding-left: 35px;
}
.ant-input-prefix .iconfont{
  font-size: @commonTitleFontSize;
  color: #171717;
}

.ant-list-items{
  // padding: 0 0 12px;
  border-bottom: 1px solid @defaultBorderColor;
}
.ant-list-split .ant-list-item{
  border-bottom: none;
}

.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header,
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header > .arrow{
  cursor: default;
}

.ant-collapse{
  background-color: #FCFCFC;
  border-color: @defaultBorderColor;
  border-radius: 0;
  > .ant-collapse-item{
    border-color: @defaultBorderColor;
    > .ant-collapse-header{
      padding-left: 8px;
      background-color: #fff;
      .ico{
        padding: 0;
        transform: rotate(-90deg);
        vertical-align: 0;
        .iconfont{
          font-size: 12px;
          color: #171717;
          transform: scale(0.4);
        }
      }
    }
    &.ant-collapse-item-active{
      .ico{
        transform: rotate(0deg);
      }
    }
    .ant-collapse-content{
      background: none;
      border-color: @defaultBorderColor;
    }
  }
}

.ant-row::before, .ant-row::after{
  clear: both;
}
.ant-form-item-label{
  line-height: @formLabelLineHeight;
}
.ant-form-item-control{
  line-height: @formLabelLineHeight;
}
.ant-form-horizontal .ant-form-item{
  margin-bottom: 12px;
}
.ant-form-horizontal .ant-form-explain{
  // position: absolute;
  // left: 100%;
  // top: 12px;
  // margin-left: 10px;
  // white-space: nowrap;
  padding-top: 5px;
  font-size: 12px;
  color: @dangerColor;
}
.ant-form-horizontal .ant-form-item-label{
  margin-right: 15px;
  &.ant-col{
    margin-right: 0;
  }
}

// antdv edit
.ant-btn{
  padding: 0 30px;
  height: @btnHeight;
  line-height: @btnHeight;
  font-size: @btnFontSize;
  vertical-align: middle;
  border-radius: 3px;
  min-width: @btnWidth;
  &.ant-btn-sm {
    height: @smallBtnHeight;
    line-height: @smallBtnHeight;
    padding: 0 12px;
    font-size: @btnFontSize;
    min-width: @smallBtnWidth;
  }
  &.ant-btn-lg {
    height: @bigBtnHeight;
    line-height: @bigBtnHeight;
    padding: 0 57px;
    min-width: @bigBtnWidth;
  }
}
.ant-btn:active, .ant-btn:hover, .ant-btn:focus{
  border-color: @btnPrimaryBg;
  color: @btnPrimaryBg;
}

.ant-btn-sm{
  padding: 0 15px;
  height: 26px;
  line-height: 26px;
  font-size: 12px;
}
.ant-btn-link{
  padding: 0 10px;
  color: @btnPrimaryBg;
}
// .ant-btn-link:hover, .ant-btn-link:focus{
//   color: hsv(@link-hsvhue, @link-hsvsaturation, (@link-hsvvalue + @link-hover));
// }
// .ant-btn-link:active{
//   color: hsv(@link-hsvhue, @link-hsvsaturation, (@link-hsvvalue + @link-active));
// }

.ant-btn-primary{
  position: relative;
  background-color: @btnPrimaryBg;
  border-color: @btnPrimaryBg;
}
.ant-btn-primary:hover, .ant-btn-primary:focus{
  background-color: @btnPrimaryBg;
  border-color: @btnPrimaryBg;
}

.ant-btn::before{
  opacity: 0.2;
}
.ant-btn.ant-btn-primary:hover::before/*, .ant-btn-primary:focus::before*/{
  display: block;
  background: @btnBgOverlayHover;
}
.ant-btn.ant-btn-primary:active::before{
  display: block;
  background: @btnBgOverlayActive;
}
.ant-btn.ant-btn-loading::before{
  display: block;
  opacity: 0.5;
}

.ant-btn-primary-disabled, .ant-btn-primary.disabled, .ant-btn-primary[disabled], .ant-btn-primary-disabled:hover, .ant-btn-primary.disabled:hover, .ant-btn-primary[disabled]:hover, .ant-btn-primary-disabled:focus, .ant-btn-primary.disabled:focus, .ant-btn-primary[disabled]:focus, .ant-btn-primary-disabled:active, .ant-btn-primary.disabled:active, .ant-btn-primary[disabled]:active, .ant-btn-primary-disabled.active, .ant-btn-primary.disabled.active, .ant-btn-primary[disabled].active{
  background-color: @btnPrimaryBg;
  border-color: @btnPrimaryBg;
  color: @btnPrimaryCorlor;
}
.ant-btn-primary-disabled::before, .ant-btn-primary.disabled::before, .ant-btn-primary[disabled]::before{
  display: block;
  opacity: 0.5;
}

.ant-btn-primary:hover, .ant-btn-primary:focus, .ant-btn-primary:active{
  background-color: @btnPrimaryBg;
  border-color: @btnPrimaryBg;
  color: #fff;
}
.ant-btn-link:active, .ant-btn-link:hover, .ant-btn-link:focus{
  color: @btnPrimaryBg;
  border-color: transparent;
}
.ant-btn-default {
  border-color: @btnPrimaryBg;
  color: @btnPrimaryBg;
}

.ant-btn-background-ghost.ant-btn-primary{
  border-color: @btnPrimaryBg;
  color: @btnPrimaryBg;
}
.ant-btn-background-ghost.ant-btn-primary:hover, .ant-btn-background-ghost.ant-btn-primary:focus{
  border-color: @btnPrimaryBg;
  color: @btnPrimaryBg;
}

.ant-btn-icon-only{
  padding: 0;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
}
.ant-btn-icon-only > i {
  vertical-align: -0.125em;
}
.ant-btn-circle, .ant-btn-circle-outline{
  border-radius: 50%;
}

.ant-btn .anticon-loading{
  width: 1em;
  height: 1em;
  border: 1px solid #fff;
  border-right-color: transparent;
  border-radius: 50%;
  position: relative;
  animation: loader-rotate 1s linear infinite;
}
.ant-btn .anticon-loading .anticon-spin{
  display: none;
}
@keyframes loader-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}


.ant-tag{
  padding: 0 5px;
  line-height: 18px;
  border-radius: 2px;
}


.ant-input{
  border-color: @inputBorderColor;
  font-size: 14px;
  vertical-align: middle;
}
.ant-input:hover{
  border-color: @inputBorderColorHover;
}
.ant-input:focus{
  box-shadow: none;
  border-color: @inputBorderColorHover;
}
.has-error .ant-input, .has-error .ant-input:hover{

}
.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled){
  border-color: @inputBorderColorHover;
}

.ant-input, .ant-select-selection{
  border-radius: 0;
}


.ant-select{
  vertical-align: middle;
  border-color: @inputBorderColor;
  font-size: 14px;
  .anticon{
    // line-height: 1;
    font-family: "iconfont" !important;
    font-style: normal;
    font-size: 12px;
    transform: scale(0.5);
    color: #999;
    transition: transform 0.3s;
    &::after {
      // content: "\e64e";
    }
    &.anticon-down {
      transform: scale(0.4);
    }
    &.anticon-down::after {
      content: "\e64e";
    }
    &.anticon-close {
      transform: scale(0.6);
    }
    &.anticon-close::after {
      content: "\e63f";
    }
    &.anticon-close-circle::after {
      content: "\e63f";
    }
    svg{
      display: none;
    }
  }
  .ant-select-selection--single{
    .anticon{
    }
  }
  .ant-select-selection__choice__remove{
    // right: -2px;
    .anticon{
    }
  }
  .ant-select-selection__clear{
    margin-top: -4px;
    background-color: #D6D6D6;
    border-radius: 50%;
    .anticon-close-circle {
      color: #fff;
      vertical-align: 0;
    }
  }
}
.ant-select-open .ant-select-arrow-icon.anticon{
  transform: scale(0.4) rotate(180deg);
}
.ant-select-arrow{
  right: 2px;
}
.ant-select-selection{
  border-color: @inputBorderColor;
}
.ant-select-selection:hover{
  border-color: @inputBorderColorHover;
}
.ant-select-selection--single .ant-select-selection__rendered {
  overflow: hidden;
}
.ant-select-open .ant-select-selection{
  border-color: @inputBorderColorHover;
  box-shadow: none;
}
.ant-select-tree li .ant-select-tree-node-content-wrapper{
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.ant-select-tree-dropdown{
  max-width: 600px;
}
.ant-select-auto-complete.ant-select .ant-input:focus, .ant-select-auto-complete.ant-select .ant-input:hover{
  border-color: @inputBorderColorHover;
}
.ant-select-focused .ant-select-selection, .ant-select-selection:focus, .ant-select-selection:active {
  border-color: #8D0405;
  box-shadow: none;
}

.ant-radio-inner{
  border-radius: 0;
  border-color: #E2E2E2;
}
.ant-radio-checked .ant-radio-inner{
  border-color: @mainColor;
  background-color: #e6effa;
}
.ant-radio-inner::after{
  top: 4px;
  left: 4px;
  width: 6px;
  height: 6px;
  border-radius: 0;
  background-color: @mainColor;
}
.ant-radio-wrapper:hover .ant-radio, .ant-radio:hover .ant-radio-inner, .ant-radio-input:focus + .ant-radio-inner{
  border-color: @mainColor;
}
.ant-radio-checked .ant-radio-inner {
  background-color: #faefe6;
}
.ant-radio-input:focus + .ant-radio-inner {
  box-shadow: 0 0 0 3px rgba(255, 144, 24, 0.08);
}

.ant-checkbox-inner::after {
  left: 30%;
}
.ant-checkbox-checked::after {
  border-color: @mainColor;
}

.ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: @mainColor;
  &::after {
    background-color: #fff;
    height: 1px;
  }
}

.ant-checkbox-indeterminate.ant-checkbox-disabled .ant-checkbox-inner::after {
  background-color: @mainColor;
  border-color: @mainColor;
}
.ant-checkbox-wrapper{
  color: @titleColor;
  line-height: 1.5;
  .ant-checkbox{
    + span{
      padding-left: 4px;
    }
    .ant-checkbox-inner{
      border-color: #E2E2E2;
      border-radius: 0;
      width: 22px;
      height: 22px;
    }
  }
}
.ant-checkbox-checked {
  .ant-checkbox-inner{
    background-color: @mainColor;
  }
  &.ant-checkbox-disabled .ant-checkbox-inner{
    background-color: #f5f5f5;
  }
}

.ant-select-dropdown-menu-item-selected {
  background-color: #fff;
}
.ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled){
  background-color: #fff;
}
.ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled){
  background-color: @assistColor;
  color: @mainColor;
}
.ant-select-dropdown-menu-item-selected{
  color: @mainColor;
}

.ant-message{
  top: 140px;
  // .anticon{
  //   display: none;
  // }
  // .ant-message-notice-content{
  //   overflow: hidden;
  //   padding: 0;
  //   .ant-message-custom-content{
  //     padding: 12px 76px;
  //   }
  //   .ant-message-error{
  //     background-color: #D63C3C;
  //     color: #fff;
  //   }
  //   .ant-message-success{
  //     background-color: #0AAF60;
  //     color: #fff;
  //   }
  // }
}


.ant-table{
  color: @titleColor;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr > td{
  padding: 13px 16px;
}
.ant-table-thead > tr > th{
  border-bottom: 2px solid @defaultBorderColor;
  background: none;
  font-weight: bold;
}
.ant-table-tbody > tr > td{
  border-bottom: 1px solid @defaultBorderColor;
}
.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td, .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td, .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td, .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #f5f5f5;
}
.ant-table-row-expand-icon:focus
, .ant-table-row-expand-icon:hover {
  color: @mainColor;
}
.ant-table-header {
  background: transparent;
}
tr.ant-table-expanded-row {
  td > .ant-table-wrapper.pima-sub-table {
    margin: -14px -16px -14px;
  }
}


.ant-pagination{
  &.ant-table-pagination{
    margin-right: 24px;
  }

  .ant-pagination-item-1{
    overflow: hidden;
    border-radius: 2px 0 0 2px;
  }
  li:nth-last-child(3){
    overflow: hidden;
    border-radius: 0 2px 2px 0;
  }
  .ant-select-selection--single{
    height: 40px;
  }
  .ant-select-selection__rendered{
    line-height: 38px;
  }
  .ant-pagination-options-quick-jumper input{
    height: 40px;
    border-radius: 2px;
    border-color: @inputBorderColor;
    &:focus{
      border-color: @inputBorderColorHover;
      box-shadow: none;
    }
    &:hover{
      border-color: @inputBorderColorHover;
    }
  }
}

.ant-pagination-total-text{
  margin-right: 24px;
}
.ant-pagination-item{
  margin-right: -1px;
  min-width: 40px;
  height: 40px;
  line-height: 38px;
  border-color: @inputBorderColor;
  border-radius: 0;

  &:focus, &:hover{
    border-color: @inputBorderColor;
  }
  &:focus a, &:hover a{
    background-color: @btnPrimaryBg;
    color: #fff;
  }
}
.ant-pagination-item-active{
  a{
    background-color: @btnPrimaryBg;
    color: #fff;
  }
  &:focus a, &:hover a{
    background-color: @btnPrimaryBg;
    color: #fff;
  }
}

.ant-pagination-prev, .ant-pagination-next, .ant-pagination-jump-prev, .ant-pagination-jump-next{
  min-width: 40px;
  height: 40px;
  line-height: 38px;
  border-radius: 0;
}
.ant-pagination-prev{
  margin-right: -1px;
}
.ant-pagination-next, .ant-pagination-jump-prev, .ant-pagination-jump-next{
  margin-right: 0;
}
.ant-pagination-jump-prev, .ant-pagination-jump-next{
  margin-right: -1px;
  border: 1px solid @inputBorderColor;
  background-color: #fff;

  a{
    display: block;
  }

  &:focus a{
    .ant-pagination-item-ellipsis{
      opacity: 1;
    }
  }
  &:hover a{
    background-color: @btnPrimaryBg;
    color: #fff;
  }
  .ant-pagination-item-container{
    .ant-pagination-item-link-icon, .ant-pagination-item-link-icon{
      color: #fff;
    }
  }
}
.ant-pagination-prev, .ant-pagination-next{
  &:focus .ant-pagination-item-link, &:hover .ant-pagination-item-link{
    background-color: @btnPrimaryBg;
    color: #fff;
    border-color: @inputBorderColor;
  }
}

.ant-pagination-disabled{
  a, &:hover a, :focus a, .ant-pagination-item-link, &:hover .ant-pagination-item-link, &:focus .ant-pagination-item-link{
    color: rgba(0, 0, 0, 0.25);
    background-color: #fff;
    border-color: #d9d9d9;
  }
}

.ant-pagination-prev .ant-pagination-item-link, .ant-pagination-next .ant-pagination-item-link{
  padding: 0 15px;
  border-radius: 2px;
  border-color: @inputBorderColor;
}

.ant-popover.dark-theme {
  .ant-popover-inner {
    background-color: #E8EFFB;
  }

  .ant-popover-content .ant-popover-inner-content {
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-popover-arrow {
    background: #E8EFFB;
  }

  .ant-popover-placement-right > .ant-popover-content > .ant-popover-arrow,
  .ant-popover-placement-rightTop > .ant-popover-content > .ant-popover-arrow,
  .ant-popover-placement-rightBottom > .ant-popover-content > .ant-popover-arrow{
    border-bottom-color: #E8EFFB;
    border-left-color: #E8EFFB;
  }
}

.ant-switch {
  background: #BBBBBB;
  border: 1px solid #BBBBBB;
  height: 26px;
  width: 48px;
}
.ant-switch::after {
  width: 22px;
  height: 22px;
  top: 1px;
  left: 2px;
  background-color: #fff;
}

.ant-switch-checked {
  background-color: @mainColor;
  border: 1px solid @mainColor;
}

.ant-switch-checked::after {
  width: 22px;
  height: 22px;
  top: 1px;
  left: 46px;
  background-color: #fff;
}

// 日历
.ant-calendar-today .ant-calendar-date {
  color: @mainColor;
  font-weight: bold;
  border-color: @mainColor;
}
.ant-calendar-selected-day .ant-calendar-date {
  background: @assistColor;
}
.ant-calendar-date:hover {
  background: @assistColor;
  cursor: pointer;
}
.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: @inputBorderColorHover;
}
.ant-calendar .ant-calendar-ok-btn {
  background-color: @mainColor;
  border-color: @mainColor;
  color: #fff;
}
.ant-calendar .ant-calendar-ok-btn:hover, .ant-calendar .ant-calendar-ok-btn:focus {
  color: #fff;
  background-color: @mainColor;
  border-color: @mainColor;
}
.ant-calendar-time-picker-select li:hover {
  background: @assistColor;
}
.ant-calendar-time-picker-select li:focus {
  color: @mainColor;
}
.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date,
.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
  background: @mainColor;
}
.ant-calendar-range .ant-calendar-in-range-cell::before {
  background-color: @assistColor;
}
.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover
, .ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover {
  background: @mainColor;
}
.ant-calendar-picker:focus .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: @mainColor;
  box-shadow: none;
}
.ant-fullcalendar-today .ant-fullcalendar-value
, .ant-fullcalendar-month-panel-current-cell .ant-fullcalendar-value {
  box-shadow: 0 0 0 1px @assistColor inset;
}
.ant-fullcalendar-value:hover {
  background: @assistColor;
}
.ant-fullcalendar-selected-day .ant-fullcalendar-value
, .ant-fullcalendar-month-panel-selected-cell .ant-fullcalendar-value {
  background: @mainColor;
}

.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)
,.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  background: @mainColor;
  border-color: @mainColor;
}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)
, .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  box-shadow: -1px 0 0 0 @assistColor;
}
.ant-radio-button-wrapper:hover {
  color: @mainColor;
}

.ant-time-picker-input:hover {
  border-color: @mainColor;
}
.ant-time-picker-panel-select li:hover {
  background: @assistColor;
}
.ant-time-picker-panel-select li:focus {
  color: @mainColor;
}
.ant-calendar-header a:hover {
  color: @mainColor;
}
.ant-calendar-year-panel-header a:hover {
  color: @mainColor;
}
.ant-calendar-year-panel-year:hover {
  background: @assistColor;
}
.ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year {
  background: @mainColor;
}
.ant-calendar-month-panel-month:hover {
  background: @assistColor;
}
.ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month {
  background: @mainColor;
}
.ant-calendar-decade-panel-selected-cell .ant-calendar-decade-panel-decade
, .ant-calendar-decade-panel-selected-cell .ant-calendar-decade-panel-decade:hover {
  background: @mainColor;
}
.ant-calendar-decade-panel-decade:hover {
  background: @assistColor;
}

.ant-input-number {
  border-radius: 0;
}
.ant-input-number-input {
  border-radius: 0;
}
.ant-input-number:hover {
  border-color: @mainColor;
}
.ant-input-number:focus {
  border-color: @mainColor;
  box-shadow: none;
}
.ant-input-number-handler:hover .ant-input-number-handler-up-inner
, .ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: @mainColor;
}
.ant-input-number-input-wrap:focus {
  outline-color: @mainColor;
}
.ant-input-number-focused {
  border-color: @mainColor;
  box-shadow: none;
}
.ant-input-number-disabled:hover {
  border-color: #d9d9d9;
}

.ant-spin-dot-item {
  background-color: @mainColor;
}

// tabs标签
.ant-tabs-ink-bar {
  background-color: @mainColor;
}
.ant-tabs-nav .ant-tabs-tab-active
,.ant-tabs-nav .ant-tabs-tab:hover {
  color: @mainColor;
}



// 步骤条
.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: @mainColor;
}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after
,.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
  background-color: #EDEDED;
}
.ant-steps-dot .ant-steps-item-icon {
  width: 11px;
  height: 11px;
}
.ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot {
  border-radius: 50%;
}
.ant-steps-dot .ant-steps-item-tail::after, .ant-steps-dot.ant-steps-small .ant-steps-item-tail::after {
  margin-left: 14px;
}
.ant-steps-dot .ant-steps-item-process .ant-steps-item-icon {
  width: 7px;
  height: 7px;
}
.ant-steps-vertical.ant-steps-dot .ant-steps-item-process .ant-steps-icon-dot {
  left: 2px;
}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: @mainColor;
  box-shadow: 0px 0px 9px 1px @mainColor
}

.ant-upload.ant-upload-select-picture-card:hover {
  border-color: @mainColor;
}

.ant-upload-list-item:hover .ant-upload-list-item-info {
  background-color: @assistColor;
}

.ant-form-horizontal {
  .ant-form-item {
    display: flex;
    align-items: center;
    .ant-form-item-label {
      line-height: 1.5;
      margin-right: 8px;
    }
    label {
      white-space: normal;
    }
  }
}

.ant-cascader-picker{
  &:hover{
    border-color: @mainColor;
  }

  &:focus{
    // border-color: @mainColor;
    // box-shadow: none;

    .ant-cascader-input{
      border-color: @mainColor;
      box-shadow: none;
    }
  }
}

.ant-cascader-menus .ant-cascader-menu{
  .ant-cascader-menu-item{
    background-color: #fff;
  
    &:hover{
      background-color: @assistColor;
      color: @mainColor;
    }

    &.ant-cascader-menu-item-active,
    &.ant-cascader-menu-item-selected{
      background-color: #fff;
      color:@mainColor;
    }
  }


}