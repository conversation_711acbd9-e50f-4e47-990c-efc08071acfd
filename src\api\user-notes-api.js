import httpRequest from './request';
import requestToken from './requestToken';
import config from '../config';


// eslint-disable-next-line prefer-destructuring
const Base64 = require('js-base64').Base64;

export function getLoginUserInfo() {
  return new Promise((resolve, reject) => {
    httpRequest({
      baseURL: config.archApiBaseUrl,
      url: 'users/profile',
      methods: 'get',
    })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getUserData(params = {}) {
  // eslint-disable-next-line no-param-reassign
  params.code = process.env.SERVICE_CODE;
  return new Promise((resolve, reject) => {
    requestToken({
      baseURL: config.bdcCoreApiBaseUrl,
      url: 'users/auths',
      method: 'get',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 获取编辑器上传图片的token
export function getTokens(username) {
  return new Promise((resolve, reject) => {
    const accessToken = Base64.toBase64(`${process.env.CLIENT_ID}:${process.env.CLIENT_SECRET}`);
    requestToken({
      baseURL: config.bdcCoreApiBaseUrl,
      url: 'oauth/username/token',
      method: 'post',
      params: username,
      headers: { Authorization: `Basic ${accessToken}` },
    })
      .then(({ data = {} }) => {
        resolve(data.model);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

// 查询场馆列表
export function getNotesListApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/use-notes',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 查询场馆列表动态选择器选项
export function searchNotesListApi(name) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/use-notes',
      method: 'get',
      params: {
        name,
        isAll: true,
      },
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 新增基础数据场馆
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function addNotesApi(data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/use-notes',
      method: 'post',
      data: {
        ...data,
        type: 'use_note',
      },
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}


// 删除
export function delNotesApi({ id }) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/use-notes/${id}/remove`,
      method: 'post',
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

/**
 * 获取规则详情
 * @param userId: number
 * @return {Promise<unknown>}
 */
export function getNotesDetailApi(id) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/use-notes/${id}`,
      method: 'get',
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 编辑规则
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function updateNotesApi(data) {
  const { id = '' } = data;
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/use-notes/${id}`,
      method: 'post',
      data: {
        ...data,
        type: 'use_note',
      },
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 上传图片
export function uploadAttachment({ relateType, fileData }) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('relateType', relateType);
    formData.append('fileData', fileData);
    httpRequest
      .post('/attachments/upload', formData, {
        baseURL: config.pimaUploadBaseUrl,
        headers: {
          Accept: ' */*',
          'Content-Type': 'multipart/form-data',
        },
      })
      .then((resp) => {
        resolve(resp.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 删除附件
export function removeAttachment(id) {
  return new Promise((resolve, reject) => {
    httpRequest({
      baseURL: config.pimaUploadBaseUrl,
      url: `attachments/${id}/remove`,
      method: 'POST',
    })
      .then((resp) => {
        resolve(resp.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}


// 查询公告
export function getNoticeApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/use-notes/notice',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 新增或修改公告
export function updateNoticeApi(data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/use-notes/notice',
      method: 'post',
      data: {
        ...data,
        type: 'notice',
      },
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
