/**
 * Select Personal Component
 */

import { searchUserApi } from '../../../api/unidata-api';
import Select from 'ant-design-vue/lib/select';
import Radio, { Group } from "ant-design-vue/lib/radio";
import './index.less';

// 个人选项类型
const PERSONAL_TYPES = {
  ACCOUNT: 'ACCOUNT',
  STU: 'STU',
};

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  return function () {
    const ctx = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, args);
    }, wait);
  };
}

/**
 * 获取选中的节点
 * @param personalData: Array<{key, value, title}>
 * @param selectedKeys: Array<string|number>
 * @param key: String
 * @return {*[]}
 */
const getSelectedPersonals = (personalData, selectedKeys, keyword = 'key') => {
  let results = [];
  if (Array.isArray(selectedKeys)) {
    // eslint-disable-next-line no-restricted-syntax
    for (const key of selectedKeys) {
      results = results.concat(...personalData.filter((Item) => Item[keyword] === key));
    }
    return results;
  }
  return results;
};

const PimaSelectPersonal = {
  name: 'SelectPersonal',

  props: {
    checked: {
      type: Array,
      default: () => [],
    },
    personalData: {
      type: Array,
      default: () => [],
    },
    defaultPersonalKeys: {
      type: Array,
      default: () => [],
    },
    visibleImport: {
      type: Boolean,
      default: false,
    },
    importUrl: {
      type: String,
      default: '',
    },
    templateUrl: {
      type: String,
      default: '',
    },
    templateFileName: {
      type: String,
      default: '',
    },
    allowedTypes: {
      type: Array,
      default: () => ['xls', 'xlsx'],
    },
    initialNotices: {
      type: Array,
      default: () => [
        '下载导入模板，填写用户信息，批量新建用户',
        '上传填好的文件，仅支持xls、xlsx格式文件',
      ],
    },
    config: {
      type: Object,
      default: () => {},
    },
  },

  components: {
    [Select.name]: Select,
    [Select.Option.name]: Select.Option,
    [Group.name]: Radio.Group,
    [Radio.name]: Radio,
  },

  data() {
    return {
      showPersonalType: PERSONAL_TYPES.ACCOUNT,
      persons: [],
      taskIds: [], // 上传的名单id s => {id, fileName}
      selectedKeys: [],
      selectedUsers: [],
      fetching: false,
    };
  },

  mounted() {
    this.$emit('update:checked', {
      users: [...getSelectedPersonals(this.personalData, this.defaultPersonalKeys)],
      taskIds: this.taskIds.map((item) => item.id),
    });
  },

  methods: {
    fetchUsers(value) {
      if (!value || !value.length) return;
      this.fetching = true;
      searchUserApi(value)
        .then(({ model }) => {
          this.persons = model;
          this.fetching = false;
        })
        .catch((err) => {
          this.fetching = false;
          this.$message.error(err.errorList.message);
          // this.$error({
          //   title: err.errorList.message,
          //   class: 'pima-confrim pima-confrim-error',
          // });
        })
    },
    changeDropdownVisible(open) {
      if (!open) {
        this.persons = [];
      }
    },
    /**
     * select 改变选择
     * @param keys: {Array} 已经选中的人员的key
     * @param keyword: {string} select key
     */
    changeSelect(keys, keyword) {
      // this.persons = [];
      this.selectedKeys = keys;
      this.selectedUsers = [...getSelectedPersonals(this.personalData, this.selectedKeys, keyword)];
      this.emitSyncPersons();
    },
    /**
     * 删除某个已选中的用户
     * @param user: {object}
     */
    removeOneUser(user) {
      this.selectedUsers = this.selectedUsers.filter((item) => item !== user);
      this.emitSyncPersons();
    },
    /**
     * 清空选中的pesonal select
     */
    clearUsers() {
      this.selectedUsers = [];
      this.emitSyncPersons();
    },
    handleImportSuccess(status, res, file) {
      const { model } = res;
      if (model) {
        this.taskIds.push({
          id: model,
          fileName: file.name,
        });
      }
      this.emitSyncPersons();
      this.showImport = false;
    },
    handleImportError(status, res) {
      this.$message.error(res.errorList.message);
      // this.$error({
      //   title: '500',
      //   content: res.errorList.message,
      //   class: 'pima-confrim pima-confrim-error',
      // });
    },
    handleImportWarn(msg) {
      this.$confirm({
        title: msg,
        class: 'pima-confrim',
        onOk() {},
      });
    },
    /**
     * 改变person选择类型 Radio
     * @param e
     */
    changePersonalType(e) {
      this.selectedKeys = [];
      this.showPersonalType = e.target.value;
    },
    /**
     * 删除上传的名单
     * @param id: {number} 名单ID
     */
    removeTaskId(id) {
      this.taskIds.splice(this.taskIds.map((item) => item.id).indexOf(id), 1);
      this.emitSyncPersons();
    },
    /**
     * 过滤搜索 select自定义
     * @param input: {string} 用户输入内容
     * @param option: {Object} 节点数据
     * @param keywords: {string} 筛选目标
     * @return {boolean}
     */
    filterOptionsFn(input, option, keywords) {
      if (!input || !input.length) return true;
      const field = option.data[keywords];
      return field.toLowerCase().indexOf(input.toLowerCase()) > -1;
    },
    /**
     * 同步选中的Personal数据
     */
    emitSyncPersons() {
      this.$emit('update:checked', {
        users: [...this.selectedUsers],
        taskIds: this.taskIds.map((item) => item.id),
      });
    }
  },

  render() {
    return (
			<div>
        <div class="personal-top">
          {/*<div class="radio-wrap">*/}
          {/*  <a-radio-group*/}
          {/*    default-value={this.showPersonalType}*/}
          {/*    name="radioGroup"*/}
          {/*    onChange={e => this.changePersonalType(e)}*/}
          {/*  >*/}
          {/*    <a-radio value={PERSONAL_TYPES.ACCOUNT}>*/}
          {/*      账号*/}
          {/*    </a-radio>*/}
          {/*    /!*<a-radio value={PERSONAL_TYPES.STU}>*!/*/}
          {/*    /!*  工号*!/*/}
          {/*    /!*</a-radio>*!/*/}
          {/*  </a-radio-group>*/}
          {/*</div>*/}
          {
            this.showPersonalType === PERSONAL_TYPES.ACCOUNT ?
              (
                <div class="account-select">
                  <a-select
                    key={PERSONAL_TYPES.ACCOUNT}
                    value={this.selectedUsers.map((item) => item['id'])}
                    default-value={this.defaultPersonalKeys}
                    mode="multiple"
                    style="width: 100%"
                    placeholder="你可以输入工号、姓名、系统账号"
                    // filterOption={(input, option) => this.filterOptionsFn(input, option, 'name')}
                    filterOption={false}
                    not-found-content={this.fetching ? undefined : null}
                    onChange={(keys) => this.changeSelect(keys, 'id')}
                    onSearch={(value) => debounce(this.fetchUsers)(value)}
                    onDropdownVisibleChange={this.changeDropdownVisible}
                  >
                    {
                      this.fetching ? <a-spin slot="notFoundContent" size="small" /> : null
                    }
                    {this.persons.map((item) => {
                      return <a-select-option {...item} key={item.id}>
                        {item.name}
                      </a-select-option>;
                    })}
                  </a-select>
                </div>
              ) :
              (
                <div class="stu-select">
                  <a-select
                    key={PERSONAL_TYPES.STU}
                    default-value={this.defaultPersonalKeys}
                    mode="multiple"
                    style="width: 100%"
                    placeholder="搜索用户名..."
                    onChange={this.changeSelect}
                  >
                    {this.personalData.map((item) => {
                      return <a-select-option key={item.name}>
                        {item.name}
                      </a-select-option>;
                    })}
                  </a-select>
                </div>
              )
          }

          <div class="select-list-wrap">
            <div class="select-list">
              {
                this.selectedUsers.map((item, index) => {
                  return (
                    <span
                      key={item.key + index}
                      class={'list-item'}
                    >
                      {item.name}
                      <span
                        class={'delete-person-btn'}
                        onClick={() => this.removeOneUser(item)}
                      />
                    </span>
                  )
                })
              }
            </div>
            <div class="list-action">
              {
                this.selectedUsers.length ? <span onClick={this.clearUsers}>清空</span> : null
              }
            </div>
          </div>
        </div>
        <div>
          {
            this.taskIds.length ? (
              <div class="personal-files-wrap">
                <div class="files-header">已导入名单</div>
                {
                  this.taskIds.map((item, index) => {
                    return (
                      <div
                        key={item.filename + index + Math.random() * 7 + 3}
                        class="file-item"
                      >
                        <div class="icon iconfont .icon-wenjian"></div>
                        <span class="filename">{item.fileName}</span>
                        <span
                          class={'delete-btn'}
                          onClick={() => this.removeTaskId(item.id)}
                        >删除</span>
                      </div>
                    )
                  })
                }
              </div>
            ) : null
          }
        </div>

        <pima-ui-import
          visible={this.visibleImport}
          config={this.config}
          import-url={this.importUrl}
          template-url={this.templateUrl}
          template-file-name={this.templateFileName}
          base-url={this.config.importApiBaseUrl}
          allowed-types={this.allowedTypes}
          initial-notices={this.initialNotices}
          {...{on: {
            'update:visible': val => this.$emit('update:visibleImport', val),
            'success': (...val) => this.handleImportSuccess(...val),
            'error': (...val) => this.handleImportError(...val),
            'warn': (...val) => this.handleImportWarn(...val),
          }}}
        />
      </div>
    );
  },
};

export default PimaSelectPersonal;
