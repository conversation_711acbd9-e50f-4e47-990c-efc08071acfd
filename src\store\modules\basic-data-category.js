import {
  getCategoryListApi,
  getCategoryDetailApi,
  addCategoryApi,
  editCategoryApi,
  uploadAttachment,
} from '@api/basic-data-api';

const initState = {
  dataSource: [],
  loading: false,
  saving: false,
  page: 1,
  pageSize: 10,
  total: 0,
  categoryInfo: {},
};

const getters = {};

const mutations = {
  setDataSource(state, payload) {
    state.dataSource = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setSaving(state, payload) {
    state.saving = payload;
  },
  setPage(state, payload) {
    if (!payload) return;
    state.page = Number(payload);
  },
  setPageSize(state, payload) {
    if (!payload) return;
    state.pageSize = Number(payload);
  },
  setTotal(state, payload = 0) {
    state.total = payload;
  },
  setCategoryInfo(state, payload) {
    state.categoryInfo = payload;
  },
};

const actions = {
  fetchCategoryList({ commit }, payload) {
    commit('setLoading', true);

    return new Promise((resolve, reject) => {
      getCategoryListApi(payload)
        .then((res) => {
          commit('setDataSource', res.data);
          commit('setTotal', res.total);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
  fetchCategoryDetail({ commit }, payload) {
    commit('setLoading', true);

    return new Promise((resolve, reject) => {
      getCategoryDetailApi(payload)
        .then((res) => {
          commit('setCategoryInfo', res.data);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
  // 新建场馆类别
  createCategory({ commit }, payload) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      addCategoryApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setSaving', false);
        });
    });
  },
  // 修改场馆类别
  editCategory({ commit }, payload) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      editCategoryApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setSaving', false);
        });
    });
  },

  uploadCategoryIcon({ commit }, { fileData, relateType }) {
    return new Promise((resolve, reject) => {
      const data = {
        relateType,
        fileData,
      };
      uploadAttachment(data).then((resp) => {
        resolve(resp.data.model);
      }).catch((err) => {
        reject(err);
      }).finally(() => {
        commit('setSaving', false);
      });
    });
  },
};

export default {
  namespaced: true,
  state: initState,
  getters,
  mutations,
  actions,
};
