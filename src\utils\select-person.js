import _ from 'lodash';

export function generateTreeList(payload, hasUser) {
  if (!Array.isArray(payload)) {
    throw new Error('tree must be a Array');
  }
  const map = {};
  const list = [...payload];
  let result = [];
  function generateKey(tree) {
    function generateSubKey(inode) {
      const node = inode;
      // const parentKey = node.key;
      if (node.children && node.children.length > 0) {
        // eslint-disable-next-line no-unused-vars
        node.children = node.children.map((oitem, index) => {
          const item = oitem;
          // item.key = `${parentKey}-${index}`;
          item.key = oitem.id;
          return generateSubKey(item);
        });
      }
      return node;
    }
    // eslint-disable-next-line no-unused-vars
    return tree.map((oitem, index) => {
      const item = oitem;
      // item.key = `${index}`;
      item.key = oitem.id;
      return generateSubKey(item);
    });
  }
  list.forEach((item) => {
    const { id } = item;
    map[id] = item;
    map[id].title = item.name;
    map[id].value = item.id;
    map[id].name = item.name;
    map[id].label = item.name;
    map[id].key = `${item.id}`;
    map[id].scopedSlots = { title: 'title' };
    map[id].slots = { title: 'title' };
    if (hasUser) {
      map[id].isLeaf = !item.isExistingUsers;
    } else {
      map[id].isLeaf = true;
    }
  });
  for (let index = 0; index < list.length; index += 1) {
    const item = list[index];
    if (item.parentId && map[item.parentId]) {
      const parent = map[item.parentId];
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(item);
      delete parent.isLeaf;
    } else {
      result.push(item);
    }
  }
  result = generateKey(result);
  return result;
}

function setChild(data) {
  // console.log(data);
  if (data.children === null) {
    // eslint-disable-next-line
    data.isLeaf = true;
  } else {
    data.children.forEach((citem) => {
      setChild(citem);
    });
  }
}

export function getTree(tree) {
  const data = JSON.parse(JSON.stringify(tree));
  data.forEach((item, k) => {
    if (item.children === null) {
      // eslint-disable-next-line
      data[k].isLeaf = true;
    } else {
      item.children.forEach((citem) => {
        setChild(citem);
      });
    }
  });
  return data;
}

export function filterTreeNode(pid, tree, targetId) {
  function dft(node, parent, id) {
    if (node.id === (id * 1)) {
      parent.children.splice(parent.children.indexOf(node), 1);
      return parent;
    }
    if (Array.isArray(node.children) && node.children.length > 0) {
      const { children } = node;
      // eslint-disable-next-line no-unreachable-loop
      for (let index = 0; index < children.length; index += 1) {
        const item = children[index];
        return dft(item, node, id);
      }
    }
    return node;
  }
  const treeList = _.cloneDeep(tree);
  if (!pid) {
    const res = treeList.filter((item) => item.id !== targetId * 1);
    return res;
  }
  return treeList.map((item) => {
    dft(item, null, targetId);
    return item;
  });
}
function getParentKeys(key, tree) {
  let parentKey;
  for (let i = 0; i < tree.length; i += 1) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKeys(key, node.children)) {
        parentKey = getParentKeys(key, node.children);
      }
    }
  }

  return parentKey;
}
// function getParentNode(key, tree) {
//   let parentNode;
//   for (let i = 0; i < tree.length; i += 1) {
//     const node = tree[i];
//     if (node.children) {
//       if (node.children.some(item => item.key === key)) {
//         parentNode = node;
//       } else if (getParentNode(key, node.children)) {
//         parentNode = getParentNode(key, node.children);
//       }
//     }
//   }
//   return parentNode;
// }
// function getParentNodeById(id, tree) {
//   let parentNode;
//   for (let i = 0; i < tree.length; i += 1) {
//     const node = tree[i];
//     if (node.children && node.children.length > 0) {
//       if (node.children.some(item => item.id === id) || node.id === id) {
//         parentNode = node;
//       } else if (getParentNodeById(id, node.children)) {
//         parentNode = getParentNodeById(id, node.children);
//       }
//     }
//   }
//   return parentNode;
// }

export function getTreeNodeById(id, tree) {
  let currNode;
  for (let i = 0; i < tree.length; i += 1) {
    const node = tree[i];
    if (node.id === id) {
      currNode = node;
      break;
    } else if (Array.isArray(node.children)) {
      const childNode = getTreeNodeById(id, node.children);
      if (childNode) {
        currNode = childNode;
        break;
      }
    }
  }
  return currNode;
}

export function getSerachKeys(dataList, val, treeData) {
  return dataList
    .map((item) => {
      if (item.title.indexOf(val) > -1) {
        return getParentKeys(item.key, treeData);
      }
      return null;
    })
    .filter((item, i, self) => item && self.indexOf(item) === i);
}
export function getGoupsNode(dataList, val) {
  return dataList
    .map((item) => {
      if (item.title.indexOf(val) > -1 && !item.isUser) {
        // return getParentNode(item.key, treeData);
        return item;
      }
      return null;
    })
    .filter((item, i, self) => item && self.indexOf(item) === i);
}
export function getGoupsNodeById(dataList, id) {
  return dataList
    .map((item) => {
      if (item.id === id && !item.isUser) {
        // return getParentNodeById(item.id, treeData);
        return item;
      }
      return null;
    }).filter((item, i, self) => item && self.indexOf(item) === i);
}
export function getLocalByKey(key) {
  const str = localStorage.getItem(key);
  return JSON.parse(str);
}

export function encodeToken(token) {
  return btoa(JSON.stringify(token));
}

export function decodeToken(encodedToken) {
  if (encodedToken) {
    try {
      return JSON.parse(atob(encodedToken));
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('decodeToken.error', err);
    }
  }
  return false;
}

export function getFileSize(fileByte) {
  const fileSizeByte = fileByte;
  let fileSizeMsg = '';
  if (fileSizeByte < 1048576) {
    fileSizeMsg = `${(fileSizeByte / 1024).toFixed(2)}KB`;
  } else if (fileSizeByte === 1048576) {
    fileSizeMsg = '1MB';
  } else if (fileSizeByte > 1048576 && fileSizeByte < 1073741824) {
    fileSizeMsg = `${(fileSizeByte / (1024 * 1024)).toFixed(2)}MB`;
  } else if (fileSizeByte > 1048576 && fileSizeByte === 1073741824) {
    fileSizeMsg = '1GB';
  } else if (fileSizeByte > 1073741824 && fileSizeByte < 1099511627776) {
    fileSizeMsg = `${(fileSizeByte / (1024 * 1024 * 1024)).toFixed(2)}GB`;
  } else {
    fileSizeMsg = '文件超过1TB';
  }
  return fileSizeMsg;
}

export function getSidebarMenuItemList(sidebarTree) {
  let items = [];
  if (Array.isArray(sidebarTree)) {
    sidebarTree.forEach((item) => {
      if (Array.isArray(item.children) && item.children.length > 0) {
        items = items.concat(getSidebarMenuItemList(item.children));
      } else {
        items.push(item);
      }
    });
  }
  return items;
}

export const validatorNumTypeRange = {
  // eslint-disable-next-line no-bitwise
  INT: -(1 << 31) - 1,
};
