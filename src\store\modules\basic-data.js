import { getVenuesListApi } from '@api/basic-data-api';

const initState = {
  dataSource: [],
  loading: false,
  page: 1,
  pageSize: 10,
  total: 0,
  filter: {
    // 是否查询全部
    isAll: false,
    // 场馆类别
    venueCategoryIds: '',
    // 关键字
    name: '',
  },
};

const getters = {};

const mutations = {
  setDataSource(state, payload) {
    state.dataSource = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setPage(state, payload) {
    if (!payload) return;
    state.page = Number(payload);
  },
  setPageSize(state, payload) {
    if (!payload) return;
    state.pageSize = Number(payload);
  },
  setTotal(state, payload = 0) {
    state.total = payload;
  },
  setFilter(state, payload) {
    Object.keys(payload).forEach((key) => {
      if (payload[key] !== undefined && Object.prototype.hasOwnProperty.call(state.filter, key)) {
        state.filter[key] = payload[key];
      }
    });
  },
};

const actions = {
  fetchVenuesListApi({ commit, state }, payload) {
    const { filter, page, pageSize } = payload;
    commit('setFilter', filter || {});
    commit('setPage', page);
    commit('setPageSize', pageSize);
    commit('setLoading', true);

    const params = {
      page: state.page,
      limit: state.pageSize,
    };

    Object.entries(state.filter).forEach(([k, v]) => {
      if (v !== '') params[k] = v;
    });

    return new Promise((resolve, reject) => {
      getVenuesListApi(params)
        .then((res) => {
          commit('setDataSource', res.data);
          commit('setTotal', res.total);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
};

export default {
  namespaced: true,
  state: initState,
  getters,
  mutations,
  actions,
};
