<template>
  <PopModal
    :title="t('title')"
    :width="600"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="drawer-bd">
      <center>{{ t('label', { name: user.name, type }) }}</center>
      <a-form
        :form="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :colon="false"
        @submit="onSubmit"
      >
        <a-form-item
          :label="t('reason')"
        >
          <a-textarea
            v-decorator="['reason', {
              initialValue: '',
              rules: [
                { required: true, message: t('reasonPlace') },
                { max: 200, message: $t('form.maxLength', { num: 200 }) },
              ]
            }]"
            :auto-size="{ minRows: 4, maxRows: 5 }"
          />
        </a-form-item>
      </a-form>
      <div class="clearfix drawer-ft">
        <a-button @click="onClose">
          {{ $t('action.close') }}
        </a-button>
        <a-button
          type="primary"
          :loading="submiting"
          @click="onSubmit"
        >
          {{ $t('action.ok') }}
        </a-button>
      </div>
    </div>
  </PopModal>
</template>


<script>
import { mapState } from 'vuex';
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';
import { OrderTypeMap } from '@/constants/venue';

export default {
  name: 'AddBlackListModel',
  components: {
    PopModal,
  },

  mixins: [
    nsI18n('t', 'reserveQuery.blacklist'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: null,
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      realValue: this.value,
    };
  },

  computed: {
    ...mapState({
      submiting: (state) => state.blacklist.saving,
    }),
    type() {
      const { reservationType } = this.user;
      if (OrderTypeMap.COMMON === reservationType) {
        return this.$t('reserveRule.orderType.common');
      }
      return this.$t('reserveRule.orderType.activity');
    },
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            ...values,
            userId: this.user.id,
            reservationType: this.user.reservationType,
          };
          this.$store.dispatch('blacklist/createBlackList', payload)
            .then((res) => {
              if (res.status === 200) {
                this.$message.success(this.$t('blacklist.msg.saveSucc'));
                this.$emit('update');
                this.onClose();
              }
            });
        }
      });
    },

    onClose() {
      this.realValue = false;
    },
  },
};
</script>
