@selectUserHeaderHeight: 33px;
@selectUserSiderWidth: 160px;
@mainColor: #8D0306;
@borderColor: #E2E2E2;
//整体
.select-user-wrap {
  position: fixed;
  z-index: 14;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: rgba(0, 0, 0, .2);
  user-select: none;

  .select-user-box {
    width: 80%;
    height: 70%;
    max-width: 1000px;
    min-width: 600px;
    min-height: 600px;
    max-height: 800px;
    margin: 100px auto;
    background: #fff;
    position: relative;
  }
}

//close-btn
.close-btn {
  position: absolute;
  right: 0;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, .8);
  background-image: url("../../assets/img/close-icon.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 10px 11px;
  margin-right: -26px;
  margin-top: -26px;
  cursor: pointer;
  transition: background-color 200ms ease;

  &:hover {
    background-color: rgba(255, 255, 255, 1);
  }
}

//头部区域
.select-user-header{
  height: @selectUserHeaderHeight;
  display: flex;
  border-bottom: 1px solid #EFEFEF;
  .header-title {
    width: @selectUserSiderWidth;
    border-right: 1px solid #EFEFEF;
    padding-left: 20px;
    line-height: @selectUserHeaderHeight;
    color: rgba(0, 0, 0, .85);
    font-size: 12px;
    font-weight: 500;
  }

  //操作区
  .header-action{
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 20px;
    font-size: 14px;
    color: @mainColor;

    span {
      cursor: pointer;
      line-height: 20px;
      position: relative;
      padding: 0 10px;
      &:last-of-type{
        &::after{
          content: "";
          display: none;
        }
      }
      &::after {
        content: "|";
        position: absolute;
        right: 0;
        height: 10px;
        font-size: 10px;
        top: -2px;
        color: #D8D8D8;
      }
    }
  }
}

//除头部区
.select-user-body {
  width: 100%;
  height: calc(100% - @selectUserHeaderHeight);
  display: flex;

  //左侧菜单
  .select-user-sider {
    width: @selectUserSiderWidth;
    height: 100%;
    border-right: 1px solid #EFEFEF;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        height: 34px;
        transition: height 200ms ease;
        overflow: hidden;

        &.open {
          height: auto;
        }
        &.sider-item{
          border-bottom: 1px solid #EFEFEF;
        }

        &.child-sider-item {
          background: #FCFCFC;

          .sider-item-title{
            background: #FCFCFC;
            font-size: 12px;
            padding-left: 36px;
            cursor: pointer;
            transition: background 200ms ease;
            &:hover {
              background: #F5F5F5;
            }

            &.selected {
              background: #F5F5F5;
              border-left: 2px solid @mainColor;
            }
          }
        }

        .sider-item-title{
          height: 100%;
          line-height: 34px;
          padding-left: 20px;
          font-size: 14px;
        }
      }
    }
  }

  //内容区(动态)
  .select-user-content{
    flex: 1;
    padding: 20px;
  }
}


.pima-select-user-header{
  height: @selectUserHeaderHeight;
  border-bottom: 1px solid #eee;
  line-height: @selectUserHeaderHeight;
  padding: 0 10px;

  .pima-select-user-header-title{
    font-size: 18px;
    font-weight: 600;
  }
}

.pima-select-user-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 40px;
}
.drawer-header-action{position: relative;}
@iconHeight: 14px;
.fullscreen-icon,
.close-icon{
  font-size: @iconHeight;
  position: absolute;
  cursor: pointer;
  top: 5px;
  opacity: .8;
  &:hover{
    opacity: 1;
  }
}
.fullscreen-icon{
  right: 40px;
}
.close-icon{
  right: 10px;
}
///deep/ .ant-select-selection--multiple .ant-select-selection__rendered{
//  min-height: 90px;
//}
/deep/ .ant-spin-nested-loading {
  height: calc(100% - @selectUserHeaderHeight);

  .ant-spin-container {
    height: 100%;
  }
}
.ant-drawer-body{
  padding: 0 24px;
}


.select-user-wrap{
  .ant-select-selection__placeholder {display: inline-block !important;}
  .ant-select-open .ant-select-selection {
    .ant-select-selection__placeholder{display: none !important;}
  }
  .ant-select-selection__rendered{
    ul li {
      display: none;
      &:last-of-type {
        display: inline-block;
      }
    }
  }
}
/** 树组件选择框 */
.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background-color: @mainColor;
  border-color: @borderColor;
}

.ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner, .ant-tree-checkbox:hover .ant-tree-checkbox-inner, .ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {
  border-color: @mainColor;
}
/** 选中还原border颜色*/
.ant-tree-checkbox-checked::after {
  border-color: @borderColor;
}