@import './variables.less';

/**
 * tree
 */
.ant-page-header-heading-tags{
  margin: 0;
}

.tree-input{
  &.ant-input-affix-wrapper{
    input.ant-input {
      height: 24px;
    }
  }
}

li .tree-node-operation {
  visibility: hidden;
  width: 12px;
  position: absolute;
  right: 19px;
  z-index: 2;
  //transform: translateY(-@treeNodeHeight);
}
.ant-tree > li > .ant-tree-node-content-wrapper > .ant-tree-title > .tree-node-operation {
  //transform: translateY(-@topTreeNodeHeight);
}

.ant-tree{
  position: relative;
  border-top: 1px solid #efefef;
  max-height: calc(100vh - 88px);
}

.ant-tree li{
  padding: 0;
}
.ant-tree-child-tree > li:first-child{
  padding-top: 0;
}

.ant-tree li .ant-tree-node-content-wrapper {
  width: calc(100% - 46px);
  height: @treeNodeHeight;
  line-height: @treeNodeHeight;

  &.ant-tree-node-selected{
    background: none;

    > .ant-tree-title > .whole-placeholder{
      background: @treeNodeSelectedBg;
      border-left: 2px solid @treeNodeSelectedBorderBg;
    }
  }

  &:hover{
    background: none;
    > .ant-tree-title .whole-placeholder{
      background: @treeNodeSelectedBg;
    }
    > .ant-tree-title .tree-node-operation{
      visibility: visible;
    }
  }
}
.ant-tree > li.ant-tree-treenode-checkbox-checked > .ant-tree-node-content-wrapper > .ant-tree-title > span{
  > .whole-placeholder{
    background: @treeNodeSelectedBg;
    border-left: 2px solid @treeNodeSelectedBorderBg;
  }
}
.ant-tree-child-tree > li.ant-tree-treenode-checkbox-checked > .ant-tree-node-content-wrapper > .ant-tree-title > span{
  > .whole-placeholder{
    background: @treeNodeSelectedBg;
    border-left: 2px solid @treeNodeSelectedBorderBg;
  }
}

.ant-tree li span.ant-tree-checkbox {
  position: relative;
  z-index: 10;
  margin-top: 5px;
}

.ant-tree > li {
  &:last-child {
    padding-bottom: 0;
  }
  &:first-child {
    padding-top: 0;
  }
  > span.ant-tree-checkbox {
    margin-top: 8px;
  }

  > .ant-tree-node-content-wrapper {
    height: @topTreeNodeHeight;
    line-height: @topTreeNodeHeight;
    width: 94%;
  }

  > .ant-tree-node-content-wrapper > .ant-tree-title .whole-placeholder{
    border-bottom: 1px solid #efefef;
    background: transparent;
    height: @topTreeNodeHeight;
  }
}
.ant-tree {
  > li > .ant-tree-node-content-wrapper > .ant-tree-title {
    font-size: 14px;
  }
  li .ant-tree-node-content-wrapper{
    color: @titleColor;
    // font-weight: 300;
    font-size: 12px;
    padding: 0;
    &.ant-tree-node-content-wrapper-open,
    &.ant-tree-node-selected{
      .ant-tree-title{
        color: @titleColor;
      }
    }
  }
  li.ant-tree-treenode-checkbox-checked {
    .ant-tree-node-content-wrapper{
      color: @titleColor;
      // font-weight: 300;
      font-size: 12px;
      padding: 0;
      .ant-tree-title{
        color: @titleColor;
      }
    }
  }
}
.ant-tree {
  li span.ant-tree-switcher,
  .ant-tree li span.ant-tree-iconEle{
    display: inline-block;
    width: 24px;
    height: @treeNodeHeight;
    position: relative;
    z-index: 5;
    margin: 0;
    line-height: @treeNodeHeight;
    text-align: center;
    vertical-align: top;
    border: 0 none;
    outline: none;
  }
  >li > span.ant-tree-switcher {
    height: @topTreeNodeHeight;
    line-height: @topTreeNodeHeight;
  }
  >li > .ant-tree-child-tree{
    padding-left: 32px;
    .ant-tree-child-tree {
      padding-left: 16px;
    }
    .ant-tree-switcher{
      //display: none;
    }
  }

  //tree展开箭头
  li span.ant-tree-switcher .ant-tree-switcher-icon{
    font-size: 8px !important;
    vertical-align: initial;
  }
  li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon svg{
    transform: rotate(90deg);
  }
}
.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon{
  font-size: 8px;
}

.ant-popover {
  box-shadow: none;
  .ant-popover-content{
    .ant-popover-inner-content{
      padding: 6px 0;
    }
  }
  .ant-popover-arrow{
    background: #fff;
    border: 1px solid #efefef;
    border-top-color: transparent;
    border-right-color: transparent;
  }
  .ant-popover-inner{
    border-radius: 0;
    box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.1);
    border: 1px solid #efefef;
  }
  .pop-content-link{
    color: #111;
    font-size: 14px;
    font-weight: 300;
    display: block;
    line-height: 30px;
    padding: 0 16px;
    &:hover{
      color: @mainColor;
      background-color: #F5F5F5;
    }
  }
}

.ant-popover-inner-content{
  font-size: 12px;
}

//占位符
.whole-placeholder{
  position: absolute;
  width: 100%;
  height: @treeNodeHeight;
  left: 0;
  background: @treeNodeBg;
  pointer-events: none;
  transition: background 200ms ease;

  & + span{
    position: absolute;
    z-index: 2;
  }
}

.tree-search-input{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 24px;
  height: 42px;
  .ant-select-combobox{
    width: 100%;
  }
}

//treenode title length
.ant-tree {
  //一级
  > li > .ant-tree-node-content-wrapper > .ant-tree-title > .whole-placeholder + span {
    max-width: calc(100% - 24px - 30px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  //二级
  > li > .ant-tree-child-tree > li > .ant-tree-node-content-wrapper > .ant-tree-title > .whole-placeholder + span {
    max-width: calc(100% - 24px - 62px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  //三级
  > li > .ant-tree-child-tree > li > .ant-tree-child-tree > li > .ant-tree-node-content-wrapper > .ant-tree-title > .whole-placeholder + span {
    max-width: calc(100% - 24px - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}


/**
 * select tree
 */
.ant-select-tree{
  position: relative;
  margin-top: 0;
  line-height: 11px;
}

.ant-select-tree li{
  padding: 0;
  margin: 0;
}
.ant-select-tree-child-tree > li:first-child{
  padding-top: 0;
}

.ant-select-tree li .ant-select-tree-node-content-wrapper {
  height: @treeNodeHeight;
  line-height: @treeNodeHeight;

  &.ant-select-tree-node-selected{
    background: none;

    > .ant-select-tree-title > .whole-placeholder{
      background: @treeNodeSelectedBg;
      border-left: 2px solid @treeNodeSelectedBorderBg;
    }
  }

  &:hover{
    background: none;
    > .ant-select-tree-title > .whole-placeholder{
      background: @treeNodeSelectedBg;
    }
  }
}

.ant-select-tree > li {
  &:last-child {
    padding-bottom: 0;
  }
  &:first-child {
    padding-top: 0;
  }

  > .ant-select-tree-node-content-wrapper {
    height: @topTreeNodeHeight;
    line-height: @topTreeNodeHeight;
    width: 90%;
  }

  > .ant-select-tree-node-content-wrapper > .ant-select-tree-title .whole-placeholder{
    border-bottom: 1px solid #efefef;
    background: transparent;
    height: @topTreeNodeHeight;
  }
}
.ant-select-tree {
  > li > .ant-select-tree-node-content-wrapper > .ant-select-tree-title {
    font-size: 14px;
  }
  li .ant-select-tree-node-content-wrapper{
    color: @titleColor;
    // font-weight: 300;
    font-size: 12px;
    padding: 0;
    &.ant-select-tree-node-content-wrapper-open,
    &.ant-select-tree-node-selected{
      .ant-select-tree-title{
        color: @titleColor;
      }
    }
  }
}
.ant-select-tree {
  li span.ant-select-tree-switcher,
  .ant-select-tree li span.ant-select-tree-iconEle{
    display: inline-block;
    width: 24px;
    height: @treeNodeHeight;
    position: relative;
    z-index: 5;
    margin: 0;
    line-height: @treeNodeHeight;
    text-align: center;
    vertical-align: top;
    border: 0 none;
    outline: none;
  }
  >li > span.ant-select-tree-switcher {
    height: @topTreeNodeHeight;
    line-height: @topTreeNodeHeight;
  }
  >li > .ant-select-tree-child-tree{
    padding-left: 32px;
    .ant-select-tree-child-tree {
      padding-left: 16px;
    }
    .ant-select-tree-switcher{
      //display: none;
    }
  }

  //tree展开箭头
  li span.ant-select-tree-switcher .ant-select-tree-switcher-icon{
    font-size: 8px !important;
    vertical-align: initial;
  }
  li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-tree-switcher-icon svg{
    transform: rotate(90deg);
  }
}
.ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-tree-switcher-icon{
  font-size: 8px;
}
.ant-select-selection-selected-value .whole-placeholder { display: none; }
.ant-select-tree {
  //一级
  > li > .ant-select-tree-node-content-wrapper > .ant-select-tree-title > .whole-placeholder + span {
    max-width: calc(100% - 24px - 30px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  //二级
  > li > .ant-select-tree-child-tree > li > .ant-select-tree-node-content-wrapper > .ant-select-tree-title > .whole-placeholder + span {
    max-width: calc(100% - 24px - 62px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  //三级
  > li > .ant-select-tree-child-tree > li > .ant-select-tree-child-tree > li > .ant-select-tree-node-content-wrapper > .ant-select-tree-title > .whole-placeholder + span {
    max-width: calc(100% - 24px - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.ant-select-tree li.filter-node > span {
  font-weight: normal;
  color: #f50;
}
