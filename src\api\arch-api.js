import archRequest from './request-arch';


// 获取个人信息
export function getUserInfo(id) {
  return new Promise((resolve, reject) => {
    archRequest({
      url: `users/${id}`,
      method: 'get',
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 获取部门
export function getDepts(params) {
  return new Promise((resolve, reject) => {
    archRequest({
      url: 'depts',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export const a = 1;

export function fetchUsers(params) {
  return new Promise((resolve, reject) => {
    archRequest({
      url: 'users',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export function getLoginUserInfo() {
  return new Promise((resolve, reject) => {
    archRequest({
      // baseURL: config.archApiBaseUrl,
      url: 'users/profile',
      methods: 'get',
    })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}
