<template>
  <PopModal
    :title="t('title')"
    :visible.sync="realValue"
    @close="onClose"
  >
    <a-spin :spinning="isLoadingCurrentUserInfo">
      <div class="drawer-bd">
        <a-form-model
          ref="formRef"
          layout="horizontal"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 15 }"
          :colon="false"
          :model="formModel"
          :rules="rules"
        >
          <a-form-item
            v-if="isEdit"
            :label="tl('venueNameTime')"
          >
            <span class="mr-8">{{ venueModel.venueName }}</span>
            <span>{{ `${venueModel.reservationDate} ${venueModel.startTime}-${venueModel.endTime}` }}</span>
          </a-form-item>
          <a-form-item
            v-else
            :label="tl('venueNameTime')"
          >
            <div
              v-for="(item, idx) in payload"
              :key="`venue-time-${idx}`"
            >
              <span class="mr-8">{{ item.venueChnName }}</span>
              <span>{{ `${item.selectedDate} ${item.startTime}-${item.endTime}` }}</span>
            </div>
          </a-form-item>

          <a-form-model-item
            prop="userId"
            :label="tl('appointerName')"
            required
          >
            <a-select
              v-model="formModel.userId"
              show-search
              :filter-option="false"
              :show-arrow="false"
              :disabled="isEdit"
              @search="(v) => debounce(handleSearch, 800)(v)"
              @change="handleChangeSelectUser"
            >
              <template slot="placeholder">
                {{ $t('user.form.repairStaffPlace') }}
              </template>
              <a-spin
                v-if="isFetchingUsers"
                slot="notFoundContent"
                size="small"
              />
              <a-select-option
                v-for="(u, index) in users"
                :key="index"
                :value="u.id"
              >
                {{ u.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-item :label="tl('appointerNumber')">
            <a-input
              v-model="userInfo.userNo"
              disabled
            />
          </a-form-item>

          <a-form-item :label="tl('departmentCollege')">
            <a-input
              v-model="userInfo.mainDeptNames"
              disabled
            />
          </a-form-item>

          <a-form-model-item
            prop="userPhone"
            required
            :label="tl('contact')"
          >
            <a-input
              v-model="formModel.userPhone"
            />
          </a-form-model-item>


          <a-form-model-item
            prop="activityName"
            required
            :label="tl('activityName')"
          >
            <a-input
              v-model="formModel.activityName"
              :placeholder="tl('enterPlace')"
            />
          </a-form-model-item>

          <a-form-model-item
            prop="notes"
            :label="tl('applicationInstructions')"
          >
            <a-textarea
              v-model="formModel.notes"
              :rows="4"
            />
          </a-form-model-item>

          <!-- 使用积分 -->
          <a-form-item
            :label="tl('integralUsed')"
          >
            {{ t('text.integrals',{integrals:integrals}) }}
          </a-form-item>

          <a-form-item
            v-if="formModel.createUserName"
            :label="tl('createUserName')"
          >
            {{ formModel.createUserName }}
          </a-form-item>
        </a-form-model>

        <div class="clearfix drawer-ft">
          <a-button @click="onClose">
            {{ $t('action.close') }}
          </a-button>
          <a-button
            type="primary"
            :loading="isSubmitingReserve"
            @click="onSubmit"
          >
            {{ $t('action.ok') }}
          </a-button>
        </div>
      </div>
    </a-spin>
  </PopModal>
</template>


<script>
import _ from 'lodash';
import { mapActions, mapMutations, mapState } from 'vuex';
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';
import { VenueStatus } from '@/constants/venue';
import { getLoginUserInfo } from '@/api/arch-api';
import { getUserIntegralDetail } from '../../../api/venue-calendar';
// import { getReservationDetailApi } from '@api/reserve-query-api';

let timer;
function debounce(fn, wait = 500) {
  // eslint-disable-next-line func-names
  return function (...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.call(this, ...args);
    }, wait);
  };
}

export default {
  components: {
    PopModal,
  },

  mixins: [
    nsI18n('t', 'venueCalendar.modalReserve'),
    nsI18n('tl', 'venueCalendar.modalReserve.form.label'),
  ],

  inject: ['reloadCalendar'],

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    payload: {
      type: [Array, Object],
      default() {
        return [];
      },
    },

    isEdit: {
      type: Boolean,
      default: false,
    },

    dataSource: {
      type: Object,
      default() {
        return null;
      },
    },
  },

  data() {
    return {
      VenueStatus,

      realValue: this.value,
      detailModel: this.createDetailModel(),
      formModel: {},
      venueModel: {
        venueName: '',
        startTime: '',
        endTime: '',
        reservationDate: '',
      },

      isFetchingUsers: false,
      userInfo: {
        id: '',
        userNo: '',
        mainDeptNames: '',
        deptId: null,
        mobile: '',
        mainDeptIds: [{ id: null }],
      },
      balancePoints: 0,
    };
  },

  computed: {
    ...mapState({
      isLoadingCurrentUserInfo: (state) => state.venueCalendar.isLoadingCurrentUserInfo,
      currentUserInfo: (state) => state.venueCalendar.currentUserInfo,
      userDepartments: (state) => state.venueCalendar.userDepartments,
      isSubmitingReserve: (state) => state.venueCalendar.isSubmitingReserve,
      users: (state) => state.venueCalendar.users,
    }),

    rules() {
      return {
        userId: [
          {
            required: true,
            message: this.$t('hint.required'),
          },
        ],
        userPhone: [
          {
            required: true,
            message: this.$t('hint.required'),
          },
        ],
        activityName: [
          {
            required: true,
            message: this.$t('hint.required'),
          },
        ],
      };
    },

    integrals() {
      if (Array.isArray(this.payload)) {
        return this.payload.reduce((acc, cur) => acc + (cur.integrals || 1), 0);
      }

      return 0;
    },
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }

      if (val) {
        this.initData();
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },

    isLoadingCurrentUserInfo(value, oldValue) {
      if (value === false && oldValue === true) {
        this.initData();
      }
    },

    
  },

  mounted() {
    this.createFormModel();
  },

  methods: {
    ...mapActions({
      fetchUserList: 'venueCalendar/fetchUserList',
    }),
    ...mapMutations({
      clearUsers: 'venueCalendar/clearUsers',
    }),
    debounce,
    createDetailModel(value) {
      return {
        appointerChnName: '',
        appointerEngName: '',
        appointerNumber: '',
        ...value,
      };
    },

    createFormModel() {
      console.log('%c [ ？ ]-319', 'font-size:13px; background:pink; color:#bf2c9f;');
      if (this.isEdit) {
        const id = this.dataSource ? this.dataSource.reservationId : 0;
        this.$store.dispatch('venueCalendar/fetchReservationDetail', id)
          .then(({ model = {} }) => {
            this.formModel = {
              userPhone: model.userPhone,
              activityName: model.activityName,
              notes: model.notes,
              createUserName: model.createUserName,
              userId: model.userId,
            };
            this.userInfo = {
              id: model.userId,
              userNo: model.userNo,
              mainDeptNames: model.deptName,
              deptId: model.deptId,
              mobile: model.userPhone,
              mainDeptIds: [{ id: model.deptId }],
            };
            this.venueModel = {
              venueName: model.venueName,
              startTime: model.startTime,
              endTime: model.endTime,
              reservationDate: this.$moment(model.reservationDate).format('YYYY-MM-DD'),
            };
          })
          .catch((error) => {
            this.$message.error(error.message);
          });
      } else {
        this.formModel = {
          userPhone: '',
          activityName: '',
          notes: '',
          createUserName: '',
          userId: '',
        };
        this.userInfo = {
          id: '',
          userNo: '',
          mainDeptNames: '',
          deptId: null,
          mobile: '',
          mainDeptIds: [{ id: null }],
        };
        getLoginUserInfo()
          .then(({ model }) => {
            this.formModel.createUserName = model.name;
          });
      }
    },

    initData() {
      this.createFormModel();
      const name = _.get(this.currentUserInfo, 'name', '');
      const enName = _.get(this.currentUserInfo, 'enName', '');
      const userNo = _.get(this.currentUserInfo, 'userNo', '');
      // const mobile = _.get(this.currentUserInfo, 'mobile', '');
      // const depts = _.get(this.currentUserInfo, 'mainDeptIds', []);
      // const deptId = depts.length > 0 ? depts[0].id : 0;
      this.detailModel = this.createDetailModel({
        appointerChnName: name,
        appointerEngName: enName,
        appointerNumber: userNo,
      });

      // Object.assign(this.formModel, {
      //   deptId,
      //   userPhone: mobile,
      // });
    },

    onSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            if (this.isEdit) {
              await this.$store.dispatch('venueCalendar/updateReserveInfo', {
                data: {
                  activityName: this.formModel.activityName,
                  mobile: this.formModel.userPhone,
                  notes: this.formModel.notes,
                },
                id: this.dataSource.reservationId,
              });
            } else {
              const {
                id: userId, name: userName, userNo, mainDeptNames: deptName,
              } = this.userInfo;
              const deptId = this.userInfo.mainDeptIds[0].id;
              const payload = [];
              this.payload.forEach((item) => {
                const {
                  venueId, selectedDate, startTime, endTime, reservationType, venueReservationTimeId, ruleTimeId,
                } = item;
                payload.push({
                  venueId,
                  reservationDate: selectedDate,
                  startTime,
                  endTime,
                  reservationType,
                  venueReservationTimeId,
                  ruleTimeId,
                  userId,
                  userName,
                  userNo,
                  deptId,
                  deptName,
                  ...this.formModel,
                });
              });
              await this.$store.dispatch('venueCalendar/reserveVenue', payload);
            }
            this.realValue = false;
            this.$message.success(this.$t('hint.dataSaved'));
            this.reloadCalendar();
          } catch (error) {
            this.$message.error(error.message);
          }
        }
      });
    },

    onClose() {
      this.realValue = false;
    },

    handleChangeSelectUser(id) {
      const [currentUser] = this.users.filter((u) => u.id === id);
      this.userInfo = currentUser;
      this.formModel.userPhone = currentUser.mobile;
    },
    handleSearch(value) {
      if (!value || !value.trim()) return;
      this.isFetchingUsers = true;
      this.clearUsers();
      this.fetchUserList({
        keyword: value,
      })
        .then(() => {
          this.isFetchingUsers = false;
        });
    },
    async fetchUserIntegral(userId) {
      try {
        const res = await getUserIntegralDetail({ userId });
        this.balancePoints = res.balancePoints;
      } catch (error) {
        this.$message.error(error.message);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.mr-8 {
  margin-right: 8px;
}
</style>
