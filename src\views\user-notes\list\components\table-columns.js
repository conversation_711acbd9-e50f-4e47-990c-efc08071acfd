// 配置table columns
export default function getColumns(vm) {
  return [
    {
      title: vm.$t('userNotes.columns.title'),
      dataIndex: 'title',
      width: '40%',
    },
    {
      title: vm.$t('userNotes.columns.sortOrder'),
      dataIndex: 'sortOrder',
      width: '10%',
    },
    {
      title: vm.$t('userNotes.columns.status'),
      scopedSlots: { customRender: 'status' },
      width: '10%',
    },
    {
      title: vm.$t('userNotes.columns.updateTime'),
      scopedSlots: { customRender: 'updateTime' },
      width: '20%',
    },
    {
      title: vm.$t('userNotes.columns.operate'),
      scopedSlots: { customRender: 'action' },
      width: '20%',
    },
  ];
}
