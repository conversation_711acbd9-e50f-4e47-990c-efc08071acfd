import Qs from 'qs';
import config from '../config';
import httpRequestArch from './arch-request';
import httpRequest from './request';

const apiList = {
  depTreeList: '/depts',
  createDep: '/depts',
  depApi: '/depts',
  depView: '/depts',
  deleteDep: '/depts',
  updateDep: '/depts',
  usersApi: '/users',
  registerUser: '/user/registry',
  roleList: '/roles',
  roleApi: '/roles',
  createRole: '/roles',
  roleView: '/role/view',
  postApi: '/posts',
  distriPost: '/posts/users',
  appList: '/applications',
  // 获取服务列表
  fmxservice: '/applications',
  setApi: '/setapi',
  // 流程
  processCategory: '/process-category',
  processDefinition: '/process-definition',
};

export function searchUserApi(keyword) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get('/users/select-person/search', {
        baseURL: config.archApiBaseUrl,
        headers: {
          Accept: ' */*',
        },
        params: {
          keyword,
        },
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getDepartmentData(param) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.depView}/${param.id}`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postCreateDep(payload, update = false) {
  return new Promise((resolve, reject) => {
    let url = apiList.createDep;
    if (update) {
      url = `${apiList.updateDep}/${payload.id}`;
    }
    httpRequestArch
      .post(url, payload.form)
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getRolePostList(id) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.roleApi}/${id}/depts`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((res) => {
        resolve(res.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postCreateRole(payload, update = false) {
  return new Promise((resolve, reject) => {
    let url = apiList.createRole;
    if (update) {
      url = `${apiList.roleList}/${payload.id}`;
    }
    httpRequestArch
      .post(url, payload.form)
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function removeDepUser(param) {
  return new Promise((resolve, reject) => {
    const { userId, depId } = param;
    httpRequestArch
      .request({
        method: 'post',
        url: `${apiList.depApi}/${depId}/users/${userId}/remove`,
      })
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function getDepUserList({ id }) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .request({
        method: 'get',
        url: `${apiList.depTreeList}/${id}/users`,
      })
      .then((r) => resolve(r.data))
      .catch((e) => reject(e));
  });
}

export function getTreeData(api) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(api || apiList.depTreeList, {
        baseURL: config.archApiBaseUrl,
        headers: {
          Accept: ' */*',
        },
      })
      .then((res) => {
        resolve(res.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function deleteDep(id) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .request({
        method: 'post',
        url: `${apiList.deleteDep}/${id}/remove`,
      })
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

/**
 * 将多个用户添加到某个职称中(选人组件)
 * @param id: {number} 职称ID
 * @param params: {deptIds?: Array<number>, isAllDept?: number, isAllPerson? number, userIds?: Array<number>,
 *   taskIds?: Array<number>}
 * @return {Promise<unknown>}
 */
export function batchAddUsersToProfessionalApi(id, params) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(`/posts/${id}/users`, {}, {
        paramsSerializer(param) {
          return Qs.stringify(param, { indices: false });
        },
        params,
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

/**
 * 将用户从岗位中删除
 * @param id: {number} 职称ID
 * @param userId: {number} 用户ID
 * @return {Promise<unknown>}
 */
export function removePostsUserApi(id, userId) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(`/posts/${id}/users/${userId}/remove`)
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

/**
 * 批量将用户从岗位中删除
 * @param id: {number} 职称ID
 * @param userIds: {Array<number>} 用户ID
 * @return {Promise<unknown>}
 */
export function batchRemoveUsersFromPostsApi(id, userIds) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(`/posts/${id}/users/batch-remove`, {}, {
        paramsSerializer(params) {
          return Qs.stringify(params, { indices: false });
        },
        params: { userIds },
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getUserlist(params) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(apiList.usersApi, {
        baseURL: config.archApiBaseUrl,
        headers: {
          Accept: ' */*',
        },
        // params: {
        //   groupId: param.groupId || '',
        // },
        params,
      })
      .then((r) => resolve(r.data))
      .catch((e) => reject(e));
  });
}

export function registryUser({ form, id }, update) {
  return new Promise((resolve, reject) => {
    let url = apiList.usersApi;
    if (update) {
      url = `${apiList.usersApi}/${id}`;
    }
    httpRequestArch
      .post(url, form)
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function getRoleList(api) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(api || apiList.roleList, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((res) => {
        resolve(res.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getRoleDetail(param) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.roleList}/${param.id}`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function deleteRole(id) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .post(
        `${apiList.roleList}/${id}/remove`,
        {},
      )
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function getUserPostList(postId, keyword) {
  return new Promise((resolve, reject) => {
    const params = {};
    if (keyword) {
      params.keyword = keyword;
    }
    httpRequest
      .get(`${apiList.postApi}/${postId}/users`, {
        headers: {
          Accept: ' */*',
        },
        params,
      })
      .then((res) => {
        resolve(res.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

// 岗位管理
// 删除岗位下用户
export function removeUserByPost({ payload }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(
        `${apiList.postApi}/${payload.postId}/users/${payload.userId}/remove`,
        {},
      )
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function getPostList() {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(apiList.postApi, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((res) => {
        resolve(res.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getPostDetail(param) {
  return new Promise((resolve, reject) => {
    httpRequest
      .get(`${apiList.postApi}/${param.id}`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function postCreatePost(payload, update = false) {
  return new Promise((resolve, reject) => {
    let url = apiList.postApi;
    if (update) {
      url = `${apiList.postApi}/${payload.id}`;
    }
    httpRequest
      .post(url, payload.form)
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function requestDeletePost(id) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(
        `${apiList.postApi}/${id}/remove`,
        {},
      )
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function postDistribute(payload) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(`${apiList.distriPost}`, payload)
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function postSinglePostUser({ payload }) {
  return new Promise((resolve, reject) => {
    httpRequest
      .post(`${apiList.postApi}/${payload.postId}/users`, payload.form)
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

// 用户管理
export function requestRemoveUser(id) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .post(
        `${apiList.usersApi}/remove?userIds=${id}`,
        {},
      )
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

// 删除多个用户
export function requestRemoveMultipleUser(userIdList) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .post(
        `${apiList.usersApi}/remove?userIds=${userIdList.join(',')}`,
        {},
      )
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

// 设置用户为主管
export function updateGroupUserSetHeader({ groupId, userIdList, directorType }) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .post(
        `groups/${groupId}/users/setHeader?userIds=${userIdList.join(',')}&type=${directorType}`,
        {},
      )
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

export function getUserInfo({ id }) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.usersApi}/${id}`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function requesSearchUser(keyword) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.usersApi}`, {
        headers: {
          Accept: ' */*',
        },
        params: {
          keyword,
          isPostAndGroup: true,
        },
      })
      .then((r) => {
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

// 重置用户密码
export function resetUserPassword({ userId }) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .post(
        `users/${userId}/resetPwd`,
        {},
      )
      .then((resp) => resolve(resp))
      .catch((err) => reject(err));
  });
}

// 应用
export function getAppList() {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.appList}`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        // eslint-disable-next-line no-console
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getModules(id) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.fmxservice}/${id}/fmxServices`, {
        headers: {
          Accept: ' */*',
        },
        params: {
          isTree: true,
        },
      })
      .then((r) => {
        // eslint-disable-next-line no-console
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function avatarUploader({ userId, fileData }) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('fileData', fileData);
    httpRequestArch
      .post(`/users/${userId}/profile-picture/upload`, formData, {
        headers: {
          Accept: ' */*',
          'Content-Type': 'multipart/form-data',
        },
      })
      .then((r) => {
        // eslint-disable-next-line no-console
        resolve(r.data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

// 系统设置
export function setBasicForm({ form }, update) {
  return new Promise((resolve, reject) => {
    let url = apiList.setApi;
    if (update) {
      url = 'settings';
    }
    httpRequestArch
      .post(url, form)
      .then((r) => resolve(r))
      .catch((e) => reject(e));
  });
}

// 发送验证邮件
export function sendTestMail(params) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get('setting/email/test', {
        headers: {
          Accept: ' */*',
        },
        params,
      })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getSettingInfo() {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get('settings', {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function getProcessCategoryInfo() {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .get(`${apiList.processCategory}`, {
        headers: {
          Accept: ' */*',
        },
      })
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function deletePostUsers(postId, userIdList) {
  return new Promise((resolve, reject) => {
    httpRequestArch
      .post(`/posts/${postId}/users/batchRemove?userIds=${userIdList.join(',')}`, {})
      .then((r) => {
        resolve(r);
      })
      .catch((e) => {
        reject(e);
      });
  });
}
