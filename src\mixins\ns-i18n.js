import _ from 'lodash';
import i18n from '../locales';
import { namespaceT } from '../helps/namespace-t';


/**
  // usage:
  mixins: [
    nsI18n('tc', 'planManagement', i18n => i18n.tc),
  ],
 */
export const nsI18n = (fnName, namespace, createT) => {
  const t = _.isFunction(createT) ? createT(i18n).bind(i18n) : undefined;

  return {
    methods: {
      [fnName]: namespaceT(namespace, t),
    },
  };
};

export default nsI18n;
