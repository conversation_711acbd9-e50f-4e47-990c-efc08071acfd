import {
  getRepairUsers,
  addRepair<PERSON>erson,
  updateRepairUserDetail,
} from '@api/basic-data-api';

const initState = {
  dataSource: [],
  loading: false,
  page: 1,
  pageSize: 10,
  total: 0,
  filter: {
    keyword: '',
    flag: '',
  },
};

const getters = {};

const mutations = {
  setDataSource(state, payload) {
    state.dataSource = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setPage(state, payload) {
    if (!payload) return;
    state.page = payload;
  },
  setPageSize(state, payload) {
    if (!payload) return;
    state.pageSize = payload;
  },
  setTotal(state, payload = 0) {
    state.total = payload;
  },
  setFilter(state, payload) {
    Object.keys(payload).forEach((key) => {
      if (payload[key] !== undefined && Object.prototype.hasOwnProperty.call(state.filter, key)) {
        state.filter[key] = payload[key];
      }
    });
  },
  clearFilter(state) {
    state.page = 1;
    state.pageSize = 10;
    Object.keys(state.filter).forEach((key) => {
      state.filter[key] = '';
    });
  },
};

const actions = {
  fetchRepairUserList({ commit, state }, payload) {
    const { filter, page, pageSize } = payload;
    commit('setFilter', filter || {});
    commit('setPage', page);
    commit('setPageSize', pageSize);
    commit('setLoading', true);

    const params = {
      ...state.filter,
      page: state.page,
      limit: state.pageSize,
    };

    return new Promise((resolve, reject) => {
      getRepairUsers(params)
        .then((res) => {
          commit('setDataSource', res.data);
          commit('setTotal', res.total);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  addRepairUser({ commit }, payload) {
    return new Promise((resolve, reject) => {
      addRepairPerson(payload)
        .then((data) => {
          resolve(data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  updateRepairUser({ commit }, payload) {
    const { userId, formData } = payload;
    return new Promise((resolve, reject) => {
      updateRepairUserDetail(userId, formData)
        .then((data) => {
          resolve(data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};

export default {
  namespaced: true,
  state: initState,
  getters,
  mutations,
  actions,
};
