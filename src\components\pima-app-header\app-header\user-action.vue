<template>
  <div class="action-wrap">
    <div
      v-if="selectableLocales.length > 1"
      class="action locale-action"
    >
      <div
        ref="localePopcorn"
        class="locale-action-wrap"
        @click="handleLocalInfoClick"
      >
        <div
          v-if="currentLocale"
          class="locale-text"
        >
          <span>{{ currentLocale.shortText }}</span>
        </div>
        <div
          v-show="localeTooltipVisible"
          ref="localeTooltip"
        >
          <div
            role="tooltip"
            class="tooltip"
          >
            <div class="tooltip-content">
              <ul
                class="locale-dropdown-menu  ant-menu ant-menu-root ant-menu-light"
              >
                <template
                  v-for="(selectableLocale, index) in selectableLocales"
                >
                  <li
                    v-if="index > 0"
                    :key="`divider-${selectableLocale.code}`"
                    class="ant-menu-item-divider"
                  />
                  <li
                    :key="selectableLocale.code"
                    class="ant-menu-item"
                    @click="handleLocaleClick(selectableLocale.code)"
                  >
                    {{ selectableLocale.fullText }}
                  </li>
                </template>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template v-if="hasMessageLink">
      <hr
        v-if="selectableLocales.length > 1"
        type="vertical"
        class="ant-divider ant-divider-vertical"
      >
      <div
        class="action message-action"
      >
        <a
          href="#"
          @click="handleClickMessage"
        >
          <i class="message-icon" />
        </a>
      </div>
    </template>

    <hr
      v-if="selectableLocales.length > 1 || hasMessageLink"
      type="vertical"
      class="ant-divider ant-divider-vertical"
    >
    <div class="action user-info-action">
      <div
        ref="userInfoPopcorn"
        class="user-info-action-wrap"
        @click="handleUserInfoClick"
      >
        <div class="user-info">
          <img
            class="avatar"
            icon="user"
            :src="userAvatarUrl"
          >
          <span class="name">
            <span class="user-name">{{ userName }}</span>
            <!-- <span class="user-account">ID: {{ userAccount }}</span> -->
          </span>
          <span>
            <span
              type="down"
              class="down-icon"
              :class="{ 'down-icon-reverse': userInfoTooltipVisible }"
            />
          </span>
        </div>
      </div>
      <div
        v-show="userInfoTooltipVisible"
        ref="userInfoTooltip"
      >
        <div
          role="tooltip"
          class="tooltip"
        >
          <div class="tooltip-content">
            <ul
              class="user-dropdown-menu ant-menu"
            >
              <li
                :key="CHANGE_PASSWORD_KEY"
                class="ant-menu-item"
                @click="handleMenuClick(CHANGE_PASSWORD_KEY)"
              >
                {{ $t('pimaUI.appHeader.personalCenter') }}
              </li>
              <li
                key="divider"
                class="ant-menu-item-divider"
              />
              <li
                :key="LOG_OUT_KEY"
                class="ant-menu-item"
                @click="handleMenuClick(LOG_OUT_KEY)"
              >
                {{ $t('pimaUI.appHeader.logout') }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createPopper } from '@popperjs/core';

const CHANGE_PASSWORD_KEY = 'change-password';
const LOG_OUT_KEY = 'logout';

export default {
  name: 'AppHeaderUserAction',
  components: {
  },
  props: {
    // 可切换语言
    supportLocales: {
      type: Array,
      default() {
        return ['zh-CN', 'zh-HK', 'en-US'];
      },
    },
    // 是否有消息链接
    hasMessageLink: {
      type: Boolean,
      default: true,
    },
    // 消息badge
    messageBadgeCount: {
      type: Number,
      default: 12,
    },
    // 用户名
    userName: {
      type: String,
      default: '',
    },
    // 账号
    userAccount: {
      type: String,
      default: '',
    },
    // 用户头像地址
    userAvatarUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      CHANGE_PASSWORD_KEY,
      LOG_OUT_KEY,
      localePopperInstance: null,
      localeTooltipVisible: false,
      userInfoPopperInstance: null,
      userInfoTooltipVisible: false,
    };
  },

  computed: {
    selectableLocales() {
      const all = [
        {
          code: 'zh-CN',
          shortText: '简',
          fullText: '简体中文',
        },
        {
          code: 'zh-HK',
          shortText: '繁',
          fullText: '繁體中文',
        },
        {
          code: 'en-US',
          shortText: 'En',
          fullText: 'English',
        },
      ];
      return all.filter(({ code }) => this.supportLocales.indexOf(code) !== -1);
    },

    currentLocale() {
      if (this.selectableLocales && this.selectableLocales.length > 0) {
        const currentLocales = this.selectableLocales.filter(({ code }) => code === this.$i18n.locale);
        if (currentLocales.length > 0) {
          const currentLocale = currentLocales.pop();
          return currentLocale;
        }
      }
      return null;
    },

    displayMessageBadgeCount() {
      return this.messageBadgeCount < 99 ? `${this.messageBadgeCount}` : '99+';
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.createLocalePopper();
      this.createUserInfoPopper();
    });
  },

  destroyed() {
    this.removeLocalePopper();
    this.removeUserInfoPopper();
  },

  methods: {
    handleLocaleClick(key) {
      this.localeTooltipVisible = false;
      this.$emit('locale', key);
    },

    handleClickMessage() {
      this.$emit('message');
    },

    handleMenuClick(key) {
      this.userInfoTooltipVisible = false;
      if (key === CHANGE_PASSWORD_KEY) {
        this.$emit('change-password');
      } else if (key === LOG_OUT_KEY) {
        this.$emit('logout');
      }
    },

    createLocalePopper() {
      if (this.$refs.localePopcorn) {
        this.localePopperInstance = createPopper(this.$refs.localePopcorn, this.$refs.localeTooltip, {
          placement: 'bottom-end',
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [-75, 0],
              },
            },
          ],
        });
        this.$refs.localePopcorn.addEventListener('mouseenter', this.handleLocalePopcornShow);
        this.$refs.localePopcorn.addEventListener('mouseleave', this.handleLocalePopcornHide);
        this.$refs.localeTooltip.addEventListener('mouseenter', this.handleLocalePopcornShow);
        this.$refs.localeTooltip.addEventListener('mouseleave', this.handleLocalePopcornHide);
      }
    },

    removeLocalePopper() {
      if (this.$refs.localePopcorn) {
        this.$refs.localePopcorn.removeEventListener('mouseenter', this.handleUserInfoPopcornShow);
        this.$refs.localePopcorn.removeEventListener('mouseleave', this.handleUserInfoPopcornHide);
      }
      if (this.$refs.localeTooltip) {
        this.$refs.localeTooltip.removeEventListener('mouseenter', this.handleUserInfoPopcornShow);
        this.$refs.localeTooltip.removeEventListener('mouseleave', this.handleUserInfoPopcornHide);
      }
      if (this.localePopperInstance) {
        this.localePopperInstance.destroy();
        this.localePopperInstance = null;
      }
    },

    handleLocalePopcornShow() {
      // this.localeTooltipVisible = true;
    },

    handleLocalePopcornHide() {
      // this.localeTooltipVisible = false;
    },

    handleLocalInfoClick() {
      this.localeTooltipVisible = !this.localeTooltipVisible;
    },

    createUserInfoPopper() {
      if (this.$refs.userInfoPopcorn) {
        this.userInfoPopperInstance = createPopper(this.$refs.userInfoPopcorn, this.$refs.userInfoTooltip, {
          placement: 'bottom-end',
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [-100, 0],
              },
            },
          ],
        });
        this.$refs.userInfoPopcorn.addEventListener('mouseenter', this.handleUserInfoPopcornShow);
        this.$refs.userInfoPopcorn.addEventListener('mouseleave', this.handleUserInfoPopcornHide);
        this.$refs.userInfoTooltip.addEventListener('mouseenter', this.handleUserInfoPopcornShow);
        this.$refs.userInfoTooltip.addEventListener('mouseleave', this.handleUserInfoPopcornHide);
      }
    },

    removeUserInfoPopper() {
      if (this.$refs.userInfoPopcorn) {
        this.$refs.userInfoPopcorn.removeEventListener('mouseenter', this.handleUserInfoPopcornShow);
        this.$refs.userInfoPopcorn.removeEventListener('mouseleave', this.handleUserInfoPopcornHide);
      }
      if (this.$refs.userInfoTooltip) {
        this.$refs.userInfoTooltip.removeEventListener('mouseenter', this.handleUserInfoPopcornShow);
        this.$refs.userInfoTooltip.removeEventListener('mouseleave', this.handleUserInfoPopcornHide);
      }
      if (this.userInfoPopperInstance) {
        this.userInfoPopperInstance.destroy();
        this.userInfoPopperInstance = null;
      }
    },

    handleUserInfoPopcornShow() {
      // this.userInfoTooltipVisible = true;
    },

    handleUserInfoPopcornHide() {
      // this.userInfoTooltipVisible = false;
    },

    handleUserInfoClick() {
      this.userInfoTooltipVisible = !this.userInfoTooltipVisible;
    },
  },
};
</script>

<style lang="less" scoped>
.action-wrap {
  height: 100%;
  display: flex;
  align-items: center;

  .ant-divider {
    background: rgba(161, 186, 231, 0.5);
    height: 18px;
    margin: 0;
    top: 0;
  }
}
hr{
  border: none;
}

.action {
  cursor: pointer;
  padding: 0 20px;
  color: #fff;

  &:hover {
    background: rgba(0, 0, 0, 0.025);
  }

  .icon {
    font-size: 12px;
    padding: 4px;
  }

  &.action-with-divider {
    position: relative;

    &::before {
      content: ' ';
      width: 1px;
      background-color: rgba(161, 186, 231, 0.5);
      position: absolute;
      top: 15px;
      bottom: 15px;
      left: 0;
    }
  }

  &.locale-action {
    .locale-text {
      width: 20px;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      text-align: center;
      position: relative;

      &::after {
        content: ' ';
        background-color: #97bdff;
        opacity: 0.49;
        border-radius: 50%;
        overflow: hidden;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }

      span {
        color: #ffffff;
      }
    }

    &:hover {
      .locale-text {
        &::after {
          opacity: 0.8;
        }
      }
    }
  }

  &.message-action {
    .message-icon {
      width: 20px;
      height: 20px;
      display: block;
      position: relative;

      &::before {
        content: ' ';
        background-color: #97bdff;
        opacity: 0.49;
        border-radius: 50%;
        overflow: hidden;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }

      &::after {
        content: ' ';
        width: 12px;
        height: 12px;
        background-size: 100% 100%;
        background-image: url('../../../assets/img/message.png');
        background-repeat: no-repeat;
        background-position: top right;
        position: absolute;
        top: calc(50% - 6px);
        right: calc(50% - 6px);
      }
    }

    &:hover {
      .message-icon {
        &::before {
          opacity: 0.8;
        }
      }
    }
  }

  &.user-info-action {
    margin-right: 17px;

    .user-info-action-wrap {
      height: 24px;
      line-height: 24px;

      .user-info {
        display: flex;

        .avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin: 0 5px 0 0;
        }

        .name {
          display: inline-block;
          vertical-align: middle;
          line-height: 1.4;
          color: #fff;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .user-name {
            display: block;
          }

          .user-account {
            display: block;
            color: rgba(255,255,255,0.68);
          }
        }

        .down-icon {
          color: rgba(255, 255, 255, 0.6);
          font-size: 10px;
          margin-left: 16px;
          transform: rotate(0deg);
          transition: transform 0.5s ease;

          &.down-icon-reverse {
            transform: rotate(-180deg);
          }
        }
      }
    }
  }
}

.locale-dropdown-menu,
.user-dropdown-menu {
  border-radius: 7px;
  /* box-shadow: 0 16px 28px -18px rgba(23, 86, 197, 0.27); */
  overflow: hidden;
  position: relative;

  &.ant-menu {
    .ant-menu-item-divider {
      background-color: transparent;
      margin: 0;
      position: relative;

      &::after {
        height: 1px;
        opacity: 0.12;
        background-color: #94afca;
        content: ' ';
        position: absolute;
        right: 7px;
        bottom: 0;
        left: 7px;
      }
    }

    .ant-menu-item {
      height: 37px;
      line-height: 37px;
      font-size: 12px;
      color: #233149;
      margin: 0;

      &.ant-menu-item-active {
        color: #1756c5;
      }
    }
  }
}

.tooltip {
  padding-top: 6px;
  /* transform: translate(-70%, 0); */

  .tooltip-content {
    position: relative;

    &::before {
      position: absolute;
      width: 5px;
      height: 5px;
      z-index: -1;
      content: '';
      transform: rotate(45deg);
      background: #ffffff;
      top: -2.5px;
      right: 16px;
    }
  }
}
</style>
