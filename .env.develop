# Express 运行端口号，必填
EXPRESS_PORT=11001

# 服务访问 URL，选填
# 不填写默认为localhost地址
# 原 HOST_REPLACEMENT 功能
# 注：URL 不包含 PUBLIC_PATH
SERVICE_URL=http://localhost:11001

# 根目录，必填
PUBLIC_PATH=/pkusz-venue-reservation/

# 宿主协议替换，如将http协议替换为https，默认值为http
HOST_PROTOCOL=http

# 服务编码，必填
SERVICE_CODE=pkusz-venue-reservation

# 客户端 ID，必填
CLIENT_ID=pima-bdc-service

# 客户端密钥，必填
CLIENT_SECRET=123456
# 部署环境 (可取值: DEV UAT PRO)
DEPLOYMENT_ENV=DEV

# 用于 Session ID 的 Session 键值，必填
SESSION_ID_COOKIE_NAME=pima.venue-reservation.sid

# 用于 Access Token 的 Cookie 键值，必填
ACCESS_TOKEN_COOKIE_NAME=pima.venue-reservation.at

# 用于 i18n 的 Cookie 键值，必填
# 如: pima-bdc-msg.locale
LOCALE_COOKIE_NAME=pima.locale

# 多语言支持语言范围
# 如：zh-CN,en-US
SUPPORT_LOCALES=zh-CN,en-US

# 多语言默认语言
# 如：zh-CN
FALLBACK_LOCALE=zh-CN


# 北大信息门户 URL
PKUSZ_BASE_PATH=https://pkuszdev.doocom.cn/pkusz-inner-portal

# 消息中心 URL
BDC_MSG_API_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-msg

# 北大信息门户中文版 URL
PKUSZ_API_BASE_URL=https://pkuszdev.doocom.cn/pkusz_inner

# 北大信息门户英文版 URL
PKUSZ_API_BASE_URL_EN=https://pkuszdev.doocom.cn/pkusz_inner_en

# Favicon图片地址
FAVICON_URL=https://pkuszdev.doocom.cn/static-resources/favicon.ico

# Logo图片地址
LOGO_URL=https://pkuszdev.doocom.cn/static-resources/logo.png

# 消息中心地址，不配置时隐藏链接
PIMA_MESSAGE_CENTRE_URL=/bdc-message/

# 修改密码地址
PIMA_CHANGE_PASSWORD_URL=https://pkuszdev.doocom.cn/pkusz-personnel

# 密码过期指引页面
PIMA_PASSWORD_OUTTIME_URL=

### 接口相关 - 起始 ###
# 公共接口 URL，必填
BDC_CORE_API_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-core

# 统一权限接口 URL，必填
AUTH_API_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-auth

# 统一数据接口 URL，必填
ARCH_API_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-arch

# 场馆预约应用接口 URL，必填
APP_API_BASE_URL=https://pkusz-apigateway.doocom.cn/pkusz_venue-reservation

# 微平台应用接口 URL，必填
WX_API_BASE_URL=https://pkusz-apigateway.doocom.cn/wx

# 统一应用接口 URL，必填
SERVICE_API_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-service

# 选人组件导入URL
IMPORT_API_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-import

# 附件管理接口 URL，必填
PIMA_UPLOAD_BASE_URL=https://pkusz-apigateway.doocom.cn/bdc-dfs

# 静态资源 URL，必填
STATIC_RESOURCES_URL=https://pkuszdev.doocom.cn/static-resources

# 远程 UI 入口文件 URL，必填
PIMA_REMOTE_UI_ENTRY_URL=https://pkuszdev.doocom.cn/remote-ui/remoteEntry.js

# 密码过期校验API接口 URL，必填
USER_PRE_VERIFY_URL=https://pkusz-apigateway.doocom.cn/user-info/user-profiles/pre-verify

# 预约审批微应用地址URL，必填
MICROSERVICE_VENUE_RESERVATION_DETAIL_URL=https://pkuszdev.doocom.cn/microservices/venue-reservation-approval-detail-js/

### 接口相关 - 结束 ###



### CAS 相关 - 起始 ###
# CAS 服务器 URL，必填
CAS_SERVICE_BASE_URL=https://pkuszdev.doocom.cn/cas

# CAS 版本，必填
CAS_SERVICE_VERSION=5.3.2
### CAS 相关 - 结束 ###


### Redis 相关 - 起始 ###
# Redis 服务器的 IP 地址
REDIS_HOST=localhost
# Redis 服务器的 UNIX 套接字字符串，选填
# 不填写默认为 6379
REDIS_PORT=6379
# 客户端将在连接上运行 Redis ** select ** 命令，选填
# REDIS_DB=
# 客户端将在连接上运行 Redis AUTH 命令，选填
# REDIS_PASSWORD=
# 用于为所有已用键添加前缀的字符串，避免与其他应用重复
REDIS_PREFIX=pima:venue-reservation:
### Redis 相关 - 结束 ###

ERROR_PAGE_404=https://pkuszdev.doocom.cn/404.html
ERROR_PAGE_500=https://pkuszdev.doocom.cn/500.html
