<template>
  <a-modal
    :visible="visible"
    :footer="null"
    :destroy-on-close="true"
    :width="800"
    @cancel="() => $emit('update:visible', false)"
  >
    <a-carousel
      :initial-slide="slideIndex"
      arrows
    >
      <div
        slot="prevArrow"
        class="custom-slick-arrow"
        style="left: -10px;zIndex: 1"
      >
        <a-icon
          style="color: #333"
          type="left-circle"
        />
      </div>
      <div
        slot="nextArrow"
        class="custom-slick-arrow"
        style="right: -10px"
      >
        <a-icon
          style="color: #333"
          type="right-circle"
        />
      </div>
      <template>
        <div
          v-for="img in imgs"
          :key="img.id"
          class="preview-img-wrap"
        >
          <img
            :src="img.src"
            alt=""
          >
        </div>
      </template>
    </a-carousel>
  </a-modal>
</template>

<script>
export default {
  name: 'PreviewImg',

  props: {
    imgs: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    slideIndex: {
      type: Number,
      default: 0,
    },
  },

  mounted() {
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-carousel .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  opacity: 0.3;
}
::v-deep .ant-carousel .custom-slick-arrow:before {
  display: none;
}
::v-deep .ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}
.preview-img-wrap {
  display: flex !important;
  justify-content: center;
  align-items: center;
  padding: 20px;
  height: 100%;

  img {
    max-width: 100% !important;
  }
}
</style>
