import httpRequest from './request';


// 获取场馆日历数据
export async function getVenueCalendar(params) {
  const res = await httpRequest({
    url: 'venues/verify',
    method: 'get',
    params,
  });
  return res.data.model;
}

// 提交预约
export async function reserveVenue(data) {
  await httpRequest({
    url: 'reservations/singed',
    method: 'post',
    params: data,
    data,
  });
}

export async function getReservationsByCode(params) {
  const res = await httpRequest({
    url: 'reservations/code',
    method: 'get',
    params,
  });
  return res.data.model;
}

export async function autoSigninByCode(params) {
  const res = await httpRequest({
    url: 'reservations/code/signed',
    method: 'post',
    params,
  });
  return res.data.model;
}

export async function getReservationsByOpenid(params) {
  const res = await httpRequest({
    url: 'reservations/scan',
    method: 'get',
    params,
  });
  return res.data.model;
}
