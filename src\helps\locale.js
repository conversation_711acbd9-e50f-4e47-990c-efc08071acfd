import { Locale } from '@/config/locale';
import { Locale as LOCALE } from '@/constants/locale';
import i18n from '@/locales';


export function currentLocale() {
  return i18n.locale;
}

export function getNewCurrentLocale() {
  const old = currentLocale();

  const mapper = new Map([
    [LOCALE.zhCN, Locale.ZH_CN],
    [LOCALE.zhHK, Locale.ZH_HK],
    [LOCALE.enUS, Locale.EN_US],
  ]);

  return mapper.get(old);
}

export function valueByLocale(chnFn, engFn) {
  if (currentLocale() === LOCALE.zhCN) {
    return typeof chnFn === 'function' ? chnFn() : chnFn;
  }

  return typeof engFn === 'function' ? engFn() : engFn;
}

export function textByLocale(
  chnText,
  engText,
  fallback = false,
) {
  if (fallback) {
    return valueByLocale(() => (chnText || engText), () => (engText || chnText));
  }

  return valueByLocale(chnText, engText);
}
