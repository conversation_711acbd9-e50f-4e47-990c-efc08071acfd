
import Qs from 'qs';
import { ReserveStatus } from '@/constants/venue';
import { blobDownload } from './blobDownload';
import httpRequest from './request';
// 获取我的预约列表
export function getMyReservationsListApi(params) {
  /** 根据后端特殊要求， status 为off时， 用其他参数传 开始 */
  const tempParams = params;
  if (tempParams.status === ReserveStatus.OFF) {
    tempParams.cancelType = tempParams.status;
    delete tempParams.status;
  }
  /** 根据后端特殊要求， status 为off时， 用其他参数传 结束 */
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/reservations/my',
      method: 'get',
      paramsSerializer() {
        return Qs.stringify(params, { indices: false });
      },
      params: tempParams,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
// 导出
export function exportMyReservationsListApi(params) {
  /** 根据后端特殊要求， status 为off时， 用其他参数传 开始 */
  const tempParams = params;
  if (tempParams.status === ReserveStatus.OFF) {
    tempParams.cancelType = tempParams.status;
    delete tempParams.status;
  }
  /** 根据后端特殊要求， status 为off时， 用其他参数传 结束 */
  return new Promise((resolve, reject) => {
    httpRequest({
      url: 'reservations/export',
      method: 'get',
      paramsSerializer() {
        return Qs.stringify(params, { indices: false });
      },
      params: tempParams,
      responseType: 'blob',
    })
      .then((r) => {
        blobDownload(r.data, params.fileName);
        resolve();
      })
      .catch((e) => {
        reject(e);
      });
  });
}
// 预约详情
export function getReservationDetailApi(id) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservations/${id}`,
      method: 'get',
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 取消预约
export function postCancelReservationApi(params) {
  const { id } = params;
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservations/${id}/cancel`,
      method: 'post',
      params,
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
