<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.UserNotes.View)"
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <template #title>
          <GoBack
            @click.native="onGoBack"
          />
          {{ headerTitle }}
        </template>
      </a-page-header>

      <div class="panel-body">
        <!-- 新增场馆页面 -->
        <AddVenue
          ref="AddVenue"
          :init-data="initData"
          :submiting="formLoading"
          @handle-add-submit="handleAddSubmit"
          @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
        />
        <div class="footer_btns">
          <div class="align-center-btns">
            <a-button
              type="default"
              @click="handleCancel"
            >
              {{ $t('reserveRule.action.cancel') }}
            </a-button>
            <a-button
              type="primary"
              @click="$refs.AddVenue.handleSubmit()"
            >
              {{ $t('action.submit') }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import operation from '@mixins/operation';
import {
  addNotesApi,
  updateNotesApi,
} from '@api/user-notes-api';
import GoBack from '@components/base/go-back.vue';

import AddVenue from './components/add-venue.vue';

const SHOW_CONTENT_TYPE = {
  ADD: 'ADD', // 新增
  VIEW: 'VIEW', // 基础数据查看页
  EDIT: 'EDIT', // 编辑
};

export default {
  name: 'UserNotes',

  components: {
    AddVenue,
    // EditVenue,
    GoBack,
  },

  mixins: [operation],

  data() {
    this.SHOW_CONTENT_TYPE = SHOW_CONTENT_TYPE;
    return {
      id: null,
      rowId: 0,
      filter: {
        keyword: '',
        status: '',
      },
      // 编辑页初始数据
      initData: {
        attachments: [],
      },
      isAdvancedSearch: false,
      record: {},
      contentType: SHOW_CONTENT_TYPE.VIEW,
      modalType: '',
      formLoading: false,
      activeRowRecord: {},
    };
  },

  computed: {
    headerTitle() {
      let title;
      switch (this.contentType) {
        case SHOW_CONTENT_TYPE.EDIT:
          title = this.$t('userNotes.title.editTitle');
          break;
        default:
          title = this.$t('userNotes.title.addTitle');
          break;
      }
      return title;
    },
  },
  beforeMount() {
  },

  mounted() {
    if (this.$route.query.id) {
      this.handleShowContentType(SHOW_CONTENT_TYPE.EDIT);
      this.id = Number(this.$route.query.id);
    }
  },

  methods: {
    ...mapActions({
    }),
    onGoBack() {
      this.$router.back();
    },
    // 提交新增表单
    handleAddSubmit(formData) {
      this.formLoading = true;
      if (this.id) {
        updateNotesApi(formData)
          .then(() => {
            this.formLoading = false;
            this.$message.success(this.$t('userNotes.msg.submitSuccess'));
            // this.closeModal();
            // this.$emit('handle-add-edit');
            this.onGoBack();
          })
          .catch((err) => {
            this.$message.error(err.response.data.errorMsg);
            this.formLoading = false;
          });
      } else {
        addNotesApi(formData)
          .then(() => {
            this.formLoading = false;
            this.$message.success(this.$t('userNotes.msg.submitSuccess'));
            // this.closeModal();
            // this.$emit('handle-add-submit');
            this.onGoBack();
          })
          .catch((err) => {
            this.$message.error(err.response.data.errorMsg);
            this.formLoading = false;
          });
      }
    },
    // 提交编辑表单
    handleEditSubmit(formData) {
      this.formLoading = true;
      updateNotesApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.$t('userNotes.msg.submitSuccess'));
          this.closeModal();
          this.$emit('handle-add-edit');
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.formLoading = false;
        });
    },
    // 修改显示类型
    handleShowContentType(type, cb) {
      new Promise((resolve) => {
        const res = cb ? cb(this) : null;
        resolve(res);
      })
        .then(() => {
          this.contentType = type;
        });
    },
    handleCancel() {
      this.onGoBack();
    },
    // 关闭表单
    closeModal() {
      this.modalType = '';
      this.activeRowRecord = {};
      this.formLoading = false;
      this.contentType = SHOW_CONTENT_TYPE.VIEW;
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-heading-title {
  display: flex;
  align-items: center;
}
.panel-body {
  padding-bottom: 80px;
}
</style>
