const publicPath = process.env.PUBLIC_PATH || '/';

const config = {
  // Session配置
  sessionIdCookieName: process.env.SESSION_ID_COOKIE_NAME || '__sid',
  // Token配置
  accessTokenCookieName: process.env.ACCESS_TOKEN_COOKIE_NAME || '__at',
  // Webpack配置
  publicPath,
  // 业务配置
  deploymentEnv: process.env.DEPLOYMENT_ENV || 'PRO',
  serviceCode: process.env.SERVICE_CODE,
  serviceType: process.env.SERVICE_TYPE,
  serviceName: process.env.SERVICE_NAME,
  locale: 'zh-CN',
  localeCookieName: process.env.LOCALE_COOKIE_NAME || '__locale',
  hostProtocol: process.env.HOST_PROTOCOL || 'http',
  supportLocales: process.env.SUPPORT_LOCALES ? process.env.SUPPORT_LOCALES.split(',') : ['zh-CN', 'zh-HK', 'en-US'],
  fallbackLocale: process.env.FALLBACK_LOCALE,
  faviconUrl: process.env.FAVICON_URL || `${process.env.PUBLIC_PATH || '/'}static/favicon.ico`,
  logoUrl: process.env.LOGO_URL || `${process.env.PUBLIC_PATH || '/'}static/logo.png`,
  apiTimeout: process.env.API_TIMEOUT || 60000,
  pimaMessageCentreUrl: process.env.PIMA_MESSAGE_CENTRE_URL,
  pimaHelpCentreUrl: process.env.PIMA_HELP_CENTRE_URL,
  pimaChangePasswordUrl: process.env.PIMA_CHANGE_PASSWORD_URL,
  pimaPasswordOuttimeUrl: process.env.PIMA_PASSWORD_OUTTIME_URL || '',
  staticResourcesUrl: process.env.STATIC_RESOURCES_URL || process.env.PUBLIC_PATH || '/',
  appApiBaseUrl: `${publicPath}x-venue-reservation-api`,
  bdcCoreApiBaseUrl: `${publicPath}x-bdc-core-api`,
  authApiBaseUrl: `${publicPath}x-bdc-auth-api`,
  archApiBaseUrl: `${publicPath}x-bdc-arch-api`,
  pimaUploadBaseUrl: `${publicPath}x-bdc-dfs-api`,
  pkuszBaseUrl: `${publicPath}x-bdc-pkusz-api`,
  pkuszBaseUrlEn: `${publicPath}x-bdc-pkuszen-api`,
  bdcMsgApiBaseUrl: `${publicPath}x-bdc-msg-api`,
  importApiBaseUrl: `${publicPath}x-bdc-import-api`,
  serviceApiBaseUrl: `${publicPath}x-bdc-service-api`,
};

export default config;
