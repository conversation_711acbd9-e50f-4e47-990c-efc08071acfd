<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.BasicData.Category)"
      class="content-panel"
    >
      <GoBack
        :title="$t('basicData.title.category')"
        @back="handleBack"
      >
        <template
          #extra
        >
          <a-button
            type="primary"
            size="small"
            @click="handleAdd"
          >
            {{ $t('basicData.action.addCategory') }}
          </a-button>
        </template>
      </GoBack>

      <div class="panel-body">
        <TableList
          :data-source="dataSource"
          :total-size="totalSize"
          :is-loading="isLoading"
          :page-info.sync="queryInfo"
          :reservation-type-options="reservationTypeOptions"
          @pageChange="onPageChange"
          @edit="handleEditblacklist"
          @update="handleTableUpdated"
          @remove="handleTableRemove"
        />
      </div>
    </div>

    <a-modal
      v-model="visibles"
      :title="modalTitle"
      :width="900"
      :footer="null"
      :mask-closable="false"
      :destroy-on-close="true"
      :dialog-style="{ left: '10px', top: '20px' }"
      :wrap-class-name="'modal-wrap'"
    >
      <CategoryForm
        :id="record.id"
        :type="type"
        @update="handleTableUpdated"
        @close="handleCloseModal"
      />
    </a-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import GoBack from '@components/base/go-back-title.vue';
import operation from '@mixins/operation';
import { ACTION_TYPE } from '@/constants/venue';
import { getReservationTypeApi } from '@/api/service-query-api';
import { delCategoryApi } from '@/api/basic-data-api';
import TableList from './table-list.vue';
import CategoryForm from './category-form.vue';

export default {
  name: 'BlackListSetting',

  components: {
    GoBack,
    TableList,
    CategoryForm,
  },

  mixins: [operation],

  data() {
    return {
      queryInfo: {
        limit: 10,
        page: 1,
      },
      record: {},
      type: 'add',
      visibles: false,
      reservationTypeOptions: [],
    };
  },

  computed: {
    ...mapState({
      dataSource: (state) => state.basicDataCategory.dataSource,
      totalSize: (state) => state.basicDataCategory.total,
      isLoading: (state) => state.basicDataCategory.loading,
    }),
    modalTitle() {
      return this.type === 'add'
        ? this.$t('basicData.title.addCategory')
        : this.$t('basicData.title.editCategory');
    },
  },

  mounted() {
    getReservationTypeApi()
      .then(({ model }) => {
        this.reservationTypeOptions = model.map((item) => ({
          code: item.code,
          value: item.code,
          name: item.name,
          label: item.name,
        }));
      });
    this.search('search');
  },

  methods: {
    handleBack() {
      this.$router.push({
        name: 'basicData',
      });
    },
    onPageChange(page, limit) {
      this.queryInfo.page = page;
      this.queryInfo.limit = limit;
      this.search('changePage');
    },
    handleEditblacklist(record) {
      this.record = record;
      this.type = 'edit';
      this.visibles = true;
    },
    handleAdd() {
      this.type = 'add';
      this.visibles = true;
    },
    handleCloseModal() {
      this.visibles = false;
    },
    handleTableUpdated(formType) {
      if (formType === ACTION_TYPE.DELETE) {
        /* eslint-disable-next-line */
        this.queryInfo.page = this.queryInfo.page > 1
          ? ((this.queryInfo.page - 1) * this.queryInfo.limit < this.totalSize - 1
            ? this.queryInfo.page
            : this.queryInfo.page - 1)
          : 1;
      }
      this.search();
    },
    search(isChangePage) {
      if (isChangePage === 'search') {
        this.queryInfo.page = 1;
      }
      this.$store.dispatch('basicDataCategory/fetchCategoryList', {
        ...this.queryInfo,
      });
    },

    handleTableRemove(record) {
      const self = this;
      this.$confirm({
        title: this.$t('msg.confirmDel'),
        class: 'pima-confrim',
        onOk() {
          delCategoryApi({
            id: record.id,
          })
            .then(() => {
              self.$message.success(self.$t('msg.delSucc'));
              self.handleTableUpdated(ACTION_TYPE.DELETE);
            });
        },
      });
    },
  },
};
</script>
