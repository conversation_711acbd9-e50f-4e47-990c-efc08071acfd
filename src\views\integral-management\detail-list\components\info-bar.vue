<template>
  <div class="info-bar ">
    <a-row>
      <a-col
        :span="4"
        :offset="1"
      >
        <span class="label">
          {{ t('label.name') }}
        </span>

        <span class="content">
          {{ handleEmptyText(model.name) }}
        </span>
      </a-col>

      <a-col :span="4">
        <span class="label">
          {{ t('label.sn') }}
        </span>

        <span class="content">
          {{ handleEmptyText(model.userNo) }}
        </span>
      </a-col>

      <a-col :span="4">
        <span class="label">
          {{ t('label.currentIntegral') }}
        </span>

        <span class="content">
          {{ handleEmptyText(model.balancePoints) }}
        </span>
      </a-col>
    </a-row>
  </div>
</template>


<script>
import { namespaceT } from '@/helps/namespace-t';


const EMPTY_TEXT = '--';

export default {
  props: {
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      t: namespaceT('integralManagement'),
      EMPTY_TEXT,
    };
  },

  methods: {
    handleEmptyText(text) {
      return text || this.EMPTY_TEXT;
    },
  },
};
</script>


<style lang="less" scoped>
.info-bar{
  margin: 15px 0;

  .label,
  .content{
    font-size: 14px;
    font-weight: 400;
    color: rgba(0,0,0,0.8500);
  }

  .label{
    margin-right: 5px;
  }
}

</style>
