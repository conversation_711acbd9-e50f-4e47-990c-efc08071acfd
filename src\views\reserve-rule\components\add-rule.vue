<template>
  <div class="drawer-bd">
    <a-spin :spinning="submiting">
      <a-form
        :form="form"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :colon="false"
        @submit="handleSubmit"
      >
        <!-- 规则名称 -->
        <a-form-item
          :label="t('chRuleName')"
        >
          <a-input
            v-decorator="['name', {
              initialValue: initData.name,
              rules: [
                { required: true, message: tf('chRuleName') },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 规则英文名称 -->
        <a-form-item
          :label="t('enRuleName')"
        >
          <a-input
            v-decorator="['enName', {
              initialValue: initData.enName,
              rules: [
                { required: true, message: tf('enRuleName') },
              ]
            }]"
            style="width:480px;"
          />
        </a-form-item>
        <!-- 预约类型 -->
        <a-form-item
          :label="t('appointmentType')"
        >
          <a-select
            v-decorator="['reservationType', {
              initialValue: initData.reservationType,
              rules: [
                { required: true, message: tf('appointmentType') }
              ]
            }]"
            :get-popup-container="
              triggerNode => {
                return triggerNode.parentNode || document.body;
              }
            "
            :options="repairMethodOpts"
            style="width:480px;"
            @change="changeReservationType"
          />
        </a-form-item>
        <!-- 是否需要预约 -->
        <a-form-item
          :label="t('isNeedReserve')"
        >
          <a-radio-group
            v-model="isNeedReserve"
            v-decorator="['isNeedReserve', {
              initialValue: true,
              rules: [
                { required: true, message: '' },
              ]
            }]"
          >
            <a-radio :value="true">
              {{ $t('common.YES') }}
            </a-radio>
            <a-radio :value="false">
              {{ $t('common.NO') }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <div v-if="isNeedReserve">
          <!-- 预约要求 -->
          <a-form-item
            :label="t('appointmentRequirements.label')"
            required
          >
            <a-card class="card">
              <a-form-item label="">
                <div class="card-item-one">
                  <span class="require-class">{{ t('appointmentRequirements.lessDay') }}</span>
                  <a-input
                    v-decorator="['minAdvanceDays', {
                      initialValue: appointmentRequirements.minAdvanceDays || 7,
                      rules: [
                        { required: true, message: tf('lessDay') },
                        { validator: isNumber },
                      ]
                    }]"
                    class="rule-input"
                  />
                  <span class="require-class">{{ t('appointmentRequirements.lessDayOrder') }}</span>
                  <span class="explain-class">
                    {{ t('appointmentRequirements.lessDayOrderExplain') }}
                  </span>
                </div>
              </a-form-item>
              <a-form-item label="">
                <div class="card-item-one">
                  <span class="require-class">{{ t('appointmentRequirements.moreDay') }}</span>
                  <a-input
                    v-decorator="['maxAdvanceDays', {
                      initialValue: appointmentRequirements.maxAdvanceDays || 14,
                      rules: [
                        { required: true, message: tf('moreDay') },
                        { validator: isNumber },
                      ]
                    }]"
                    class="rule-input"
                  />
                  <span class="require-class">{{ t('appointmentRequirements.moreDayOrder') }}</span>
                  <span class="explain-class">
                    {{ t('appointmentRequirements.moreDayOrderExplain') }}
                  </span>
                </div>
              </a-form-item>
              <a-form-item label="">
                <div class="card-item-one">
                  <span class="require-class">{{ t('appointmentRequirements.hour') }}</span>
                  <a-input
                    v-decorator="['maxHoursOneDay', {
                      initialValue: appointmentRequirements.maxHoursOneDay || 1,
                      rules: [
                        { required: true, message: tf('hourDay') },
                        { validator: isNumber },
                      ]
                    }]"
                    class="rule-input"
                  />
                  <span class="require-class">{{ t('appointmentRequirements.hourOrder') }}</span>
                  <span class="explain-class">
                    {{ t('appointmentRequirements.hourOrderExpalain') }}
                  </span>
                </div>
              </a-form-item>
              <!--  -->
              <a-form-item
                v-if="reservationType === 'activity'"
                label=""
              >
                <div class="card-item-one">
                  <span class="require-class">组织同一人同一天对同一场馆累计预约时长限制为</span>
                  <a-input
                    v-decorator="['maxHoursOneDayOrgan', {
                      initialValue: appointmentRequirements.maxHoursOneDayOrgan || 1,
                      rules: [
                        { required: true, message: tf('hourDay') },
                        { validator: isNumber },
                      ]
                    }]"
                    class="rule-input"
                  />
                  <span class="require-class">小时</span>
                  <span class="explain-class">
                    （默认为1小时，填0则不作限制）
                  </span>
                </div>
              </a-form-item>

              <div class="card-item-one">
                <span>{{ t('appointmentRequirements.timeOrder') }}</span>
                <a-popover
                  :arrow-point-at-center="true"
                  placement="rightTop"
                  overlay-class-name="popover-help"
                >
                  <template
                    slot="content"
                  >
                    <div style="width: 500px">
                      {{ t('tooltip') }}
                    </div>
                  </template>
                  <a-icon
                    type="exclamation"
                    class="icon-class"
                  />
                </a-popover>
                <div class="day-select-box">
                  <div class="day-select-one">
                    <a-checkbox
                      v-model="appointmentRequirements.isWeekday"
                      v-decorator="[
                        'isWeekday',
                        {
                          initialValue: appointmentRequirements.isWeekday,
                          rules: [{ required: false, message: tf('operateTime') }],
                        },
                      ]"
                      class="day-checkbox"
                    >
                      <span>{{ t('appointmentRequirements.weekDay') }}</span>
                    </a-checkbox>
                    <div
                      v-if="appointmentRequirements.isWeekday"
                      class="time-box"
                    >
                      <a-form-item label="">
                        <span>{{ t('appointmentRequirements.range') }}</span>
                        <TimePicker
                          v-model="appointmentRequirements.weekdayStartTime"
                          v-decorator="[
                            'weekdayStartTime',
                            {
                              initialValue: appointmentRequirements.weekdayStartTime,
                              rules: [{ required: false, message: tf('range') }],
                            },
                          ]"
                        />
                        <span>—</span>
                        <TimePicker
                          v-model="appointmentRequirements.weekdayEndTime"
                          v-decorator="[
                            'weekdayEndTime',
                            {
                              initialValue: appointmentRequirements.weekdayEndTime,
                              rules: [{ required: false, message: tf('range') }],
                            },
                          ]"
                        />
                      </a-form-item>
                    </div>
                  </div>
                  <div :class="{'day-select-two' : appointmentRequirements.isWeekday }">
                    <a-checkbox
                      v-model="appointmentRequirements.isWeekend"
                      v-decorator="[
                        'isWeekend',
                        {
                          initialValue: appointmentRequirements.isWeekend,
                          rules: [{ required: false, message: tf('operateTime') }],
                        },
                      ]"
                      class="day-checkbox"
                    >
                      <span>{{ t('appointmentRequirements.holiday') }}</span>
                    </a-checkbox>
                    <div
                      v-if="appointmentRequirements.isWeekend"
                      class="time-box"
                    >
                      <a-form-item label="">
                        <span>{{ t('appointmentRequirements.range') }}</span>
                        <TimePicker
                          v-model="appointmentRequirements.weekendStartTime"
                          v-decorator="[
                            'weekendStartTime',
                            {
                              initialValue: appointmentRequirements.weekendStartTime,
                              rules: [{ required: false, message: tf('range') }],
                            },
                          ]"
                        />
                        <span>—</span>
                        <TimePicker
                          v-model="appointmentRequirements.weekendEndTime"
                          v-decorator="[
                            'weekendEndTime',
                            {
                              initialValue: appointmentRequirements.weekendEndTime,
                              rules: [{ required: false, message: tf('range') }],
                            },
                          ]"
                        />
                      </a-form-item>
                    </div>
                  </div>
                </div>
              </div>
            </a-card>
            <!-- 预约要求必填提醒 -->
            <div
              v-if="requireAppointmentRequirements"
              class="err-msg"
            >
              {{ tf('appointmentRequirements') }}
            </div>
          </a-form-item>
          <!-- 谁可预约 -->
          <a-form-item
            :label="t('whoCanAppointment')"
            required
          >
            <a-button
              type="default"
              @click="() => visibleSelectUser = true"
            >
              {{ $t('reserveRule.action.addPerson') }}
            </a-button>
            <!-- 谁可预约必填提醒 -->
            <div
              v-if="requireSelectUserData"
              class="err-msg"
            >
              {{ tf('whoCanAppointment') }}
            </div>
          </a-form-item>
          <!-- 可预约人 -->
          <a-form-item
            :label="t('canAppointmentPerson')"
          >
            <div
              v-if="SelectUserData.scope"
              class="can-appoint-person"
            >
              <span v-if="SelectUserData.scope === 'all'">{{ t('scopeAll') }}</span>
              <div v-else>
                <span
                  v-for="(item, index) in SelectUserData.nameList"
                  :key="index"
                >
                  <span>{{ item }}</span>
                  <span v-if="index < SelectUserData.nameList.length - 1">、</span></span>
              </div>
            </div>
          </a-form-item>
          <!-- 是否需要审批 -->
          <a-form-item
            :label="t('administrator')"
          >
            <a-radio-group
              v-model="isShowApprovalTime"
              v-decorator="['isNeedApproval', {
                initialValue: false,
                rules: [
                  { required: true, message: tf('administrator') },
                ]
              }]"
            >
              <a-radio :value="VenueStatus.ENABLED">
                {{ getIsTrueStatusText(VenueStatus.ENABLED) }}
              </a-radio>
              <a-radio :value="VenueStatus.DISABLED">
                {{ getIsTrueStatusText(VenueStatus.DISABLED) }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 自动审批机制 -->
          <a-form-item
            v-if="isShowApprovalTime"
            :label="t('approvalSetting')"
          >
            <a-form-item
              required
            >
              <a-radio-group
                v-model="autoApprovalType"
                v-decorator="['autoApprovalType', {
                  initialValue: AUTO_APPROVAL_TYPE.AUTO_REJECT,
                  rules: [
                    { required: true, message: tf('administrator') },
                  ]
                }]"
              >
                <a-radio :value="AUTO_APPROVAL_TYPE.AUTO_REJECT">
                  {{ $t(getAutoApprovalTypeText(AUTO_APPROVAL_TYPE.AUTO_REJECT)) }}
                </a-radio>
                <a-radio :value="AUTO_APPROVAL_TYPE.AUTO_PASS">
                  {{ $t(getAutoApprovalTypeText(AUTO_APPROVAL_TYPE.AUTO_PASS)) }}
                </a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              required
            >
              <a-card
                class="card"
              >
                <a-radio-group
                  v-model="approvalTimeType"
                  v-decorator="['approvalTimeType', {
                    initialValue: APPROVAL_TYPE.TIME,
                    rules: [
                      { required: true, message: tf('approvalSetting') },
                    ]
                  }]"
                >
                  <a-radio
                    :value="APPROVAL_TYPE.TIME"
                    class="radio-box"
                  >
                    <a-form-item label="">
                      <div class="card-item-one">
                        <span class="require-class">{{ t('submitApprovalTime') }}</span>
                        <TimePicker
                          v-model="approvalTime"
                          v-decorator="[
                            'approvalTime',
                            {
                              initialValue: '',
                              rules: [{ required: false, message: tf('approvalSetting') }],
                            },
                          ]"
                          :disabled="approvalTimeType !== APPROVAL_TYPE.TIME"
                        />
                        <span class="require-class">
                          <template v-if="autoApprovalType === AUTO_APPROVAL_TYPE.AUTO_REJECT">
                            {{ t('approvalTime') }}
                          </template>
                          <template v-if="autoApprovalType === AUTO_APPROVAL_TYPE.AUTO_PASS">
                            {{ t('approvalPassTime') }}
                          </template>
                        </span>
                      </div>
                    </a-form-item>
                  </a-radio>
                  <a-radio
                    :value="APPROVAL_TYPE.HOUR"
                    class="radio-box"
                  >
                    <a-form-item label="">
                      <div class="card-item-one">
                        <span class="require-class">{{ t('submitApprovalHours') }}</span>
                        <a-input
                          v-decorator="['approvalHour', {
                            initialValue: approvalHour,
                            rules: [
                              { required: false, message: tf('approvalSetting') },
                              { validator: isNumber },
                            ]
                          }]"
                          :disabled="approvalTimeType !== APPROVAL_TYPE.HOUR"
                          class="rule-input"
                        />
                        <span class="require-class">
                          <template v-if="autoApprovalType === AUTO_APPROVAL_TYPE.AUTO_REJECT">
                            {{ t('approvalHours') }}
                          </template>
                          <template v-if="autoApprovalType === AUTO_APPROVAL_TYPE.AUTO_PASS">
                            {{ t('approvalPassHours') }}
                          </template>
                        </span>
                        <span class="explain-class">
                          <template v-if="autoApprovalType === AUTO_APPROVAL_TYPE.AUTO_REJECT">
                            {{ t('approvalHoursExplain') }}
                          </template>
                          <template v-if="autoApprovalType === AUTO_APPROVAL_TYPE.AUTO_PASS">
                            {{ t('approvalPassHoursExplain') }}
                          </template>
                        </span>
                      </div>
                    </a-form-item>
                  </a-radio>
                </a-radio-group>
              </a-card>
            </a-form-item>
          </a-form-item>
          <!-- 预约是否需要核销 -->
          <a-form-item>
            <span slot="label">
              {{ t('approvalIsRequired') }}
              <a-popover
                :arrow-point-at-center="true"
                placement="rightTop"
                overlay-class-name="popover-help"
              >
                <template
                  slot="content"
                >
                  <div style="width: 500px">
                    {{ t('signinTip[0]') }}<br>
                    {{ t('signinTip[1]') }}<br>
                    {{ t('signinTip[2]') }}
                  </div>
                </template>
                <a-icon
                  type="exclamation"
                  class="icon-class"
                />
              </a-popover>
            </span>
            <a-radio-group
              v-model="isNeedVerify"
              v-decorator="['isNeedVerify', {
                initialValue: false,
                rules: [
                  { required: true, message: tf('approvalIsRequired') },
                ]
              }]"
            >
              <a-radio :value="VenueStatus.ENABLED">
                {{ getIsTrueStatusText(VenueStatus.ENABLED) }}
              </a-radio>
              <a-radio :value="VenueStatus.DISABLED">
                {{ getIsTrueStatusText(VenueStatus.DISABLED) }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 核销有效时间 -->
          <a-row
            v-if="isNeedVerify"
            class="unify-form-item"
          >
            <a-col :span="4">
              <div class="unify-form-item-label">
                {{ t('signinValidTime') }} &nbsp;
              </div>
            </a-col>
            <a-col
              :span="20"
              class="unify-form-item-value"
            >
              {{ t('signinValidTimeTip[0]') }}
              <a-form-item>
                <a-input-number
                  v-decorator="['verifyAdvanceMinutes', {
                    initialValue: 0,
                    rules: [
                      { required: true, message: tf('fieldRequired') },
                    ]
                  }]"
                  :min="0"
                  class="rule-input"
                />
              </a-form-item>
              {{ t('signinValidTimeTip[1]') }}
              <a-form-item>
                <a-input-number
                  v-decorator="['verifyDelayMinutes', {
                    initialValue: 0,
                    rules: [
                      { required: true, message: tf('fieldRequired') },
                    ]
                  }]"
                  :min="0"
                  class="rule-input"
                />
              </a-form-item>
              {{ t('signinValidTimeTip[2]') }}
            </a-col>
          </a-row>
          <!-- 推送设置 -->
          <a-form-item
            :label="t('pushSettingLabel')"
          >
            <span class="require-class">
              {{ t('pushSettingBefore') }}
            </span>
            <a-input
              v-decorator="['pushingAdvanceMinutes', {
                initialValue: initData.pushingAdvanceMinutes || 30,
                rules: [
                  { required: true, message: tf('pushSettings') },
                  { validator: isNumber },
                ]
              }]"
              class="rule-input"
            />
            <span class="require-class">
              {{ t('pushSettingOrder') }}
            </span>
            <span class="explain-class">
              {{ t('pushSettingOrderExplain') }}
            </span>
          </a-form-item>
        </div>
        <!-- 适用场馆 -->
        <a-form-item
          :label="t('applicableVenues')"
        >
          <div
            v-decorator="[
              'venueIdList',
              {
                initialValue: '',
                rules: [{ required: false, message: '' }],
              },
            ]"
            style="width:1000px;"
          >
            <span
              v-for="(item, index) in applicableVenues"
              :key="index + item.name"
              class="checkbox-item"
            >
              <a-checkbox
                v-model="venueIdList[String(item.id)]"
                :disabled="ifDisabledVenue(item)"
              >
                {{ item.name }}
              </a-checkbox>
            </span>
          </div>
        </a-form-item>
        <!-- 备注 -->
        <a-form-item
          :label="t('remarks')"
        >
          <a-textarea
            v-decorator="['remark', {
              initialValue: initData.remark,
              rules: [
                { required: false, message: '' },
              ]
            }]"
            :auto-size="{ minRows: 6, maxRows: 6 }"
            :placeholder="t('remarkPlaceHolder')"
            style="width:1000px;"
          />
        </a-form-item>
        <!-- 是否启用 -->
        <a-form-item
          :label="t('isEnable')"
        >
          <a-radio-group
            v-decorator="['isEnable', {
              initialValue: true,
              rules: [
                { required: true, message: tf('isEnable') },
              ]
            }]"
          >
            <a-radio :value="VenueStatus.ENABLED">
              {{ getIsTrueStatusText(VenueStatus.ENABLED) }}
            </a-radio>
            <a-radio :value="VenueStatus.DISABLED">
              {{ getIsTrueStatusText(VenueStatus.DISABLED) }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-spin>
    <!-- 选人组件-->
    <pima-select-user
      ref="PimaSelectUser"
      :visible.sync="visibleSelectUser"
      :config="config"
      :base-url="config.importApiBaseUrl"
      :import-url="'/import-tasks/select-person'"
      :template-url="'/data/select_person_template.xlsx'"
      :template-file-name="'选人组件个人导入模板.xlsx'"
      :allowed-types="['xls', 'xlsx']"
      :initial-notices="[
        '下载导入模板，填写数据',
        '上传填好的文件，仅支持xls、xlsx格式文件',
      ]"
      :department-data="departments"
      :default-department-keys="SelectUserData.deptKeyList"
      :personal-data="userList"
      :default-personal-keys="defaultPersonal"
      :filter-prop="'label'"
      @submit="handleSelectUser"
      @close="handleSelectClose"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { VenueStatus, APPROVAL_TYPE, AUTO_APPROVAL_TYPE } from '@/constants/venue';
import config from '@config';
import { hasOwn } from '@utils/core';
import { nsI18n } from '@/mixins/ns-i18n';

import { getOrderTypeMapOpts, getIsTrueStatusText, getAutoApprovalTypeText } from './handler';

import TimePicker from './time-picker.vue';
/* eslint-disable-next-line */
const PimaSelectUser =
  () => import(/* webpackChunkName: PimaSelectUser */'@components/pima-select-user/index.vue');


export default {
  name: 'EditRule',
  components: {
    TimePicker,
    PimaSelectUser,
  },
  mixins: [
    nsI18n('t', 'reserveRule.addRule'),
    nsI18n('tf', 'reserveRule.form'),
  ],

  props: {
    submiting: {
      type: Boolean,
      default: false,
    },
    initData: {
      type: Object,
      default: () => {},
    },
    applicableVenues: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
    // 记录选人组件保存过的侧边栏类型
      pimaSelectedType: 'SELECT_ALL',
      // 是否显示审批时限
      isShowApprovalTime: false,
      // 审批时限选择时间还是小时 默认时间
      approvalTimeType: APPROVAL_TYPE.TIME,
      approvalTime: '',
      approvalHour: 2,
      saving: false,
      // 预约要求必填提醒
      requireAppointmentRequirements: false,
      // 谁可预约必填提醒
      requireSelectUserData: false,
      // 选人组件开始
      config,
      visibleSelectUser: false,
      SelectUserData: {
        userIdList: [],
        deptIdList: [],
        deptKeyList: [],
      },
      // 选人组件结束
      // 适用场馆
      venueIdList: {},
      VenueStatus,
      APPROVAL_TYPE,
      formModel: this.createFormModel(),
      reservationType: null,
      appointmentRequirements: this.createAppointmentRequirements(),
      defaultPersonal: [],
      isNeedReserve: true,
      isNeedVerify: false,
      AUTO_APPROVAL_TYPE,
      autoApprovalType: AUTO_APPROVAL_TYPE.AUTO_REJECT,
      getAutoApprovalTypeText,
    };
  },

  computed: {
    repairMethodOpts() {
      return getOrderTypeMapOpts(this);
    },
    ...mapState({
      departments: (state) => state.uniData.depList,
    }),
    userList() {
      return [...this.$store.state.uniData.userList].map((item) => ({
        ...item,
        key: item.id,
        value: item.id,
        title: item.name,
      }));
    },
  },

  mounted() {
    this.applicableVenues.forEach((item) => {
      this.venueIdList[String(item.id)] = false;
      return false;
    });
  },
  updated() {
    if (this.initData.venueList && this.initData.venueList.length) {
      this.initData.venueList.forEach((item) => {
        this.venueIdList[String(item)] = true;
        return false;
      });
    }
    if (this.initData.reservationLimit) {
      Object.entries(this.appointmentRequirements).forEach((item) => {
        (this.appointmentRequirements)[item[0]] = (this.initData.reservationLimit)[item[0]];
      });
    }
  },
  methods: {
    // 判断是否禁用
    ifDisabledVenue(value) {
      const { reservationRule } = value;
      // 判断是否存在预约规则,存在则禁用
      const tempFlag = Boolean(reservationRule);
      return tempFlag;
    },
    // 显示是否
    getIsTrueStatusText(process) {
      return getIsTrueStatusText(this, process);
    },
    // 判断是否为大于等于0的整数
    isNumber(rule, value, cb) {
      if (value) {
        const reg = /^[0-9]*$/;
        if (!reg.test(value)) {
          return cb(this.tf('number'));
        }
        if (value < 0) {
          return cb(this.tf('number'));
        }
      }
      return cb();
    },
    handleSelectClose() {
      // 关闭modal
      this.visibleSelectUser = false;
      // 重置侧边栏选中状态
      this.$refs.PimaSelectUser.activeTabKey = this.pimaSelectedType;
    },
    /**
     * 选人组件保存触发
     * @param data: {type: String, data: Object|Array} 选择的类型 => 数据
     */
    handleSelectUser(data) {
      if (data) {
        const params = {};
        const { type } = data;
        // 设置默认侧边栏选中状态
        this.$refs.PimaSelectUser.activeTabKey = type;
        // 记录保存过的侧边栏类型
        this.pimaSelectedType = type;
        if (type === 'SELECT_PERSONAL') {
          const { users, taskIds } = data.data;
          // id Array
          const usersList = users.map((model) => model.id);
          const taskIdsList = taskIds.map((model) => model.id);
          params.userIdList = usersList.concat(taskIdsList);
          // name Array
          const usersNameList = users.map((model) => model.name);
          const taskIdsNameList = taskIds.map((model) => model.name);
          params.nameList = usersNameList.concat(taskIdsNameList);
          // 作用范围:人员
          params.scope = 'user';
          this.defaultPersonal = [...users];
        } else if (type === 'SELECT_DEPARTMENT') {
          const depts = data.data;
          // id Array
          params.deptIdList = depts.map((dep) => dep.id);
          // name Array
          params.nameList = depts.map((dep) => dep.name);
          // default Array
          params.deptKeyList = depts.map((dep) => dep.key);
          // 作用范围: 部门
          params.scope = 'dept';
        } else if (type === 'SELECT_ALL') {
          // 作用范围: 全部
          params.scope = 'all';
        }
        this.SelectUserData = params;
      }
    },
    createFormModel() {
      return {
        name: '',
        enName: '',
        number: '',
        reservationType: '',
        whoCanAppointment: '',
        isNeedApproval: '',
        approvalHour: '',
        approvalTime: '',
        isNeedVerify: '',
        pushingAdvanceMinutes: 0,
        remark: '',
        isEnable: '',
      };
    },
    createAppointmentRequirements() {
      return {
        minAdvanceDays: 0,
        maxAdvanceDays: 0,
        maxHoursOneDay: 0,
        maxHoursOneDayOrgan: 0,
        isWeekday: false,
        weekdayStartTime: '',
        weekdayEndTime: '',
        isWeekend: false,
        weekendStartTime: '',
        weekendEndTime: '',
      };
    },
    handleSubmit() {
      // const tempDepeId = this.form.getFieldValue('isWeekday');
      this.form.validateFields((err, values) => {
        // 选人组件
        if (values.isNeedReserve && !this.SelectUserData.scope) {
          this.requireSelectUserData = true;
          return false;
        }
        this.requireSelectUserData = false;
        // 适用场馆
        const tempVenueIdList = (Object.entries(this.venueIdList)).filter((item) => item[1]);
        if (!err) {
          const payload = {
            reservationLimit: this.appointmentRequirements,
            ...values,
            ...this.SelectUserData,
            venueIdList: tempVenueIdList.map((item) => Number(item[0])),
            isNeedApproval: !!values.isNeedApproval,
            isNeedVerify: !!values.isNeedVerify,
            scope: values.isNeedReserve ? this.SelectUserData.scope : 'all',
            pushingAdvanceMinutes: values.isNeedReserve ? values.pushingAdvanceMinutes : 0,
          };
          // 去除payload中多余参数 开始
          Object.keys(this.appointmentRequirements).forEach((k) => {
            if (hasOwn(payload, k)) {
              payload.reservationLimit[k] = payload[k];
              delete payload[k];
            }
          });
          delete payload.nameList;
          // 自动退回机制
          const approvalTypeFlag = payload.approvalTimeType === APPROVAL_TYPE.TIME;
          payload.approvalTimeLimit = approvalTypeFlag ? payload.approvalTime : payload.approvalHour;
          delete payload.approvalHour;
          delete payload.approvalTime;
          // 去除payload中多余参数 结束
          this.$emit('handle-add-submit', payload);
        }
        return false;
      });
    },
    handleCancel() {
      this.$emit('handle-cancel-submit');
    },
    changeReservationType(e) {
      this.reservationType = e;
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
}
.card {
  width: 1000px;
  background: #FCFCFC;
  border: 1px solid #E4E4E4;
  .card-item-one {
    margin: 10px;
    .day-select-box {
      display: inline-grid;
      .day-select-one {
        margin-bottom: 10px;
      }
      .day-select-two {
        position: relative;
        margin-bottom: 10px;
        .time-box {
        position: absolute;
        margin-top: 3px;
        right: 0;
        }
      }
      .day-checkbox {
        margin-top: 10px;
      }
      .time-box {
        display: inline-block;
      }
    }
  }
  .radio-box {
    display: inline-flex;
    ::v-deep .ant-radio {
      margin: 18px -8px 0 10px;
    }
  }
}
/** 可预约人*/
.can-appoint-person {
  max-width: 1000px;
}
/** 适用场馆*/
.checkbox-item {
  margin-bottom: 16px;
  min-width: 250px;
  display: inline-block;
}

/** 全局小输入框 */
.rule-input {
  width: 50px;
  margin: 0 10px
}
/** 要求内容样式*/
.require-class {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0,0,0,0.8500);
}
/** 解释内容样式*/
.explain-class {
  font-size: 14px;
  font-weight: 300;
  color: rgba(0,0,0,0.6500);
}

.err-msg {
  padding-top: 5px;
  font-size: 12px;
  color: #D63C3C;
}
.icon-class {
  color: white;
  border-radius: 7px;
  background: #F49B00;
  width:14px;
}
.unify-form-item {
    line-height: 34px;
  .unify-form-item-label {
    text-align: right;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #f5222d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }
  ::v-deep .ant-form-item {
    display: inline-block;
  }
}
  ::v-deep .ant-radio-checked::after {
  display: none;
}
  ::v-deep .ant-radio-checked::after {
  display: none;
}
</style>

<style lang="less">
.ant-time-picker-panel-combobox {
  width: 112px;
}
</style>
