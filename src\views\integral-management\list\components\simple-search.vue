<template>
  <a-form
    :form="form"
    :label-col="{span: 5}"
    :wrapper-col="{span:15}"
    :layout="'inline'"
    label-align="right"
    :colon="false"
  >
    <a-form-item>
      <a-input
        v-decorator="['keyword', {
          initialValue: filter.keyword,
        }]"
        allow-clear
        style="width: 250px;margin-right: 10px"
        :placeholder="t('placeholder.keyword')"
        @pressEnter="handleSearch"
        @change="debounce(onSearchConditionChange, 1000)()"
      >
        <span
          slot="prefix"
          class="iconfont icon-all_sousuo"
          @click="handleSearch"
        />
      </a-input>
    </a-form-item>
  </a-form>
</template>
<script>
import { namespaceT } from '@/helps/namespace-t';
import { debounce } from '@/utils/debounce';

export default {
  name: 'SimpleSearch',
  data() {
    this.form = this.$form.createForm(this);
    return {
      t: namespaceT('integralManagement'),
      filter: {
        keyword: '',
      },
    };
  },
  methods: {
    debounce,
    handleSearch() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            ...values,
          };
          this.$emit('on-search', {
            page: 1,
            filter: payload,
          });
        }
      });
    },
    onSearchConditionChange() {
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
  },
};
</script>
