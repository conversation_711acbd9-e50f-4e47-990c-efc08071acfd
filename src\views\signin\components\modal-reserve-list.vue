<template>
  <PopModal
    :title="$t('sign.title')"
    :width="840"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="reserve">
      <div
        v-for="item in dataSouce"
        :key="item.id"
        class="reserve-item"
        @click="handleSelectItem(item)"
      >
        <div class="info-item">
          <i class="location" />
          <span>
            {{ item.venueName }}
          </span>
        </div>
        <div class="info-item">
          <i class="time" />
          <span>
            {{ `${$moment(item.reservationDate).format('YYYY-MM-DD')} ${item.startTime}-${item.endTime}` }}
          </span>
        </div>
        <div class="info-item">
          <i class="name" />
          <span>
            {{ item.userName }}
          </span>
        </div>
      </div>
    </div>
  </PopModal>
</template>

<script>
import PopModal from '@/components/base/pop-modal.vue';

export default {
  name: 'ModalReserveList',

  components: {
    PopModal,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    dataSouce: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      realValue: this.value,
    };
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }

      if (val) {
        this.initData();
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    onClose() {
      this.realValue = false;
    },
    initData() {},
    handleSelectItem(item) {
      this.$emit('select', item);
      this.onClose();
    },
  },
};
</script>

<style lang="less" scoped>
  .reserve {
    padding: 24px 60px;
    display: flex;
    flex-wrap: wrap;
    .reserve-item {
      width: 340px;
      height: 118px;
      margin-bottom: 32px;
      padding: 19px 0 19px 26px;
      border-left: 3px solid #8D0306;
      background: #F5F5F5;
      cursor: pointer;
      &:nth-child(odd) {
        margin-right: 32px;
      }
      .info-item {
        display: flex;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0,0,0,0.85);
        line-height: 20px;
        >i {
          display: inline-block;
          width: 17px;
          height: 17px;
          margin-right: 13px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          &.location {
            background-image: url('../../../assets/img/location-icon.png');
          }
          &.time {
            background-image: url('../../../assets/img/clock-icon.png');
          }
          &.name {
            background-image: url('../../../assets/img/people-icon.png');
          }
        }
      }
    }
  }
</style>
