import { ReserveStatus, TimeRangeType } from '@/constants/venue';

// 预约状态
const ReserveStatusI18Map = {
  [ReserveStatus.PENDING]: 'reserveQuery.status.PENDING',
  [ReserveStatus.PASS]: 'reserveQuery.status.PASS',
  [ReserveStatus.REJECT]: 'reserveQuery.status.REJECT',
  [ReserveStatus.CANCEL]: 'reserveQuery.status.CANCEL',
};

export function getreserveStatusI18Text(vm, progress) {
  return vm.$t(ReserveStatusI18Map[progress]);
}

export function getreserveStatusI18Options(vm, withAll = false) {
  const opts = Object.entries(ReserveStatusI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      {
        key: 'all',
        value: '',
        title: vm.$t('common.all'),
        label: vm.$t('common.all'),
      },
      ...opts,
    ]
    : [...opts];
}

// 时间范围
const TimeRangeTypeI18Map = {
  [TimeRangeType.CREATETIME]: 'reserveQuery.timeRangeType.CREATETIME',
  [TimeRangeType.APPROVALTIME]: 'reserveQuery.timeRangeType.APPROVALTIME',
};


export function getTimeRangeTypeI18Options(vm, withAll = false) {
  const opts = Object.entries(TimeRangeTypeI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      ...opts,
    ]
    : [...opts];
}
