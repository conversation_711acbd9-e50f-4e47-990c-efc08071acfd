/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable func-names */
/* eslint-disable camelcase */
/* eslint-disable no-undef */
module.exports = function (options) {
  const {
    casUserSessionName,
    tokenSessionName,
    accessTokenCookieKey,
  } = options;

  const config_casUserSessionName = casUserSessionName;
  const config_tokenSessionName = tokenSessionName || 'appToken';
  const config_accessTokenCookieKey = accessTokenCookieKey || '__ut';


  return (req, res, next) => {
    if (req.session
      && req.session[config_casUserSessionName]
      && req.session[config_tokenSessionName]
      && req.session[config_tokenSessionName].accessToken) {
      const { accessToken } = req.session[config_tokenSessionName];
      res.cookie(config_accessTokenCookieKey, accessToken, {
        httpOnly: false,
        sameSite: 'Strict',
      });
    } else {
      res.cookie(config_accessTokenCookieKey, '', {
        expires: new Date(0),
      });
    }

    next();
  };
};
