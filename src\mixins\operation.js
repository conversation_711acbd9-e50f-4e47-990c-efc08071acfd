import { mapGetters } from 'vuex';
import permission from '../constants/permission';

const componentReg = /^vue-component-[0-9]+-([A-Za-z]+)$/g;

const mixins = {
  computed: {
    ...mapGetters({
      hasOperation: 'user/hasOperation',
    }),
  },
  methods: {
    /**
     * 是否有操作权限
     * @param param: {String|Function: P => P[PermissionField]}
     * @return {boolean}
     */
    hasP(param) {
      componentReg.lastIndex = 0;
      const ENV_DEV = process.env.NODE_ENV === 'development';
      if (typeof param === 'string') {
        let operation = permission;
        let notOperation = false;
        const keys = param.split('.');
        /* eslint-disable-next-line */
        for (let i = 0; i < keys.length; i++) {
          if (operation[keys[i]] === undefined) {
            const vTag = this.$vnode.tag || '';
            let errorFound;
            if (componentReg.test(vTag)) {
              errorFound = `found in vue component [${RegExp.$1}]`;
            } else {
              errorFound = `found in vue component [${vTag}]`;
            }
            /* eslint-disable-next-line */
            ENV_DEV ? console.error(`hasP[Object]: permission [${param}] is not exist. \n --> ${errorFound}`) : null;
            notOperation = true;
            break;
          }
          operation = operation[keys[i]];
        }
        if (notOperation) {
          return false;
        }
        return this.hasOperation(operation);
      }
      if (typeof param === 'function') {
        let operation;
        try {
          operation = param(permission);
        } catch {
          operation = false;
        }
        if (operation === false || operation === undefined) {
          const vTag = this.$vnode.tag || '';
          let errorFound;
          if (componentReg.test(vTag)) {
            errorFound = `found in vue component [${RegExp.$1}]`;
          } else {
            errorFound = `found in vue component [${vTag}]`;
          }
          /* eslint-disable-next-line */
          ENV_DEV ? console.error(`hasP[fn]: permission is not exist. \n --> ${errorFound}`) : null;
          return false;
        }
        return this.hasOperation(operation);
      }
      return false;
    },
  },
};
export default mixins;
