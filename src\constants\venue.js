export const VenueStatus = Object.freeze({
  ENABLED: true,
  DISABLED: false,
});

export const VenueProperty = Object.freeze({
  INDOOR: 'INDOOR',
  OUTDOOR: 'OUTDOOR',
});

export const WeekDay = Object.freeze({
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY',
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY',
});

export const WEEK_DAY_LIST = Object.freeze([
  WeekDay.MONDAY,
  WeekDay.TUESDAY,
  WeekDay.WEDNESDAY,
  WeekDay.THURSDAY,
  WeekDay.FRIDAY,
  WeekDay.SATURDAY,
  WeekDay.SUNDAY,
]);

export const PeriodStatus = Object.freeze({
  ENABLED: 'Y',
  DISABLED: 'N',
});

export const PeriodType = Object.freeze({
  OPENED: 'OPENED',
  CLOSED: 'CLOSED',
  LOCKED: 'LOCKED',
});

export const PeriodProcessStatus = Object.freeze({
  FIXED_VENUE: 'fixed',
  AVAILABLE: 'available',
  RESERVED: 'reserved',
  EXPIRED: 'expired',
  CLOSED: 'closed',
  UNAVAILABLE: 'unavailable',
});

// 场馆类别
export const VenueCategory = Object.freeze({
  CLASS_ROOM: 'seminar_room',
  DANCE_ROOM: 'dancing_room',
  EXERCISE_ROOM: 'fitness_room',
});

// 启用状态
export const IsEnbaleMap = Object.freeze({
  TRUE: true,
  FALSE: false,
});

// 场馆属性
export const VenueAttribute = Object.freeze({
  INDOOR: 'indoor',
  OUTDOOR: 'outdoor',
});

// 取消类型
export const CancelType = Object.freeze({
  OFF: 'off',
  OTHER: 'other',
});

// 预约状态
export const ReserveStatus = Object.freeze({
  PENDING: 'pending',
  APPROVING: 'approving',
  PASS: 'pass',
  AUTO_PASS: 'auto-pass',
  PEDNING_SIGN: 'pending_sign',
  SIGNED: 'signed',
  REJECT: 'reject',
  CANCEL: 'cancel',
  OFF: 'off',
});

// 时间范围
export const TimeRangeType = Object.freeze({
  CREATETIME: 'createTime',
  APPROVALTIME: 'approvalTime',
});

// 预约类型
export const OrderTypeMap = {
  ACTIVITY: 'activity',
  COMMON: 'common',
};

// 规则状态
export const RuleStatusMap = Object.freeze({
  TRUE: true,
  FALSE: false,
});

// 是否学生组织预约状态
export const IsStudentReservation = Object.freeze({
  TRUE: true,
  FALSE: false,
});
// 接口返回选人组件类型
export const SelectScopeType = {
  ALL: 'all',
  DEPT: 'dept',
  USER: 'user',
};
// 选人组件内部类型
export const SELECT_TYPE = {
  SELECT_ALL: 'SELECT_ALL',
  SELECT_DEPARTMENT: 'SELECT_DEPARTMENT',
  SELECT_PERSONAL: 'SELECT_PERSONAL',
};

// 选人组件内部类型
export const APPROVAL_TYPE = {
  TIME: 'everyday',
  HOUR: 'hour',
};

// 自动加入黑名单类型
export const AUTO_ADD_TYPE = {
  MONTH: 'month',
  STAT: 'stat',
};

// 操作类型
export const ACTION_TYPE = {
  DELETE: 'delete',
};

// 自动审批机制
export const AUTO_APPROVAL_TYPE = {
  AUTO_REJECT: 'AUTO_REJECT',
  AUTO_PASS: 'AUTO_PASS',
};
