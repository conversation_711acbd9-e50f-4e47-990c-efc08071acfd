import { WEEK_DAY_LIST, PeriodType } from '@/constants/venue';


export function getDayByColumnIndex(columnIndex) {
  return WEEK_DAY_LIST[columnIndex - 1];
}

export function findCellUnit(record, columnIndex) {
  // 偶合，刚好接口字段也是日期名称（为小写）
  const today = getDayByColumnIndex(columnIndex).toLowerCase();
  return record[today] || null;
}

export function convertRecordToType(record) {
  if (record.isFixedVenue) {
    return PeriodType.LOCKED;
  }

  if (record.isEnable) {
    return PeriodType.OPENED;
  }

  return PeriodType.CLOSED;
}
