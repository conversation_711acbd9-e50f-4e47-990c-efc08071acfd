<template>
  <div class="drawer-bd">
    <a-spin :spinning="submitting">
      <a-form
        :form="form"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :colon="false"
        @submit="handleSubmit"
      >
        <!-- 规则名称 -->
        <a-form-item
          required
          :label="t('label.integralSetting')"
        >
          <div class="flex">
            <span class="require-class">
              {{ t('text.integralSetting[0]') }}
            </span>

            <a-form-item label="">
              <a-cascader
                v-decorator="['date', {
                  initialValue: initData.date,
                  rules: [
                    { required: true, message: t('error.date') },
                  ]
                }]"
                class="pima-cascader"
                :options="monthDayOptions"
                :field-names="{ label: 'label', value: 'value', children: 'children' }"
                :display-render="displayRender"
                :placeholder="t('placeholder.date')"
                :style="{ width: '120px' }"
              />
            </a-form-item>

            <span class="require-class">
              {{ t('text.integralSetting[1]') }}
            </span>

            <a-form-item label="">
              <a-input-number
                v-decorator="['initialIntegral', {
                  initialValue: initData.initialIntegral,
                  rules: [
                    { required: true, message: t('error.integral') },
                  ]
                }]"
                :min="0"
                :precision="0"
                :step="1"
              />
            </a-form-item>
          </div>
        </a-form-item>


        <!-- 预约扣除积分 -->
        <a-form-item
          required
          :label="t('label.deductIntegral')"
        >
          <div class="flex">
            <span class="require-class">
              {{ t('text.deductIntegral[0]') }}
            </span>

            <a-form-item>
              <a-input-number
                v-decorator="['deduceIntegral', {
                  initialValue: initData.deduceIntegral,
                  rules: [
                    { required: true, message: t('error.integral') },
                  ]
                }]"
                :min="0"
                :precision="0"
                :step="1"
              />
            </a-form-item>

            <span class="require-class">
              {{ t('text.deductIntegral[1]') }}
            </span>
          </div>
        </a-form-item>

        <a-form-item label=" ">
          <span class="explain-class">
            {{ t('text.settingTips') }}
          </span>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script>

import { fetchServiceSysParams } from '@/api/service-api';
import { IntegralSysParams } from '@/constants/integral-management';
import { namespaceT } from '@/helps/namespace-t';
import { createModelForForm } from '../hooks/model';
import {
  GetMonthDayOptions, displayRender, handleInitData, handleSubmitData,
} from '../hooks/cascader-plugins';

export default {


  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      t: namespaceT('integralManagement'),
      initData: createModelForForm(),
      monthDayOptions: [],
    };
  },

  beforeMount() {
    this.monthDayOptions = GetMonthDayOptions();
    this.fetchIntegralSysParams();
  },

  methods: {
    displayRender,
    handleInitData,
    handleSubmit() {
      this.form.validateFields((err, values) => {
        console.log('%c [ values ]-144', 'font-size:13px; background:pink; color:#bf2c9f;', values);
        if (!err) {
          const payload = {
            ...values,
          };


          console.log('%c [  ]-144', 'font-size:13px; background:pink; color:#bf2c9f;', handleSubmitData(payload));

          // this.$emit('on-submit', payload);
        }
        return false;
      });
    },

    async fetchIntegralSysParams() {
      try {
        const codes = Object.values(IntegralSysParams);
        const res = await fetchServiceSysParams(codes);

        this.handleInitData(res.data.model);
      } catch (error) {
        this.$message.error(error.message);
      }
    },
  },
};


</script>


<style lang="less" scoped>
.flex{
  display: flex;
  align-items: center;
  gap:5px;

  :deep(.ant-row.ant-form-item){
    margin-bottom: 0;
  }

  :deep(.ant-form-item-control .ant-form-item-children){
    display: flex;
    align-items: center;
    gap: 5px;
  }
}

/** 要求内容样式*/
.require-class {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0,0,0,0.8500);
}
/** 解释内容样式*/
.explain-class {
  font-size: 14px;
  font-weight: 300;
  color: rgba(0,0,0,0.6500);
}
</style>
