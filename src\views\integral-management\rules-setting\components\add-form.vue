<template>
  <div class="drawer-bd">
    <a-spin :spinning="submitting">
      <a-form
        :form="form"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :colon="false"
        @submit="handleSubmit"
      >
        <!-- 规则名称 -->
        <a-form-item
          required
          :label="t('label.integralSetting')"
        >
          <div class="flex">
            <span class="require-class">
              {{ t('text.integralSetting[0]') }}
            </span>

            <a-form-item label="">
              <SelectMonthAndDays
                v-decorator="['date', {
                  rules: [
                    { required: true, message: t('error.date') },
                  ]
                }]"
                :default-value="initData.date"
                :style="{ width: '120px' }"
              />
            </a-form-item>

            <span class="require-class">
              {{ t('text.integralSetting[1]') }}
            </span>

            <a-form-item label="">
              <a-input-number
                v-decorator="['initialIntegral', {
                  initialValue: initData.initialIntegral,
                  rules: [
                    { required: true, message: t('error.integral') },
                  ]
                }]"
                :min="0"
                :precision="0"
                :step="1"
              />
            </a-form-item>
          </div>
        </a-form-item>


        <!-- 预约扣除积分 -->
        <a-form-item
          required
          :label="t('label.deductIntegral')"
        >
          <div class="flex">
            <span class="require-class">
              {{ t('text.deductIntegral[0]') }}
            </span>

            <a-form-item>
              <a-input-number
                v-decorator="['deduceIntegral', {
                  initialValue: initData.deduceIntegral,
                  rules: [
                    { required: true, message: t('error.integral') },
                  ]
                }]"
                :min="0"
                :precision="0"
                :step="1"
              />
            </a-form-item>

            <span class="require-class">
              {{ t('text.deductIntegral[1]') }}
            </span>
          </div>
        </a-form-item>

        <a-form-item label=" ">
          <span class="explain-class">
            {{ t('text.settingTips') }}
          </span>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script>
import { fetchServiceSysParams } from '@/api/service-api';
import { IntegralSysParams } from '@/constants/integral-management';
import { namespaceT } from '@/helps/namespace-t';
import { createModelForForm } from '../hooks/model';
import SelectMonthAndDays from './select-month-and-days.vue';

export default {

  components: {
    SelectMonthAndDays,
  },

  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      t: namespaceT('integralManagement'),
      initData: createModelForForm(),
    };
  },

  beforeMou

  methods: {
    handleSubmit() {
      if (!this.form.getFieldValue('date')) {
        this.form.setFieldsValue({
          date: this.initData.date,
        });
      }

      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            ...values,
          };

          this.$emit('on-submit', payload);
        }
        return false;
      });
    },

    handleInitData(list) {
      if (!(Array.isArray(list) && list.length > 0)) {
        return;
      }

      const map = new Map([
        ['date', IntegralSysParams.COST_POINTS_REST_DATE],
        ['initialIntegral', IntegralSysParams.DEFAULT_COST_POINTS],
        ['deduceIntegral', IntegralSysParams.APPLY_DEDUCTION_OF_POINTS],
      ]);

      Object.keys(this.initData).forEach((key) => {
        if (map.has(key)) {
          const value = list.find((item) => item.code === map.get(key))?.value || null;
          this.initData[key] = value;
        }
      });
    },

    async fetchIntegralSysParams() {
      try {
        const codes = Object.values(IntegralSysParams);
        const res = await fetchServiceSysParams(codes);

        this.handleInitData(res.model);


        console.log('%c [  ]-169', 'font-size:13px; background:pink; color:#bf2c9f;', this.initData);
      } catch (error) {
        this.$message.error(error.message);
      }
    },
  },
};


</script>


<style lang="less" scoped>
.flex{
  display: flex;
  align-items: center;
  gap:5px;

  :deep(.ant-row.ant-form-item){
    margin-bottom: 0;
  }

  :deep(.ant-form-item-control .ant-form-item-children){
    display: flex;
    align-items: center;
    gap: 5px;
  }
}

/** 要求内容样式*/
.require-class {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0,0,0,0.8500);
}
/** 解释内容样式*/
.explain-class {
  font-size: 14px;
  font-weight: 300;
  color: rgba(0,0,0,0.6500);
}
</style>
