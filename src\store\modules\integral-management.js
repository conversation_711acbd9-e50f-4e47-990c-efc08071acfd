import { integralManagementListApi, integralDetailApi } from '@/api/integral-management';

const initState = {
  dataSource: [],
  loading: false,
  page: 1,
  pageSize: 10,
  total: 0,
  filter: {
    keyword: '',
  },

  detail: {
    dataSource: [],
    loading: false,
    page: 1,
    pageSize: 10,
    total: 0,
    id: null,
    userInfo: {
      name: null,
      userNo: null,
      balancePoints: null,
    },
  },
};

const getters = {};

const mutations = {
  setDataSource(state, payload) {
    state.dataSource = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setPage(state, payload) {
    if (!payload) return;
    state.page = Number(payload);
  },
  setPageSize(state, payload) {
    if (!payload) return;
    state.pageSize = Number(payload);
  },
  setTotal(state, payload = 0) {
    state.total = payload;
  },
  setFilter(state, payload) {
    Object.keys(payload).forEach((key) => {
      if (payload[key] !== undefined && Object.prototype.hasOwnProperty.call(state.filter, key)) {
        state.filter[key] = payload[key];
      }
    });
  },

  setDetailDataSource(state, { data, total }) {
    state.detail.dataSource = data;
    state.detail.total = total;
  },

  setDetailLoading(state, payload) {
    state.detail.loading = payload;
  },

  setDetailPage(state, { page, pageSize }) {
    state.detail.page = Number(page);
    state.detail.pageSize = Number(pageSize);
  },

  setDetailId(state, payload) {
    state.detail.id = payload;
  },

  setDetailUserInfo(state, payload) {
    Object.keys(state.detail.userInfo).forEach((key) => {
      state.detail.userInfo[key] = payload[key];
    });
  },

  resetDetails(state) {
    Object.assign(state.detail, {
      dataSource: [],
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0,
      id: null,
      userInfo: {
        name: null,
        userNo: null,
        balancePoints: null,
      },
    });
  },
};

const actions = {
  // 积分管理 列表接口
  async fetchIntegralManagementList({ commit, state }, payload) {
    try {
      if (payload) {
        const { filter, page, pageSize } = payload;
        commit('setFilter', filter || {});
        commit('setPage', page || state.page);
        commit('setPageSize', pageSize || state.pageSize);
      }
      commit('setLoading', true);

      const params = {
        page: state.page,
        limit: state.pageSize,
      };

      Object.entries(state.filter).forEach(([k, v]) => {
        if (v !== '') params[k] = v;
      });


      const res = await integralManagementListApi(params);

      commit('setDataSource', res.data);
      commit('setTotal', res.total);
    } catch (error) {
      this.$message.error(error.message);
    } finally {
      commit('setLoading', false);
    }
  },

  // 积分详情 列表接口
  async fetchIntegralDetailList({ commit, state }, payload) {
    try {
      let isSameId = false;
      if (payload) {
        const { page, pageSize, id } = payload;
        if (id) {
          isSameId = state.detail.id === id;
          commit('setDetailId', id);
        }

        commit('setDetailPage', { page, pageSize });
      }

      commit('setDetailLoading', true);

      const params = {
        page: state.detail.page,
        limit: state.detail.pageSize,
      };


      const res = await integralDetailApi({
        id: state.detail.id,
        params,
      });

      if (!isSameId && res.other) {
        commit('setDetailUserInfo', res.other);
      }

      commit('setDetailDataSource', res);
    } catch (error) {
      this.$message.error(error.message);
    } finally {
      commit('setDetailLoading', false);
    }
  },
};

export default {
  namespaced: true,
  state: initState,
  getters,
  mutations,
  actions,
};
