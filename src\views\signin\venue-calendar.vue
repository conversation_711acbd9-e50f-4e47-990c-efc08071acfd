<template>
  <div
    v-if="!loadingProfile && hasP((P) => P.VerifyCalendar.View)"
    class="page-panel clearfix"
  >
    <div class="content-panel">
      <div class="signin-search">
        <a-input
          ref="searchKeyword"
          v-model="searchKeyword"
          :placeholder="$t('venueCalendar.verifySearchPlace')"
          class="input"
          auto-focus
          @pressEnter="onSearchChange"
          @keydown.native="posOrInput"
        />
        <a-button
          type="primary"
          class="submit"
          @click="onSearchChange"
        >
          {{ $t('action.verify') }}
        </a-button>
      </div>

      <div class="panel-body">
        <div class="filter">
          <div class="status-info">
            <span class="item">
              <span class="ico ico-available" />
              <span>{{ $t('venueCalendar.status.pending') }}</span>
            </span>
            <span class="item">
              <span class="ico ico-signed" />
              <span>{{ $t('venueCalendar.status.signed') }}</span>
            </span>
            <span class="item">
              <span class="ico ico-closed" />
              <span>{{ $t('venueCalendar.status.invalid') }}</span>
            </span>
          </div>
          <span class="filter-label">{{ t('simpleSearch.form.label.appointmentDate') }}</span>
          <div class="date-wrap">
            <div class="prev-next-wrap">
              <PrevNext
                v-if="today"
                v-model="today"
                @prev="onPrevNextChange"
                @next="onPrevNextChange"
              />
            </div>
            <a-date-picker
              v-model="datePickerDate"
              :disabled-date="disabledDate"
              dropdown-class-name="date-picker-no-input"
              input-read-only
              @change="onDatePickerChange"
            >
              <a-button
                type="link"
                class="btn-calendar"
              >
                <i class="ico-calendar" />
              </a-button>
            </a-date-picker>
          </div>
          <div class="calendar-tips">
            <a-popconfirm
              placement="right"
              overlay-class-name="calendar-tips-pop"
              :visible="tipsVisible"
              :get-popup-container="getPopupContainer"
              :overlay-style="{'z-index': 1}"
            >
              <span slot="icon" />
              <template slot="title">
                {{ $t('venueCalendar.verifyTableTips') }}
              </template>
              <a-icon
                type="exclamation-circle"
                theme="filled"
                class="icon"
                @click="onTips"
              />
            </a-popconfirm>
          </div>
        </div>

        <TableList
          :columns="columns"
          :data-source="dataSource"
          :venues="venues"
          :selected-date="today"
          :is-loading="isLoading"
        />
        <ModalReserve
          v-model="isShowModalReserve"
          :payload="payload"
        />
        <ModalReserveList
          v-model="isShowModalChooseReserve"
          :data-souce="reserveList"
          @select="handleSelectItem"
        />
      </div>
    </div>
  </div>
</template>


<script>
import _ from 'lodash';
import moment from 'moment';
import { mapState } from 'vuex';
import OpreationMixin from '@/mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';
import {
  // getReservationsByCode,
  getReservationsByOpenid,
  autoSigninByCode,
} from '@api/verify';
import PrevNext from './components/prev-next.vue';
import TableList from './components/table-list.vue';
import ModalReserve from './components/modal-reserve.vue';
import ModalReserveList from './components/modal-reserve-list.vue';
import { shiftCodeMap, codeMap } from './code-mapper';

const sequence = (option) => {
  let seq = [];
  let id = null;

  const func = (...args) => {
    const u = option.process(...args);
    // seq.push(u);
    let key = null;
    if (u.keyCode === 229) {
      key = u.code.slice(-1);
    } else {
      key = u.key;
    }
    key = codeMap(u.code) || u.key;

    seq.push({
      key: key.toLowerCase(),
      code: u.code,
      altKey: u.altKey,
      ctrlKey: u.ctrlKey,
      shiftKey: u.shiftKey,
      metaKey: u.metaKey,
      keyCode: u.keyCode,
    });

    clearTimeout(id);
    id = setTimeout(
      () => {
        option.result(seq);
        seq = [];
      },
      option.interal,
    );
  };

  return func;
};

const isPosSequence = (seq) => {
  const a = _.last(seq).code === 'Enter';
  const b = _.initial(seq).length > 1;
  // 输入只有1位，并且没有按alt/ctrl/shift
  const c = _.initial(seq).every((u) => u.key.length === 1 && !u.altKey && !u.ctrlKey);
  return a && b && c;
};


export default {
  name: 'VenueCalendar',

  components: {
    PrevNext,
    TableList,
    ModalReserve,
    ModalReserveList,
  },

  mixins: [
    OpreationMixin,
    nsI18n('t', 'venueCalendar'),
  ],

  provide() {
    return {
      reloadCalendar: this.loadData,
      canReserve: this.canReserve,
    };
  },

  data() {
    return {
      today: null,
      datePickerDate: moment(),
      columns: [],
      searchKeyword: '',
      code: '',

      isShowModalReserve: false,
      payload: {},
      tipsVisible: true,
      reserveList: [],
      isShowModalChooseReserve: false,
      timer: null,
      isMove: false,
      posOrInput: () => {},
    };
  },

  computed: {
    ...mapState({
      loadingProfile: (state) => state.user.loadingProfile,
      dataSource: (state) => state.verify.periods,
      venues: (state) => state.verify.venues,
      isLoading: (state) => state.verify.isLoadingCalendar,
    }),

    canReserve() {
      return () => this.hasP((P) => P.VerifyCalendar.Signin);
    },
  },

  watch: {
    loadingProfile(value) {
      if (value === false) {
        this.loadUserData();
      }
    },

    today(value) {
      this.datePickerDate = moment(value);
    },
  },

  mounted() {
    this.today = new Date();
    this.loadData();
    this.loadUserData();
    if (this.$refs.searchKeyword) {
      this.$refs.searchKeyword.focus();
    }
    this.mousemove();
    this.bindEvent();
  },

  beforeDestroy() { // 生命周期 销毁前
    clearInterval(this.timer);
    window.onmousemove = null;
  },

  methods: {
    mousemove() {
      const that = this;
      window.onmousemove = () => {
        this.isMove = true;
        clearInterval(that.timer);
        that.timer = setInterval(() => {
          that.isMove = false;
          if (that.$refs.searchKeyword) {
            that.$refs.searchKeyword.focus();
          }
        }, 10000);
      };
    },
    disabledDate(current) {
      // return current && current < moment().endOf('day');
      return current < moment().subtract(1, 'day');
    },
    async loadUserData() {
      await this.$store.dispatch('verify/fetchCurrentUserInfo');
    },

    async loadData() {
      const payload = {
        reservationDate: moment(this.today).format('yyyy-MM-DD'),
        isNeedVerify: true,
      };
      await this.$store.dispatch('verify/fetchVenueCalendar', payload);
      const columns = this.venues.map((item) => item.name);
      this.columns = columns;
    },

    onPrevNextChange(value) {
      this.today = value;
      this.loadData();
    },

    onDatePickerChange(value) {
      this.today = value.toDate();
      this.loadData();
    },
    getQuery(key) {
      const ops = {};
      const params = this.searchKeyword.split('?')[1];
      if (params) {
        // eslint-disable-next-line
        params.split('&').map((i) => ops[(i.split('=')[0])] = i.split('=')[1]);
        return ops[key];
      }
      return null;
    },
    validatorCode(val) {
      // eslint-disable-next-line prefer-regex-literals
      return new RegExp('^[0-9]{6}$').test(val);
    },
    onSearchChange() {
      // eslint-disable-next-line prefer-regex-literals
      // const validator = new RegExp('^[0-9]{6}$').test(this.searchKeyword);
      // eslint-disable-next-line
      const validatorUrl = new RegExp('^((http|https):\/\/).*$').test(this.searchKeyword);
      if (this.validatorCode(this.searchKeyword)) {
        this.searchSubmit(this.searchKeyword);
      } else if (validatorUrl) {
        const code = this.getQuery('code');
        if (code) {
          if (this.validatorCode(code)) {
            this.searchKeyword = code;
            this.searchSubmit(code);
          }
        }
      } else if (this.searchKeyword) {
        this.$message.error(this.$t('sign.errorCodeTip'));
      }
      this.$refs.searchKeyword.select();
    },
    handleSelectItem(item) {
      this.isShowModalReserve = true;
      this.payload = item;
    },
    searchSubmit(code) {
      const params = {
        code,
      };
      this.formLoading = true;
      // 输入6位数字，则直接进行核销
      autoSigninByCode(params)
        .then(() => {
          this.searchKeyword = '';
          this.$message.success(this.$t('sign.signinSucc'));
          this.loadData();
        })
        .finally(() => {
          this.formLoading = false;
          this.$refs.searchKeyword.select();
        });
    },
    searchSubmitByOpenid(code) {
      const params = {
        code,
      };
      this.formLoading = true;
      getReservationsByOpenid(params)
        .then((res) => {
          this.formLoading = false;
          if (res && res.length === 1) {
            this.isShowModalReserve = true;
            [this.payload] = res;
          } else if (res && !res.length) {
            this.$message.error(this.t('verifyNoDataTip'));
          } else {
            this.isShowModalChooseReserve = true;
            this.reserveList = res;
          }
        })
        .catch(() => {
          this.formLoading = false;
        });
    },
    onTips() {
      this.tipsVisible = !this.tipsVisible;
    },
    getPopupContainer(trigger) {
      return trigger.parentElement;
    },
    bindEvent() {
      const self = this;
      this.posOrInput = sequence({
        process(event) {
          return event;
        },
        result(seq) {
          const urlStrArr = seq
            .map((u, idx) => {
              if (idx > 0
                && (seq[idx - 1].code === 'ShiftLeft' || seq[idx - 1].code === 'ShiftRight')) {
                let { key } = u;
                // 前一个是shift键
                key = shiftCodeMap(u.code) || u.key;
                return {
                  ...u,
                  key,
                };
              }
              return u;
            })
            .filter((u) => !(u.code === 'ShiftLeft' || u.code === 'ShiftRight'));
          if (isPosSequence(urlStrArr)) {
            const urlStr = _.map(_.initial(urlStrArr), 'key').join('');
            self.searchKeyword = urlStr;
            // eslint-disable-next-line
            const validatorUrl = new RegExp('^((http|https):\/\/).*$').test(self.searchKeyword);
            if (validatorUrl) {
              const code = self.getQuery('code');
              if (code) {
                if (self.validatorCode(code)) {
                  self.searchKeyword = code;
                  self.searchSubmit(code);
                }
              }
            }
            self.$refs.searchKeyword.select();
          }
        },
        interal: 60,
      });
    },
  },
};
</script>


<style lang="less">
.date-picker-no-input {
  padding-top: 40px;

  .ant-calendar-input-wrap {
    display: none;
  }
}
.calendar-tips-pop {
  .ant-popover-buttons {
    display: none;
  }
  .ant-popover-message {
    padding: 0 10px;
    > .anticon {
      top: 4px;
    }
  }
}

.ant-popover-message-title{
  padding-left: 0;
}
</style>

<style lang="less" scoped>
::v-deep .ant-calendar-picker {
  height: 40px;
}
.signin-search {
  padding: 30px 24px;
  text-align: center;
  background-color: #f9f9f9;
  .input {
    height: 48px;
    line-height: 48px;
    width: 40%;
  }
  .submit {
    height: 48px;
  }
}

.calendar-tips {
  display: inline-block;
  margin-left: 50px;
  .icon {
    font-size: 14px;
    color: #faad14;
  }
}
.filter {
  margin-top: 12px;
  margin-bottom: 10px;

  .filter-label {
    vertical-align: text-bottom;
  }

  .date-wrap {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    // border: 1px solid #ededed;

    .prev-next-wrap {
      display: inline-block;
      padding: 0 6px;
      border: 1px solid #ededed;

      ::v-deep .prev-next {
        height: 38px;
      }
    }

    .btn-calendar {
      padding: 0 14px;
      min-width: auto;
      height: 100%;
      border: 1px solid #8D0306;
      border-radius: 0;
    }

    .ico-calendar {
      display: inline-block;
      vertical-align: middle;
      margin-top: -3px;
      width: 24px;
      height: 24px;
      background: url('~@assets/img/calendar.png') no-repeat center center / auto 100%;
    }
  }
  .status-info{
    float: right;
    margin-top: 20px;
    .item {
      display: inline-block;
      margin-left: 32px;
      .ico {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: #F2F2F2;
        border: 1px solid rgba(0,0,0,0.65);
        vertical-align: -5px;
      }
      .ico-available {
        background: #FFF9EF;
        border-color: #F49B00;
      }
      .ico-signed {
        background: #E8F6E6;
        border-color: #4FAD4E;
      }
      .ico-closed {
        background: #F2F2F2;
        border-color:rgba(0,0,0,0.65);
      }
    }
  }
}
.content-panel .panel-body {
  height: calc(100vh - 40px - 108px);
}
</style>
