
const venueTimeSetting = {
  simpleSearch: {
    form: {
      keywordPlaceholder: 'Please key in the venue number of venue name',
    },
  },
  tableList: {
    columns: {
      venueNumber: 'Venue number',
      venueName: 'Venue name',
      venueCategory: 'Venue category',
      capacity: 'Number of people that can be accommodated',
      appointmentRule: 'Booking rules',
      lastAction: 'Last operator/Last operating time',
      status: 'Status',
      action: 'Operation',
    },
    action: {
      timeSetting: 'Time settings',
    },
  },

  modalDetail: {
    title: 'Venue Details',
    form: {
      label: {
        venueChnName: 'Chinese name of venue',
        venueEngName: 'English name of venue',
        venueNumber: 'Venue number',
        venueCategory: 'Venue category',
        venueProperty: 'Venue characteristics',
        venueManagerUsers: 'Venue manager',
        capacity: 'Number of people that can be accommodated',
        venueLocation: 'Venue location',
        whetherToEnable: 'Do you wish to enable',
        lockid: 'Door access ID',
      },
    },
  },

  timeSetting: {
    title: '{name} Time Settings',
    action: {
      openAppointment: 'Booking Open',
      closeAppointment: 'Booking Not Open',
      fixVenue: 'Fixed Venue',
    },
    tableList: {
      columns: {
        period: 'Time period',
        enable: 'Enable time period',
        date: 'Date',
        monday: 'Monday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
        sunday: 'Sunday',
      },
      cell: {
        fixedVenueName: 'Name of fixed venue:',
      },
    },
    hint: {
      timePeriodsRequired: 'Please first select time period',
      intro: {
        symbolDesc: 'Description of symbol:',
        openTip: 'Green tick, shows that the present venue is open for booking at the corresponding time period',
        closeTip: 'Grey cross, shows that the present venue is not open at the corresponding time period',
        lockTip: `Lock, shows that the venue during the corresponding time period is a fixed venue
(used for teaching and activities), booking interface will automatically show status
that venue cannot be booked and indicate that venue is fixed.`,
      },
    },
    modalOpen: {
      title: 'Settings for open for booking',
      content: 'Do you confirm that the selected time periods are open for booking?',
    },
    modalClose: {
      title: 'Settings for not open for booking',
      content: 'Do you confirm that the selected time periods are not open for booking?',
    },
    modalLock: {
      title: 'Fixed Venue Settings',
      content: 'All selected time periods are fixed venues not open for booking?',
      form: {
        label: {
          fixedVenueName: 'Name of fixed venue',
        },
        placeholder: {
          fixedVenueName: 'Please key in not more than 20 words',
        },
      },
    },
  },
};

export default venueTimeSetting;
