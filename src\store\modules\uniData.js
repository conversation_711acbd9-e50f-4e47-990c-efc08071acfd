import {
  getTreeData,
  getDepartmentData,
  postCreateDep,
  deleteDep,
  getUserlist,
  registryUser,
  getRoleList,
  postCreateRole,
  getRoleDetail,
  deleteRole,
  getUserPostList,
  getPostList,
  getPostDetail,
  postCreatePost,
  requestDeletePost,
  requestRemoveMultipleUser,
  updateGroupUserSetHeader,
  getDepUserList,
  postDistribute,
  getUserInfo,
  requesSearchUser,
  removeUserByPost,
  postSinglePostUser,
  getAppList,
  getModules,
  getRolePostList,
  avatarUploader,
  removeDepUser,
  deletePostUsers,
  resetUserPassword,
} from '@api/unidata-api';
import {
  getAuthGroupUserList,
  setPostAuthGroupUsers,
  removeUserByAuthGroup,
} from '@api/permission-api';
import { generateTreeList, getTree, getTreeNodeById } from '@utils/select-person';

const initialState = {
  totalSize: 0,
  // 部门详情
  departmentDetail: null,
  dataList: [],
  // 部门列表
  origDepList: [],
  depList: [],
  // 对应部门下的用户
  depUsers: [],
  // 部门下用户
  depUserList: [],
  depDataList: [],
  // 用户列表
  userList: [],
  isloadUserList: true,
  // isloadDetail: false,
  roleList: [],
  rolePostList: [],
  roleDetail: {},
  // 角色的岗位列表
  postUserList: [],
  postList: [],
  postDetail: {},
  postTree: [],
  // 应用树
  appList: [],
  appDataList: [],
  // 模块操作
  modulesAndOpreate: [],
  // 全选模块选择
  permissionModule: {},
  // 分配人员列表
  userAndGroupList: [],
  DepTree: [],
  hasUserAndGroupList: false,
  hasUsersInUserAndGroupList: false,
  // 删除岗位下用户状态
  isRemovingPostUsers: false,
  authGroupUserList: [],
};
const getters = {
  getPermission($state) {
    return $state.permissionModule;
  },
};
const actions = {
  fetchTreeData({ commit }, { treeApi = '' }) {
    return new Promise((resolve, reject) => {
      const param = {};
      getTreeData(treeApi, param)
        .then((r) => {
          commit('setDepDataList', r.model);
          commit('setOrigDepList', r.model);
          const tree = generateTreeList(r.model);
          commit('setDepList', tree);
          resolve(tree);
        })
        .catch((e) => reject(e));
    });
  },
  fetchDepartmentData({ commit }, id) {
    return new Promise((resolve, reject) => {
      const params = { id };
      getDepartmentData(params)
        .then((r) => {
          commit('setDepartmentData', r.model);
          resolve();
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // 删除部门下用户
  // eslint-disable-next-line no-unused-vars
  removeUserByDep({ commit }, param) {
    return new Promise((resolve, reject) => {
      removeDepUser(param)
        .then(() => {
          resolve();
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  fetchDepUserList({ commit }, id) {
    return new Promise((resolve, reject) => {
      getDepUserList({ id }).then((r) => {
        commit('setDepUsers', r.model);
        resolve();
      }).catch((e) => { reject(e); });
    });
  },
  fetchDepUser({ commit, state }, { nodeData }) {
    return new Promise((resolve, reject) => {
      if (nodeData.length <= 0) return;
      const { id } = nodeData;
      const param = { id };
      getDepUserList(param)
        .then((r) => {
          const result = r.model.map((t) => {
            const item = t;
            item.title = item.nickname;
            item.key = `user-${id}-${item.id}`;
            item.isUser = true;
            item.isLeaf = true;
            item.scopedSlots = { title: 'title' };
            item.slots = { title: 'custom' };
            return item;
          });
          commit('setDepDataList', state.depDataList.concat(result));
          resolve(result);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  fetchUserInfo({ commit }, id) {
    return new Promise((resolve, reject) => {
      const params = { id };
      getUserInfo(params)
        .then((r) => {
          resolve(r.data.model);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  fetchRoleData({ commit }, id) {
    return new Promise((resolve, reject) => {
      const params = { id };
      getRoleDetail(params)
        .then((r) => {
          commit('setRoleDetail', r.data);
          resolve();
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  createDepartment({ commit }, payload) {
    return new Promise((resolve, reject) => {
      postCreateDep(payload)
        .then((r) => {
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  createRole({ commit }, payload) {
    return new Promise((resolve, reject) => {
      postCreateRole(payload, payload.update)
        .then((r) => {
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updateDepartment({ commit }, payload) {
    return new Promise((resolve, reject) => {
      postCreateDep(payload, payload.update)
        .then((r) => {
          commit('setDepartmentData', r.data.model);
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  deleteDep({ commit }, id) {
    return new Promise((resolve, reject) => {
      deleteDep(id)
        .then((r) => {
          commit('setDepartmentData', {});
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  postDeleteRole({ commit }, id) {
    return new Promise((resolve, reject) => {
      deleteRole(id)
        .then((r) => {
          commit('setRoleDetail', {});
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  fetchRoleList({ commit }, { treeApi = '', treeNode }) {
    return new Promise((resolve, reject) => {
      if (treeNode && treeNode.dataRef.children) {
        resolve();
      }
      const param = {};
      getRoleList(treeApi, param)
        .then((r) => {
          commit('setDataList', r.model);
          const tree = generateTreeList(r.model);
          commit('setRoleList', tree);
          resolve(tree);
        })
        .catch((e) => reject(e));
    });
  },
  fetchRolePostList({ commit }, id) {
    return new Promise((resolve, reject) => {
      getRolePostList(id)
        .then((r) => {
          commit('setRolePostList', r);
          resolve(r);
        })
        .catch((e) => reject(e));
    });
  },
  fetchUserPostList({ commit }, { postId, keyword }) {
    return new Promise((resolve, reject) => {
      getUserPostList(postId, keyword)
        .then((r) => {
          commit('setUserPostList', r.model);
          resolve(r);
        })
        .catch((e) => reject(e));
    });
  },
  // 岗位管理
  // eslint-disable-next-line no-unused-vars
  distriSinglePost({ commit }, payload) {
    return new Promise((resolve, reject) => {
      const param = { payload };
      postSinglePostUser(param)
        .then(() => {
          resolve();
        })
        .catch((e) => reject(e));
    });
  },
  // 获取部门下人员分配所需的部门列表
  fetchAsyncUserList({ state, commit }) {
    return new Promise((resolve, reject) => {
      if (state.hasUserAndGroupList) {
        resolve(state.DepTree);
        return;
      }

      const param = {};
      getTreeData(null, param)
        .then((r) => {
          commit('setDataList', r.model);
          const tree = generateTreeList(r.model, true);
          commit('setUserAndGroupList', tree);
          commit('setDepTree', JSON.parse(JSON.stringify(tree)));
          commit('setHasUserAndGroupList', true);
          resolve(tree);
        })
        .catch((e) => reject(e));
    });
  },
  // 获取所有部门下的用户，需要先获取部门后才能执行此方法
  fetchAllDepUsersIfNeeded({ commit, state }) {
    return new Promise((resolve, reject) => {
      if (state.hasUsersInUserAndGroupList) {
        resolve();
        return;
      }
      if (!Array.isArray(state.dataList) || state.dataList.length === 0) {
        reject(new Error('未加载部门数据'));
      }

      const promises = state.dataList.map(({ id }) => {
        const params = { id };
        return getDepUserList(params).then(({ model }) => {
          if (Array.isArray(model)) {
            const users = model.map((t) => {
              const item = t;
              item.title = item.nickname;
              item.key = `user-${id}-${item.id}`;
              item.isUser = true;
              item.isLeaf = true;
              item.scopedSlots = { title: 'title' };
              item.slots = { title: 'custom' };
              return item;
            });
            if (users.length > 0) {
              commit('insertUsersToUserAndGroupList', {
                groupId: id,
                users,
              });
            }
            resolve(users);
          } else {
            resolve([]);
          }
        }).catch((err) => {
          reject(err);
        });
      });
      Promise.all(promises).then(() => {
        commit('setHasUsersInUserAndGroupList', true);
        resolve();
      }).catch((err) => {
        reject(err);
      });
    });
  },
  // 删除岗位下用户
  // eslint-disable-next-line no-unused-vars
  removePostUser({ commit }, payload) {
    // console.log('removePostUser');
    return new Promise((resolve, reject) => {
      const param = { payload };
      removeUserByPost(param)
        .then(() => {
          resolve();
        })
        .catch((e) => reject(e));
    });
  },
  // 删除岗位下用户
  removePostUsers({ commit }, { groupId, userIdList }) {
    return new Promise((resolve, reject) => {
      commit('setIsRemovingPostUsers', true);
      deletePostUsers(groupId, userIdList).then(() => {
        commit('setIsRemovingPostUsers', false);
        resolve();
      }).catch((err) => {
        commit('setIsRemovingPostUsers', false);
        reject(err);
      });
    });
  },

  fetchPosList({ commit }) {
    return new Promise((resolve, reject) => {
      const param = {};
      getPostList(param)
        .then((r) => {
          commit('setPostList', r.model);
          const tree = generateTreeList(r.model);
          commit('setPostTree', tree);
          resolve(tree);
        })
        .catch((e) => reject(e));
    });
  },
  fetchPostData({ commit }, id) {
    return new Promise((resolve, reject) => {
      const params = { id };
      getPostDetail(params)
        .then((r) => {
          commit('setPostDetail', r.data);
          resolve();
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  createPost({ commit }, payload) {
    return new Promise((resolve, reject) => {
      postCreatePost(payload, payload.update)
        .then((r) => {
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  deletePost({ commit }, id) {
    return new Promise((resolve, reject) => {
      requestDeletePost(id)
        .then((r) => {
          commit('setRoleDetail', {});
          resolve(r);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  distribution({ commit }, payload) {
    return new Promise((resolve, reject) => {
      postDistribute(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e));
    });
  },
  // 用户管理
  // eslint-disable-next-line no-unused-vars
  removeUser({ commit }, id) {
    return new Promise((resolve, reject) => {
      requestRemoveMultipleUser([id])
        .then((r) => {
          if (r && r.data && r.data.success) {
            resolve(r);
          } else if (r && r.data && r.data.errorMsg) {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject({ message: r.data.errorMsg });
          } else {
            reject();
          }
        })
        .catch((e) => reject(e));
    });
  },
  // 用户管理
  // eslint-disable-next-line no-unused-vars
  removeMultipleUser({ commit }, userIdList) {
    return new Promise((resolve, reject) => {
      requestRemoveMultipleUser(userIdList)
        .then((r) => {
          if (r && r.data && r.data.success) {
            resolve(r);
          } else if (r && r.data && r.data.errorMsg) {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject({ message: r.data.errorMsg });
          } else {
            reject();
          }
        })
        .catch((e) => reject(e));
    });
  },
  // 设置用户为主管
  // eslint-disable-next-line no-unused-vars
  setUserDirectorType({ commit }, { groupId, userIdList, directorType }) {
    return new Promise((resolve, reject) => {
      updateGroupUserSetHeader({ groupId, userIdList, directorType })
        .then((r) => {
          if (r && r.data && r.data.success) {
            resolve(r);
          } else if (r && r.data && r.data.errorMsg) {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject({ message: r.data.errorMsg });
          } else {
            reject();
          }
        })
        .catch((e) => reject(e));
    });
  },
  // 获取用户列表
  fetchUserList({ commit }, param = {}) {
    return new Promise((resolve, reject) => {
      commit('setLoadedUserList', true);
      getUserlist(param)
        .then((r) => {
          commit('setUserList', r.data);
          commit('setUserPostList', r.data);
          commit('setLoadedUserList', false);
          commit('setTotalSize', r.total);
          resolve(r);
        })
        .catch((e) => reject(e));
    });
  },
  // 添加用户
  // eslint-disable-next-line no-unused-vars
  postRegistryUser({ commit }, form) {
    return new Promise((resolve, reject) => {
      registryUser(form, form.update)
        .then((resp) => {
          resolve(resp);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  // eslint-disable-next-line no-unused-vars
  searchUser({ commit }, keywords) {
    return new Promise((resolve, reject) => {
      requesSearchUser(keywords)
        .then((r) => {
          const result = r.data.map((t) => {
            const item = t;
            item.title = item.nickname;
            item.key = `user-${item.id}`;
            item.isUser = true;
            item.isLeaf = true;
            item.scopedSlots = { title: 'title' };
            item.slots = { title: 'custom' };
            return item;
          });
          resolve(result);
        })
        .catch((e) => reject(e));
    });
  },
  // 重置用户密码
  // eslint-disable-next-line no-unused-vars
  resetUserPassword({ commit }, payload) {
    const { userId } = payload;
    return new Promise((resolve, reject) => {
      resetUserPassword({ userId })
        .then((resp) => {
          resolve(resp);
        })
        .catch((err) => reject(err));
    });
  },
  // 权限管理
  fetchAppList({ commit }) {
    return new Promise((resolve, reject) => {
      getAppList()
        .then((r) => {
          commit('setAppDataList', r);
          const tree = generateTreeList(r.model);
          const firsNode = tree[0];
          commit('setAppList', tree);
          resolve(firsNode);
        })
        .catch((e) => reject(e));
    });
  },
  fetchModulesAndOpreate({ commit }, id) {
    return new Promise((resolve, reject) => {
      getModules(id)
        .then((r) => {
          commit('setModulesAndOpreate', r);
          resolve(r);
        })
        .catch((e) => reject(e));
    });
  },
  // eslint-disable-next-line no-unused-vars
  uploadAvatar({ commit }, payload = {}) {
    return new Promise((resolve, reject) => {
      avatarUploader(payload)
        .then((r) => {
          if (r.success) {
            resolve(r.model);
          } else {
            reject(r.errorMsg);
          }
        })
        .catch((e) => reject(e));
    });
  },

  // eslint-disable-next-line no-unused-vars
  setPostAuthGroupUsersInfo({ commit }, { form, id }) {
    return new Promise((resolve, reject) => {
      const param = { form, id };
      setPostAuthGroupUsers(param)
        .then(() => {
          resolve();
        })
        .catch((e) => reject(e));
    });
  },

  // 获取权限组用户列表
  fetchAuthGroupUserList({ commit }, param = {}) {
    return new Promise((resolve, reject) => {
      commit('setLoadedUserList', true);
      getAuthGroupUserList(param)
        .then((r) => {
          const list = [];
          r.data.forEach((rs) => {
            const item = rs;
            item.id = rs.userId;
            list.push(item);
          });
          commit('setAuthGroupUserList', list);
          commit('setLoadedUserList', false);
          commit('setTotalSize', r.total);
          resolve(r);
        })
        .catch((e) => reject(e));
    });
  },
  // 删除岗位下用户
  // eslint-disable-next-line no-unused-vars
  removeAuthGroupUser({ commit }, { userIdList, groupId }) {
    return new Promise((resolve, reject) => {
      removeUserByAuthGroup(userIdList, groupId)
        .then(() => {
          resolve();
        })
        .catch((e) => reject(e));
    });
  },
  // 删除岗位下用户
  removeAuthGroupUsers({ commit }, { userIdList, groupId }) {
    return new Promise((resolve, reject) => {
      commit('setIsRemovingPostUsers', true);
      removeUserByAuthGroup(userIdList, groupId).then(() => {
        commit('setIsRemovingPostUsers', false);
        resolve();
      }).catch((err) => {
        commit('setIsRemovingPostUsers', false);
        reject(err);
      });
    });
  },
};
const mutations = {
  setTotalSize(state, num) {
    state.totalSize = num;
  },
  setUserAndGroupList(state, payload) {
    state.userAndGroupList = payload;
  },
  insertUsersToUserAndGroupList(state, payload) {
    const { groupId, users } = payload;
    const groupNode = getTreeNodeById(groupId, state.userAndGroupList);
    if (groupNode) {
      if (Array.isArray(groupNode.children)) {
        Object.assign(groupNode, {
          isLeaf: false,
          children: [
            ...groupNode.children,
            ...users,
          ],
        });
      } else {
        Object.assign(groupNode, {
          isLeaf: false,
          children: [
            ...users,
          ],
        });
      }
    }
  },
  setHasUserAndGroupList(state, payload) {
    state.hasUserAndGroupList = payload;
  },
  setHasUsersInUserAndGroupList(state, payload) {
    state.hasUsersInUserAndGroupList = payload;
  },
  setDepTree(state, payload) {
    const data = getTree(payload);
    state.DepTree = data;
  },
  setpermissionModule(state, payload) {
    state.permissionModule = payload;
  },
  setModulesAndOpreate(state, payload) {
    const r = payload.map((o) => {
      const item = o;
      item.key = item.id;
      if (item.modules && item.modules.length > 0) {
        item.modules = item.modules.map(($e) => {
          const $o = $e;
          $o.moduleOperatings = $o.moduleOperatings.map(($t) => {
            const t = $t;
            t.label = t.name;
            t.value = t.id;
            return t;
          });
          return $o;
        });
      }
      return item;
    });
    state.modulesAndOpreate = r;
  },
  setAppDataList(state, payload) {
    state.appDataList = payload;
  },
  setAppList(state, payload) {
    state.appList = payload;
  },
  setDataList(state, payload) {
    state.dataList = payload;
  },
  setPostList(state, payload) {
    state.postList = payload;
  },
  setPostTree(state, payload) {
    state.postTree = payload;
  },
  setDepUsers(state, payload) {
    state.depUsers = payload;
  },
  setPostDetail(state, payload) {
    state.postDetail = payload.model;
  },
  setUserPostList(state, payload) {
    state.postUserList = payload;
  },
  setDepDataList(state, payload) {
    state.depDataList = payload;
  },
  setOrigDepList(state, payload) {
    state.origDepList = payload;
  },
  setDepList(state, payload) {
    state.depList = payload;
  },
  setRolePostList(state, payload) {
    state.rolePostList = payload;
  },
  setRoleList(state, payload) {
    state.roleList = payload;
  },
  setTreeList(state, tree) {
    state.treeList = tree;
  },
  setLoadedUserList(state, status) {
    state.isloadUserList = status;
  },
  setUserList(state, payload) {
    state.userList = payload;
  },
  setDepartmentData(state, payload) {
    state.departmentDetail = payload;
  },
  setRoleDetail(state, payload) {
    state.roleDetail = payload;
  },
  setIsRemovingPostUsers(state, payload) {
    state.isRemovingPostUsers = payload;
  },
  setAuthGroupUserList(state, payload) {
    state.authGroupUserList = payload;
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  actions,
  mutations,
};
