export default {
  title: {
    add: 'Add New Blacklisted People',
    set: 'Settings',
    setting: '黑名单机制设置',
    editSetting: '修改黑名单机制',
  },
  form: {
    keywordsPlace: 'Name or work ID of blacklisted people',
    userId: 'Name',
    reason: 'Reasons for addition into blacklist',
    isAutoBlacklist: 'Do you wish to enable automatic addition into blacklist',
    isCancelBlacklist: 'Do you wish to enable removal of blacklist',
    rule: 'Settings rules',
    usefulType: '适用类型',
    isAutoAdd: '是否自动加入黑名单',
    autoAddType: '自动加入黑名单规则',
    isAutoRemove: '是否自动移出黑名单',
    reservationType: '不可预约类型',
  },
  place: {
    reason: 'Please limit remarks to within 200 words',
    reasonPlace: 'Please fill in reasons for addition into blacklist',
    userId: 'Please select people',
    systemMonth: 'Please key in number of natural months',
    systemCount: 'Please key in number of no-shows',
    systemDay: 'Please key in number of days',
    isAutoAdd: '请选择是否自动加入黑名单',
    autoAddType: '请选择自动加入黑名单规则',
    isAutoRemove: '请选择是否自动移出黑名单',
    reservationType: '请选择不可预约类型',
  },
  action: {
    add: 'Add',
    setting: 'Blacklist Settings',
  },
  msg: {
    delTip: 'Do you confirm you wish to move this person out of the blacklist?',
    delSucc: 'Operation is successful!',
    saveSucc: 'Successfully saved!',
    editSucc: 'Successfully edited!',
    autoTip: [
      'Total number of no-shows in natural month',
      ' times, there will be automatic addition into blacklist ',
    ],
    cancelTip: [
      'Automatic addition into blacklist, after',
      ` days, move out of blacklist (count starts from the day of addition into blacklist,
manual addition is ineffective)`,
    ],
    setTip: `Example: automatic removal 2 days after automatic addition,
addition into blacklist on 13 September at 15:00, removal from blacklist on 15 September 0:00`,
    autoAddMsg: [
      '个自然月累计爽约',
      '次，则自动加入黑名单。',
      '累计爽约',
      '次，则自动加入黑名单。',
    ],
  },
  columns: {
    userName: 'Name',
    userNo: 'Student ID/work ID',
    reason: 'Reasons for addition into blacklist',
    createTime: 'Time of addition into blacklist',
    createUserName: 'Operator',
    operation: 'Operation',
    usefulType: '适用类型',
    blackName: '加入黑名单机制',
    removeName: '移出黑名单机制',
    lastModName: '最后操作人/最后操作时间',
    reservationType: '不可预约类型',
    isAutoRemove: '是否自动移出',
  },
  isAutoRemove: {
    yes: '自动加入黑名单，{days}天后，移出黑名单',
    no: '不自动移出',
  },
  autoAddType: {
    month: '{mon}个自然月累计爽约{count}次，则自动加入黑名单',
    stat: '累计爽约{count}次，则自动加入黑名单',
    no: '不自动加入黑名单',
  },
};
