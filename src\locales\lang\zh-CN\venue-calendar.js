const venueCalendar = {
  title: '场馆日历',
  simpleSearch: {
    form: {
      label: {
        appointmentDate: '预约日期',
        venueCate: '场馆类别',
      },
      selectPlaceHolder: '请选择',
    },
  },
  tableList: {
    columns: {
      period: '时段',
      venue: '场馆',
    },

    text: {
      integrals: '{integrals}积分',
    },
  },

  modalReserve: {
    title: '预约信息',
    titleView: '预约详情',
    form: {
      label: {
        venueNameTime: '预约场馆时间',
        venueName: '预约场馆',
        reserveTime: '预约时间',
        appointerName: '预约人姓名',
        appointerNumber: '预约人工号',
        department: '所属部门',
        departmentCollege: '所属部门/学院',
        contact: '联系电话',
        activityName: '活动名称',
        applicationInstructions: '申请说明',

        sn: '申请单号',
        createTime: '申请时间',
        // venueName: '预约场馆',
        reservationDate: '预约时间',
        userName: '预约人',
        userPhone: '联系电话',
        integralUsed: '使用积分',
        createUserName: '操作人',

        signinTime: '签到时间',
        cancelTime: '取消时间',
        remark: '备注',
        enterPlace: '请输入',
      },
    },
    text: {
      integrals: '@:venueCalendar.tableList.text.integrals',
    },
    action: {
      submit: '提交预约',
    },
    authError: '您暂没有预约权限，若有疑问可与管理员联系',
    hint: {
      notEnoughIntegral: '积分不足，预约失败',
    },
  },
  verifySearchPlace: '请在此输入6位数的签到核销码 或 使用扫码枪扫描签到核销二维码',
  verifyTableTips: '可点击有内容的区域查看预约详情。',
  verifyNoDataTip: '没有找到匹配的预约记录，请核实后重新搜索',
  status: {
    pending: '待签到',
    signed: '已签到',
    invalid: '已失效',
  },
  msg: {
    reserveError: '请选择同一场馆的连续时段',
    reserveTimeError: '请选择连续时段',
    noTimeError: '请先选择预约时段',
  },
  btn: {
    reserve: '预约',
  },
};

export default venueCalendar;
