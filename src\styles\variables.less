@inputBorderColor: #EFEFEF;
@defaultBorderColor: #EFEFEF;
@inputBorderColorHover: #9B0000;
@btnPrimaryBg: #9B0000;
@btnPrimaryCorlor: #fff;
@btnBgOverlayHover: rgba(255,255,255,1);
@btnBgOverlayActive: rgba(0,0,0,1);
@titleColor: rgba(0, 0, 0, 0.85);
@textColor: rgba(0, 0, 0, 0.65);
@dangerColor: #D63C3C;
@link-hsvhue: 218;
@link-hsvsaturation: 86%;
@link-hsvvalue: 88%;
@link-hover: 12%;
@link-active: -12%;
@mainColor: #9B0000;
@assistColor: #FAF2F2;
@switchDeaultColor: #B3B3B3;

@headerHeight: 40px;
@pageHeaderHeight: 50px;
@sideBarWidth: 240px;
@commonTitleFontSize: 16px;
@btnHeight: 32px;
@smallBtnHeight: 26px;
@bigBtnHeight: 38px;
@smallBtnWidth: 58px;
@btnWidth: 74px;
@bigBtnWidth: 96px;
@btnFontSize: 14px;
@formLabelFontSize: 14px;
@formLabelLineHeight: 32px;

@topTreeNodeHeight: 40px;
@treeNodeHeight: 34px;
@treeNodeBg: #f9f9f9;
@treeNodeSelectedBg: #f5f5f5;
@treeNodeSelectedBorderBg: #9B0000;

@grayBg: #FCFCFC;
