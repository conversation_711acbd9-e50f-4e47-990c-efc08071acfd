export default {
  title: {
    title: '使用说明',
    add: '新增使用说明',
    addTitle: '新增使用说明',
    editTitle: '编辑使用说明',
    publishAnnounce: '公告管理',
    publishAnnounceEdit: '修改公告',
    announceInfo: '公告信息',
  },
  columns: {
    title: '使用说明标题',
    sortOrder: '排序号',
    status: '是否发布',
    updateTime: '操作人/最后操作时间',
    operate: '操作',
  },
  form: {
    title: '标题',
    titleTip: '请输入标题',
    enTitle: '标题英文',
    content: '正文中文',
    contentTip: '请输入正文中文',
    enContent: '正文英文',
    attachmentIds: '附件',
    attachmentIdsTip: '仅支持xls、xlsx、pdf、doc、docx格式文件',
    sortOrder: '排序号',
    sortOrderPlace: '请输入数字，数字越小越靠前',
    sortOrderTip: '请输入排序号',
    isEnable: '是否发布',
    keywordPlace: '使用说明标题',
    isNumber: '只允许输入数字',
  },
  action: {
    view: '查看',
    cancel: '取消',
    edit: '编辑',
    submit: '提交',
    save: '保存',
    change: '修改',
  },
  msg: {
    success: '操作成功',
    submitSuccess: '提交成功',
    cancelSuccess: '取消成功',
    confirmCancel: '确定取消报修申请吗？',
    assignSucc: '指派成功',
    handleSucc: '处理成功',
    saveSucc: '保存成功',
    delSucc: '删除成功',
    imgType: '只允许上传 {types} 类型图片',
    delTip: '确认是否删除？',
  },
  status: {
    TRUE: '启用',
    FALSE: '停用',
  },
  isTrueStatus: {
    TRUE: '是',
    FALSE: '否',
  },
  publishAnnounce: {
    msg: {
      success: '操作成功',
      submitSuccess: '保存成功',
      cancelSuccess: '取消成功',
      confirmCancel: '确定取消报修申请吗？',
      assignSucc: '指派成功',
      handleSucc: '处理成功',
      saveSucc: '保存成功',
      delSucc: '删除成功',
      imgType: '只允许上传 {types} 类型图片',
      delTip: '确认是否删除？',
    },
    form: {
      title: '中文标题',
      enTitle: '英文标题',
      titleTip: '请输入标题',
      content: '中文内容',
      enContent: '英文内容',
      contentTip: '请输入中文内容',
      isEnable: '是否发布',
      publishTime: '发布时间',
      publishTimeNotice: '(若未设置保存后即刻生效）',
      expirationTime: '公告到期时间',
      expirationTimeNotice: '(若未设置则会一直生效；若超过设置的时间后，公告失效，用户界面将不可见公告信息）',
      lastOperator: '最后修改人',
      lastOperateTime: '最后修改时间',
      effect: '用户可见',
      unEffect: '用户不可见',
    },
  },
  enabLStatus: {
    TRUE: '发布',
    FALSE: '未发布',
  },
};
