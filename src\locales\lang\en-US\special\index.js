export default {
  title: {
    add: 'Add Special Date',
    edit: 'Edit Special Date',
  },
  form: {
    keywordsPlace: 'Name of date',
    name: 'Name of date',
    enName: 'English name of date',
    startDate: 'Start time',
    endDate: 'End time',
    remark: 'Remarks',
    isEnable: 'Do you wish to enable',
    venueIds: 'Appropriate venue',
    type: 'Type of date',
  },
  place: {
    name: 'Please enter name of date ',
    enName: 'Please enter English name of date',
    startDate: 'Please select start time',
    endDate: 'Please select end time',
    type: 'Please select type of date',
    venueIds: 'Please select appropriate venue',
  },
  action: {
    add: 'Add',
  },
  msg: {
    delTip: 'Do you confirm you wish to cancel?',
    delSucc: 'Successfully deleted!',
    saveSucc: 'Successfully saved!',
    editSucc: 'Successfully edited!',
  },
  columns: {
    name: 'Name of date',
    type: 'Type',
    date: 'Date',
    venueNames: 'Appropriate venue',
    updateUserName: 'Operator/Operating time',
    isEnable: 'Status',
    operation: 'Operation',
  },
  enable: {
    all: 'All',
    true: 'Enable',
    false: 'Prohibit',
  },
  type: {
    reservation_unavailable: 'Booking is unavailable',
    venue_unavailable: 'Venue cannot be booked',
  },
};
