<template>
  <div class="">
    <a-cascader
      class="pima-cascader"
      :options="monthDayOptions"
      :field-names="{ label: 'label', value: 'value', children: 'children' }"
      :display-render="displayRender"
      :placeholder="t('placeholder.date')"
      v-bind="$attrs"
      v-on="$listeners"
      @change="$emit('input', $event)"
    />
  </div>
</template>

<script>
import moment from 'moment';
import { namespaceT } from '@/helps/namespace-t';

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      monthDayOptions: [],
      t: namespaceT('integralManagement'),
    };
  },
  beforeMount() {
    this.monthDayOptions = this.generateMonthDayOptions();
  },
  methods: {
    generateMonthDayOptions() {
      const options = [];
      // eslint-disable-next-line no-plusplus
      for (let m = 1; m <= 12; m++) {
        const month = m;
        const monthLabel = this.t('text.months', { month });
        const totalDays = moment(`2024-${m}`, 'YYYY-M').daysInMonth();
        const children = Array.from({ length: totalDays }, (_, i) => {
          const day = (i + 1);
          return {
            label: this.t('text.days', { day }),
            value: day,
          };
        });

        options.push({
          label: monthLabel,
          value: month,
          children,
        });
      }
      return options;
    },

    displayRender(e) {
      return e.labels.map((item) => item).join('');
    },
  },
};
</script>
