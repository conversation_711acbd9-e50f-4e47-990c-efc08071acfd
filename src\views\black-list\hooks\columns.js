
import { namespaceT } from '@/helps/namespace-t';
// 配置table columns
export function getColumns() {
  const t = namespaceT('blacklist.columns');

  return [
    {
      title: t('userName'),
      dataIndex: 'userName',
      align: 'left',
    },
    {
      title: t('userNo'),
      dataIndex: 'userNo',
      align: 'left',
    },
    {
      title: t('reservationType'),
      dataIndex: 'reservationType',
      scopedSlots: { customRender: 'reservationType' },
      align: 'left',
    },

    {
      title: t('removeReason'),
      // TODO:待修改
      dataIndex: 'removeReason',
      align: 'left',
    },
    {
      title: t('removeCreateTime'),
      // TODO:待修改
      dataIndex: 'removeTime',
      scopedSlots: { customRender: 'time' },
      align: 'left',
    },
    {
      title: t('createUserName'),
      dataIndex: 'createUserName',
      scopedSlots: { customRender: 'createUserName' },
      align: 'left',
    },
  ];
}
