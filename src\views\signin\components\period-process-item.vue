<template>
  <div class="status-container">
    <div
      v-if="currentVenue && currentPeriod"
      class="status"
      :class="className"
      @click="onClickReserve"
    >
      <div class="status-wrap">
        <!-- <template v-if="PeriodProcessStatus.RESERVED === currentPeriod.status">
          01:{{ currentPeriod.status }}
          <span>{{ currentPeriod.reservationUserName }}</span>
          <br>
          <span v-if="OrderTypeMap.ACTIVITY === currentVenue.reservationType"> {{ currentPeriod.activityName }}</span>
          <template v-if="currentPeriod.fixedVenueName">
            <br><span>{{ currentPeriod.fixedVenueName }}</span>
          </template>
        </template> -->
        <template v-if="currentPeriod.status">
          <template v-if="currentPeriod.verifyReservationVo">
            <template v-if="currentPeriod.verifyReservationVo.activityName">
              <span style="font-size: 14px;">{{ currentPeriod.verifyReservationVo.activityName }}</span>
            </template>
            <span style="font-size: 12px;">{{ currentPeriod.verifyReservationVo.userName }}</span>
          </template>
          <!-- <span class="btn-reserve">
            {{ currentPeriod.status | periodProcessStatusI18n }}
          </span> -->

          <template v-if="payload">
            <ModalReserve
              v-model="isShowModalReserve"
              :payload="payload"
            />
          </template>
        </template>
        <template v-else>
          {{ currentPeriod.status | periodProcessStatusI18n }}
        </template>
      </div>
      <div
        :class="{'selected-icon': clickActive}"
      />
    </div>
    <div
      v-else
      class="empty-holder"
    />
  </div>
</template>


<script>
// import moment from 'moment';
import { periodProcessStatusI18n } from '@/filters/venue';
import { PeriodProcessStatus, OrderTypeMap, ReserveStatus } from '@/constants/venue';
import ModalReserve from './modal-reserve.vue';

export default {
  components: {
    ModalReserve,
  },

  filters: {
    periodProcessStatusI18n,
  },

  inject: ['canReserve'],

  props: {
    value: {
      type: Object,
      default() {
        return null;
      },
    },

    rowIndex: {
      type: Number,
      default: 0,
    },

    columnIndex: {
      type: Number,
      default: 0,
    },

    venues: {
      type: Array,
      default() {
        return [];
      },
    },

    selectedDate: {
      type: Date,
      default() {
        return null;
      },
    },
  },

  data() {
    return {
      OrderTypeMap,
      PeriodProcessStatus,
      ReserveStatus,
      isShowModalReserve: false,
      clickActive: false,
    };
  },

  computed: {
    currentVenue() {
      const idx = Math.max(0, this.columnIndex - 1);
      return this.venues[idx];
    },

    currentPeriod() {
      if (this.currentVenue && Array.isArray(this.currentVenue.venueCalendarVenueTimeCellVoList)) {
        let currPeriod = null;
        this.currentVenue.venueCalendarVenueTimeCellVoList.forEach((period) => {
          if (period.startTime === this.value.startTime && period.endTime === this.value.endTime) {
            currPeriod = period;
          }
        });
        return currPeriod;
      }

      return null;
    },

    className() {
      const map = new Map([
        // [PeriodProcessStatus.FIXED_VENUE, 'status-fixed-venue'],
        // [PeriodProcessStatus.AVAILABLE, 'status-available'],
        // [PeriodProcessStatus.RESERVED, 'status-reserved'],
        // [PeriodProcessStatus.EXPIRED, 'status-expired'],
        // [PeriodProcessStatus.CLOSED, 'status-closed'],
        [ReserveStatus.PEDNING_SIGN, 'status-available'],
        [ReserveStatus.SIGNED, 'status-signed'],
        [ReserveStatus.CANCEL, 'status-closed'],
      ]);
      let approvalStatus = '';
      if (this.currentPeriod
        && this.currentPeriod.verifyReservationVo
      ) {
        approvalStatus = this.currentPeriod.verifyReservationVo.approvalStatus;
      }
      return map.get(approvalStatus);
    },

    payload() {
      return this.currentPeriod.verifyReservationVo;
      // return {
      //   venueId: this.currentVenue.venueId,
      //   venueChnName: this.currentVenue.name,
      //   venueEngName: this.currentVenue.enName,
      //   reservationType: this.currentVenue.reservationType,
      //   selectedDate: moment(this.selectedDate).format('yyyy-MM-DD'),
      //   startTime: this.currentPeriod.startTime,
      //   endTime: this.currentPeriod.endTime,
      //   venueReservationTimeId: this.currentPeriod.venueReservationTimeId,
      //   ruleTimeId: this.currentPeriod.reservationRuleTimeId,
      //   status: this.currentPeriod.status,

      //   approvalStatus: this.currentPeriod.verifyReservationVo.approvalStatus,
      //   sn: this.currentPeriod.verifyReservationVo.sn,
      //   createTime: this.currentPeriod.verifyReservationVo.createTime,
      //   venueName: this.currentPeriod.verifyReservationVo.venueName,
      //   reservationDate: this.currentPeriod.verifyReservationVo.reservationDate,
      //   startTime: this.currentPeriod.verifyReservationVo.reservationDate,
      //   reservationDate: this.currentPeriod.verifyReservationVo.reservationDate,
      //   userName: this.currentPeriod.verifyReservationVo.userName,
      //   userPhone: this.currentPeriod.verifyReservationVo.userPhone,
      // };
    },
  },

  methods: {
    onClickReserve() {
      if (this.canReserve()) {
        this.isShowModalReserve = true;
        this.clickActive = true;
        setTimeout(() => {
          this.clickActive = false;
        }, 500);
      } else {
        this.$message.error(this.$t('sign.authError'));
      }
    },
  },
};
</script>


<style lang="less">
strong { font-weight: bold;}
</style>
<style lang="less" scoped>
.status-container {
  width: 100%;
  height: 100%;
  border: 2px solid #fff;
}
.status {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  color: fadeout(#000, 25%);
  font-size: 14px;
  font-weight: 300;
  .selected-icon {
    visibility: visible;
  }

  &.status-available {
    background: #FFF9EF;
    color: #F49B00;
    font-weight: 400;
    cursor: pointer;

    &:hover {
      border: 1px solid #8D0306;
      .selected-icon {
        visibility: visible;
      }
    }
  }

  &.status-signed {
    background: fadeout(#179E00, 90%);;
    color: #4fad4e;
    font-weight: 400;
    cursor: pointer;
    &:hover {
      border: 1px solid #8D0306;
      .selected-icon {
        visibility: visible;
      }
    }
  }

  &.status-closed {
    background: #F2F2F2;
    color: rgba(0,0,0, 0.65);
    font-weight: 400;
    cursor: pointer;
    &:hover {
      border: 1px solid #8D0306;
      .selected-icon {
        visibility: visible;
      }
    }
  }

  .status-wrap {
    display: -webkit-box;
    width: 100%;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    box-orient: vertical;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    span {
      display: -webkit-box;
      width: 100%;
      overflow: hidden;
      text-align: center;
      text-overflow: ellipsis;
      box-orient: vertical;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
}

.btn-reserve {
  color: #4fad4e;
}

.empty-holder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // background: url("~@/assets/img/empty-cell.png") repeat center center / auto auto;
}
</style>
