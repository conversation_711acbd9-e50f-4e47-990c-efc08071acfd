import Cookies from 'js-cookie';
import {
  clearCookies,
  downloadUserImportTemplate,
  downloadUserData,
} from '@/api/user-api';
import config from '@/config';

// initial state
const initialState = {
  avatarUrl: '',
  mobile: null,
  loadingPermission: false,
  menu: [],
  operations: [],
  sidebarMenuItems: [],
  loadingProfile: true,
  userModel: {
    mobile: '',
    userId: '',
  },
};

// getters
const getters = {
  hasOperation: (state) => (operation) => state.operations.indexOf(operation) !== -1,
};

// mutations
const mutations = {
  setOperations(state, payload) {
    state.operations = payload;
  },
  setSidebarMenuItems(state, payload) {
    state.sidebarMenuItems = payload;
  },
  setLoadingProfile(state, payload) {
    state.loadingProfile = payload;
  },
  setProfile(state, { mobile, avatarUrl }) {
    state.mobile = mobile;
    state.avatarUrl = avatarUrl;
  },
  setUserModel(state, payload) {
    state.userModel = payload;
    state.loadingProfile = false;
  },
};

// actions
const actions = {
  clearUserCookies() {
    return new Promise((resolve) => {
      Cookies.remove(config.userTokenCookieName);
      clearCookies().then(() => {
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  },
  // eslint-disable-next-line no-unused-vars
  downloadUserImportTemplate({ state }, { fileName }) {
    return new Promise((resolve) => {
      downloadUserImportTemplate(fileName).then(() => {
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  },
  // eslint-disable-next-line no-unused-vars
  exportUser({ state }, params) {
    return new Promise((resolve) => {
      downloadUserData(params).then(() => {
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  mutations,
  actions,
};
