<template>
  <a-table
    :data-source="dataSource"
    :loading="isLoading"
    :columns="columns"
    :row-key="record => record.id"
    :pagination="pagination"
    class="pima-table"
  >
    <template
      slot="lastModName"
      slot-scope="text, record"
    >
      {{ record.updateUserName }} <br>
      {{ $moment(text).format('YYYY/MM/DD HH:mm') }}
    </template>
    <template
      slot="reservationType"
      slot-scope="text"
    >
      {{ getReservationType(text) }}
    </template>
    <template
      slot="blackName"
      slot-scope="text, record"
    >
      <span v-if="!text">
        {{ $t('blacklist.autoAddType.no') }}
      </span>
      <span v-else-if="record.autoAddType === AUTO_ADD_TYPE.MONTH">
        {{ $t('blacklist.autoAddType.month', {mon: record.months, count:record.offTimes}) }}
      </span>
      <span v-else-if="record.autoAddType === AUTO_ADD_TYPE.STAT">
        {{ $t('blacklist.autoAddType.stat', {count:record.offTimes}) }}
      </span>
    </template>
    <template
      slot="removeName"
      slot-scope="text, record"
    >
      {{ text ? $t('blacklist.isAutoRemove.yes', {days: record.autoRemoveDays}) : $t('blacklist.isAutoRemove.no') }}
    </template>
    <template
      slot="operation"
      slot-scope="text, record"
    >
      <a
        v-if="hasP(P => P.BlackList.SettingEdit)"
        @click="handleEdit(record)"
      >
        {{ $t('action.modify') }}
      </a>
    </template>
  </a-table>
</template>

<script>
import OpreationMixin from '@/mixins/operation';
import { AUTO_ADD_TYPE } from '@/constants/venue';

export default {
  name: 'BlackRuleList',

  mixins: [OpreationMixin],

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: true,
    },
    pageInfo: {
      type: Object,
      default: () => ({
        page: 1,
        limit: 10,
      }),
    },
    totalSize: {
      type: Number,
      default: 0,
    },
    reservationTypeOptions: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      AUTO_ADD_TYPE,
    };
  },

  computed: {
    pagination() {
      const self = this;
      const showQuickJumper = this.totalSize / this.pageInfo.limit > 1;
      if (this.totalSize < 10) {
        return false;
      }
      return {
        showQuickJumper,
        showSizeChanger: true,
        current: self.pageInfo.page,
        defaultPageSize: self.pageInfo.limit,
        total: self.totalSize || 0,
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, limit) {
          self.$emit('pageChange', page, limit);
        },
        showTotal(total) {
          self.total = total;
          const totalPage = Math.ceil(self.totalSize / self.pageInfo.limit);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        onShowSizeChange(cur, size) {
          self.$emit('pageChange', cur, size);
        },
      };
    },
    columns() {
      return [
        {
          title: this.$t('blacklist.columns.usefulType'),
          dataIndex: 'reservationType',
          scopedSlots: { customRender: 'reservationType' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.blackName'),
          dataIndex: 'isAutoAdd',
          scopedSlots: { customRender: 'blackName' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.removeName'),
          dataIndex: 'isAutoRemove',
          scopedSlots: { customRender: 'removeName' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.lastModName'),
          dataIndex: 'updateTime',
          scopedSlots: { customRender: 'lastModName' },
          align: 'left',
        },
        {
          title: this.$t('blacklist.columns.operation'),
          scopedSlots: { customRender: 'operation' },
          align: 'left',
          width: 105,
        },
      ];
    },
  },
  methods: {
    handleEdit(record) {
      this.$emit('edit', record);
    },
    getReservationType(code) {
      const type = this.reservationTypeOptions.filter((item) => item.code === code);
      if (type.length) {
        return type[0].label;
      }
      return '';
    },
  },
};
</script>
