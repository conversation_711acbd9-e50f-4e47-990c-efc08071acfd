<template>
  <a-table
    :data-source="dataSource"
    :loading="isLoading"
    :columns="columns"
    :row-key="record => record.id"
    :pagination="pagination"
    class="pima-table"
    :scroll="{ y: 'calc(100vh - 212px)' }"
  >
    <template
      slot="date"
      slot-scope="text, record"
    >
      {{ $moment(record.startDate).format('YYYY-MM-DD') }} - {{ $moment(record.endDate).format('YYYY-MM-DD') }}
    </template>
    <template
      slot="venueNames"
      slot-scope="text"
    >
      {{ text.join('、') }}
    </template>
    <template
      slot="type"
      slot-scope="text"
    >
      {{ text | typeI18n }}
    </template>
    <template
      slot="date"
      slot-scope="text, record"
    >
      {{ $moment(record.startDate).format('YYYY-MM-DD') }} - {{ $moment(record.endDate).format('YYYY-MM-DD') }}
    </template>
    <template
      slot="operate"
      slot-scope="text, record"
    >
      {{ record.updateUserName }}
      <br>
      {{ $moment(record.updateTime).format('YYYY-MM-DD HH:mm') }}
    </template>
    <template
      slot="isEnable"
      slot-scope="text"
    >
      <span :class="[text ? 'status_success' : 'status_error']">
        {{ text ? $t('special.enable.true') : $t('special.enable.false') }}
      </span>
    </template>
    <template
      slot="operation"
      slot-scope="text, record"
    >
      <a
        v-if="hasP(P => P.SpecialDay.Edit)"
        class="action_link nowrap"
        @click="handleEdit(record)"
      >
        {{ $t('action.edit') }}
      </a>
      <a
        v-if="hasP(P => P.SpecialDay.Del)"
        class="action_link nowrap"
        @click="handleDel(record)"
      >
        {{ $t('action.del') }}
      </a>
    </template>
  </a-table>
</template>

<script>
import OpreationMixin from '@/mixins/operation';
import { delSpecialDayApi } from '@/api/special-day-api';
import { typeI18n } from '@/filters/type';

export default {
  name: 'SpecialDayList',

  filters: {
    typeI18n,
  },

  mixins: [OpreationMixin],

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: true,
    },
    pageInfo: {
      type: Object,
      default: () => ({
        page: 1,
        limit: 10,
      }),
    },
    totalSize: {
      type: Number,
      default: 0,
    },
  },

  computed: {
    pagination() {
      const self = this;
      const showQuickJumper = this.totalSize / this.pageInfo.limit > 1;
      if (this.totalSize < 10) {
        return false;
      }
      return {
        showQuickJumper,
        showSizeChanger: true,
        current: self.pageInfo.page,
        defaultPageSize: self.pageInfo.limit,
        total: self.totalSize || 0,
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, limit) {
          self.$emit('pageChange', page, limit);
        },
        showTotal(total) {
          self.total = total;
          const totalPage = Math.ceil(self.totalSize / self.pageInfo.limit);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        onShowSizeChange(cur, size) {
          self.$emit('pageChange', cur, size);
        },
      };
    },
    columns() {
      return [
        {
          title: this.$t('special.columns.name'),
          dataIndex: 'name',
          align: 'left',
        },
        {
          title: this.$t('special.columns.type'),
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' },
          align: 'left',
        },
        {
          title: this.$t('special.columns.date'),
          dataIndex: 'endDate',
          scopedSlots: { customRender: 'date' },
          align: 'left',
        },
        {
          title: this.$t('special.columns.venueNames'),
          dataIndex: 'venueNames',
          scopedSlots: { customRender: 'venueNames' },
          align: 'left',
        },
        {
          title: this.$t('special.columns.updateUserName'),
          scopedSlots: { customRender: 'operate' },
          dataIndex: 'updateUserName',
          align: 'left',
        },
        {
          title: this.$t('special.columns.isEnable'),
          dataIndex: 'isEnable',
          scopedSlots: { customRender: 'isEnable' },
          align: 'left',
        },
        {
          title: this.$t('special.columns.operation'),
          scopedSlots: { customRender: 'operation' },
          align: 'left',
        },
      ];
    },
  },
  methods: {
    handleEdit(record) {
      this.$emit('edit', record);
    },
    handleDel(record) {
      const self = this;
      this.$confirm({
        title: this.$t('special.msg.delTip'),
        class: 'pima-confrim',
        onOk() {
          delSpecialDayApi({
            id: record.id,
          })
            .then(() => {
              self.$message.success(self.$t('special.msg.delSucc'));
              self.$emit('update', 'delete');
            });
        },
      });
    },
  },
};
</script>
