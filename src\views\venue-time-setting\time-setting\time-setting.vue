<template>
  <div
    v-if="hasP((P) => P.VenueTimeSetting.TimeSetting)"
    class="content-panel"
  >
    <GoBack
      :title="t('title', { name: venueChnName })"
      @back="onGoBack"
    >
      <template #extra>
        <a-button
          type="primary"
          size="small"
          @click="onOpenAppointment"
        >
          {{ t('action.openAppointment') }}
        </a-button>

        <a-button
          type="primary"
          size="small"
          @click="onCloseAppointment"
        >
          {{ t('action.closeAppointment') }}
        </a-button>

        <a-button
          type="primary"
          size="small"
          @click="onLockVenue"
        >
          {{ t('action.fixVenue') }}
        </a-button>
      </template>
    </GoBack>
    <!-- <a-page-header :ghost="false">
      <template #title>
        <GoBack @click.native="onGoBack" />
        {{ t('title', { name: venueChnName }) }}
      </template>

      <template #extra>
        <a-button
          type="primary"
          size="small"
          @click="onOpenAppointment"
        >
          {{ t('action.openAppointment') }}
        </a-button>

        <a-button
          type="primary"
          size="small"
          @click="onCloseAppointment"
        >
          {{ t('action.closeAppointment') }}
        </a-button>

        <a-button
          type="primary"
          size="small"
          @click="onLockVenue"
        >
          {{ t('action.fixVenue') }}
        </a-button>
      </template>
    </a-page-header> -->

    <div class="panel-body">
      <TableList
        :data-source="timePeriods"
        :is-loading="isLoadingTimePeriods"
        :venue-id="venueId"
      />

      <div class="intro">
        <p>
          <span class="desc">
            {{ t('hint.intro.symbolDesc') }}
          </span>
          <PeriodTypeIcon
            :value="PeriodType.OPENED"
            small
          />
          <span>{{ t('hint.intro.openTip') }}</span>
        </p>
        <p>
          <span class="desc">&nbsp;</span>
          <PeriodTypeIcon
            :value="PeriodType.CLOSED"
            small
          />
          <span>{{ t('hint.intro.closeTip') }}</span>
        </p>
        <p>
          <span class="desc">&nbsp;</span>
          <PeriodTypeIcon
            :value="PeriodType.LOCKED"
            small
          />
          <span>{{ t('hint.intro.lockTip') }}</span>
        </p>
      </div>
    </div>

    <ModalOpen
      v-model="isShowOpenAppointmentModal"
      :venue-id="venueId"
      @reload="loadData"
    />

    <ModalClose
      v-model="isShowCloseAppointmentModal"
      :venue-id="venueId"
      @reload="loadData"
    />

    <ModalLock
      v-model="isShowLockAppointmentModal"
      :venue-id="venueId"
      @reload="loadData"
    />
  </div>
</template>


<script>
import { mapState, mapGetters } from 'vuex';
import OpreationMixin from '@/mixins/operation';
import { nsI18n } from '@/mixins/ns-i18n';
import GoBack from '@/components/base/go-back-title.vue';
import { PeriodType } from '@/constants/venue';
import TableList from './components/table-list.vue';
import PeriodTypeIcon from './components/period-type-icon.vue';
import ModalOpen from './components/modal-open.vue';
import ModalClose from './components/modal-close.vue';
import ModalLock from './components/modal-lock.vue';


export default {
  components: {
    GoBack,
    TableList,
    PeriodTypeIcon,
    ModalOpen,
    ModalClose,
    ModalLock,
  },

  mixins: [
    OpreationMixin,
    nsI18n('t', 'venueTimeSetting.timeSetting'),
  ],

  data() {
    return {
      PeriodType,

      venueId: 0,
      isShowOpenAppointmentModal: false,
      isShowCloseAppointmentModal: false,
      isShowLockAppointmentModal: false,
    };
  },

  computed: {
    ...mapState({
      venueChnName: (state) => state.venueTimeSetting.venueChnName,
      venueEngName: (state) => state.venueTimeSetting.venueEngName,
      timePeriods: (state) => state.venueTimeSetting.timePeriods,
      isLoadingTimePeriods: (state) => state.venueTimeSetting.isLoadingTimePeriods,
      savingTimePeriodIds: (state) => state.venueTimeSetting.savingTimePeriodIds,
    }),

    ...mapGetters({
      timePeriodsSelected: 'venueTimeSetting/timePeriodsSelected',
    }),
  },

  mounted() {
    const payload = this.$route.query;
    this.venueId = Number(payload.id);
    this.loadData();
  },

  methods: {
    loadData() {
      this.$store.dispatch('venueTimeSetting/fetchVenueTimePeriodList', {
        id: this.venueId,
      });
    },

    onGoBack() {
      this.$router.back();
    },

    onOpenAppointment() {
      if (!this.timePeriodsSelected) {
        this.$warning({ title: this.t('hint.timePeriodsRequired') });
        return;
      }

      this.isShowOpenAppointmentModal = true;
    },

    onCloseAppointment() {
      if (!this.timePeriodsSelected) {
        this.$warning({ title: this.t('hint.timePeriodsRequired') });
        return;
      }

      this.isShowCloseAppointmentModal = true;
    },

    onLockVenue() {
      if (!this.timePeriodsSelected) {
        this.$warning({ title: this.t('hint.timePeriodsRequired') });
        return;
      }

      this.isShowLockAppointmentModal = true;
    },
  },
};
</script>


<style lang="less" scoped>
.intro {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 12px;
  width: 800px;
  max-width: 100%;
  color: #555;
  font-size: 12px;
  font-weight: 400;
  background: fadeout(#e63c3c, 95%);
  border-radius: 2px;
  border: 1px solid fadeout(#e63c3c, 55%);

  p:last-child {
    margin-bottom: 0;
  }

  .desc {
    display: inline-block;
    width: 70px;
    color: #222;
    text-align: right;
  }

  ::v-deep i {
    margin-right: 6px;
    vertical-align: bottom;
  }
}
</style>
