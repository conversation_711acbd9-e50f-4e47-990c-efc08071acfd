module.exports = function createPimaLocaleMiddleware(options) {
  function log(debug, ...args) {
    if (debug) {
      // eslint-disable-next-line no-console
      console.log.apply(this, ['[pima-locale-middleware][DEBUG]', ...args]);
    }
  }

  const {
    debug,
    localeQueryKeyName,
    localeCookieName,
    fallbackLocale,
    cookieMaxAge,
    cookieSecure,
    cookieSameSite,
  } = options;

  const config_debug = debug || false;
  const config_localeQueryKeyName = localeQueryKeyName || 'locale';
  const config_localeCookieName = localeCookieName || '__locale';
  const config_fallbackLocale = fallbackLocale;
  const config_cookieMaxAge = cookieMaxAge || (60 * 24 * 30 * 1000); // 默认30天
  const config_cookieSecure = cookieSecure || false;
  const config_cookieSameSite = cookieSameSite || 'Lax';

  if (!config_fallbackLocale) {
    throw new Error('fallbackLocale not configured');
  }

  return (req, res, next) => {
    const urlLocale = req.query[config_localeQueryKeyName];
    log(config_debug, 'urlLocale', urlLocale);

    const cookieLocale = req.cookies[config_localeCookieName] || null;
    log(config_debug, 'cookieLocale', cookieLocale);

    let appLocale;
    if (urlLocale) {
      appLocale = urlLocale;
    } else if (cookieLocale) {
      appLocale = cookieLocale;
    } else {
      appLocale = config_fallbackLocale;
    }

    // eslint-disable-next-line no-param-reassign
    res.locals.appLocale = appLocale;
    log(config_debug, 'appLocale', appLocale);

    if (cookieLocale !== appLocale) {
      res.cookie(config_localeCookieName, appLocale, {
        maxAge: config_cookieMaxAge,
        secure: config_cookieSecure,
        sameSite: config_cookieSameSite,
        httpOnly: false,
      });
    }

    next();
  };
};
