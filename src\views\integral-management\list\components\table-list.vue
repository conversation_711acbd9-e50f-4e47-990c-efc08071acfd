<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :scroll="scroll"
    :pagination="pagination"
  >
    <!-- 最后操作人/操作时间 -->
    <template
      slot="lastOperatorAndTime"
      slot-scope="text, row"
    >
      {{ row.updateUserName }}
      <br>
      {{ formatDate(row.updateTime, 'middle') }}
    </template>

    <!-- 操作 -->
    <template
      slot="operation"
      slot-scope="text, row"
    >
      <div>
        <a-button
          v-if="hasP(P => P.IntegralManagement.Detail) "
          class="pl-0"
          type="link"
          @click="onDetail(row)"
        >
          {{ t('action.detail') }}
        </a-button>

        <a-button
          v-if="hasP(P => P.IntegralManagement.Update)"
          class="pl-0"
          type="link"
          @click="onAdjust(row)"
        >
          {{ t('action.adjust') }}
        </a-button>
      </div>
    </template>
  </a-table>
</template>


<!-- eslint-disable import/order -->
<script>
import { mapActions, mapState } from 'vuex';

import { formatDate } from '@utils/dateformat';
import { namespaceT } from '@/helps/namespace-t';
import { getColumns } from '../hooks/columns';


import operation from '@mixins/operation';


export default {
  name: 'TableList',
  mixins: [
    operation,
  ],
  props: {
    scroll: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      columns: getColumns(),
      t: namespaceT('integralManagement'),
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.integralMgmt.dataSource,
      page: (state) => state.integralMgmt.page,
      pageSize: (state) => state.integralMgmt.pageSize,
      total: (state) => state.integralMgmt.total,
      loading: (state) => state.integralMgmt.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < this.pageSize) {
        return false;
      }
      return {
        current: this.page,
        showQuickJumper: true,
        showSizeChanger: true,
        defaultPageSize: this.pageSize,
        pageSize: this.pageSize,
        total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchIntegralManagementList({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchIntegralManagementList({
            page: current,
            pageSize: size,
          });
        },
      };
    },
  },
  methods: {
    ...mapActions({
      fetchIntegralManagementList: 'integralMgmt/fetchIntegralManagementList',
    }),

    formatDate,

    onDetail(row) {
      this.$emit('on-detail', row);
    },
    onAdjust(row) {
      this.$emit('on-adjust', row);
    },
  },
};
</script>


<style lang="less" scoped>
.pl-0{
  padding-left: 0;
}
</style>
