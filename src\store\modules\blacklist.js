import {
  blackList<PERSON><PERSON>,
  addBlackList<PERSON><PERSON>,
  editBlackListApi,
  blackListsDetailApi,
  blackRuleListApi,
  blackRuleDetailApi,
  editBlackRuleApi,
  removeBlackListApi,
} from '@/api/black-list-api';

const initialState = {
  list: [],
  total: 0,
  loading: false,
  saving: false,
  detail: null,
  ruleLoading: false,
  ruleTotal: 0,
  ruleList: [],
  ruleDetail: {},
  savingDetail: false,
  remove: {
    list: [],
    loading: false,
    page: 1,
    pageSize: 10,
    total: 0,
    filters: {
      keyword: null,
    },
  },

};

const getters = {};

const mutations = {
  setLoading(state, payload) {
    state.loading = payload;
  },
  setBlackList(state, payload) {
    state.loading = false;
    state.total = payload.total;
    state.list = payload.data;
  },
  setSaving(state, payload) {
    state.saving = payload;
  },
  setDetail(state, payload) {
    state.detail = payload;
  },
  setRuleLoading(state, payload) {
    state.ruleLoading = payload;
  },
  setBlackRuleList(state, payload) {
    state.ruleLoading = false;
    state.ruleTotal = payload.total;
    state.ruleList = payload.data;
  },
  setRuleSaving(state, payload) {
    state.savingDetail = payload;
  },
  setRuleDetail(state, payload) {
    state.ruleDetail = payload;
  },

  setRemoveLoading(state, payload) {
    state.remove.loading = payload;
  },

  setRemoveList(state, payload) {
    state.remove.list = payload.data;
    state.remove.total = payload.total;
  },

  setRemoveFilters(state, payload) {
    Object.keys(payload).forEach((key) => {
      state.remove.filters[key] = payload[key];
    });
  },

  setRemovePage(state, payload) {
    state.remove.page = payload.page;
    state.remove.pageSize = payload.pageSize;
  },
};

const actions = {
  // 获取黑名单规则列表
  fetchBlackRuleList({ commit }, payload) {
    commit('setRuleLoading', true);
    return new Promise((resolve, reject) => {
      blackRuleListApi(payload)
        .then((r) => {
          commit('setBlackRuleList', r.data);
          resolve(r.data);
        })
        .catch((e) => reject(e));
    });
  },
  // 修改黑名单规则
  editBlackRule({ commit }, payload) {
    commit('setRuleSaving', true);
    return new Promise((resolve, reject) => {
      editBlackRuleApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setRuleSaving', false);
        });
    });
  },
  // 获取黑名单规则详情
  fetchBlackRuleDetail({ commit }, payload) {
    return new Promise((resolve, reject) => {
      blackRuleDetailApi(payload)
        .then((r) => {
          commit('setRuleDetail', r.data.model);
          resolve(r.data);
        })
        .catch((e) => reject(e));
    });
  },
  // 获取黑名单列表
  fetchBlackList({ commit }, payload) {
    commit('setLoading', true);
    return new Promise((resolve, reject) => {
      blackListApi(payload)
        .then((r) => {
          commit('setBlackList', r.data);
          resolve(r.data);
        })
        .catch((e) => reject(e));
    });
  },
  // 新建黑名单
  createBlackList({ commit }, payload) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      addBlackListApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setSaving', false);
        });
    });
  },
  // 修改黑名单
  editBlackList({ commit }, payload) {
    commit('setSaving', true);
    return new Promise((resolve, reject) => {
      editBlackListApi(payload)
        .then((r) => resolve(r))
        .catch((e) => reject(e))
        .finally(() => {
          commit('setSaving', false);
        });
    });
  },
  // 获取黑名单详情
  fetchBlackListDetail({ commit }, payload) {
    return new Promise((resolve, reject) => {
      blackListsDetailApi(payload)
        .then((r) => {
          commit('setDetail', r.data.model);
          resolve(r.data);
        })
        .catch((e) => reject(e));
    });
  },

  // 获取移出黑名单人员列表
  async fetchRemoveBlackList({ commit, state }, payload) {
    try {
      commit('setRemoveLoading', true);

      if (payload) {
        commit('setRemoveFilters', payload);
        commit('setRemovePage', {
          page: payload.page || state.remove.page,
          pageSize: payload.pageSize || state.remove.pageSize,
        });
      }

      const params = {
        page: state.remove.page,
        limit: state.remove.pageSize,
        ...state.remove.filters,
      };

      const res = await removeBlackListApi(params);

      commit('setRemoveList', res.data);
    } finally {
      commit('setRemoveLoading', false);
    }
  },
};

export default {
  namespaced: true,
  state: initialState,
  getters,
  mutations,
  actions,
};
