import Vue from 'vue';
import Router from 'vue-router';
import config from '../config';
import ContentView from '../views/content-view.vue';
import { RouteName as RN } from '../constants/route';
import { useLoginStatusStore } from '../store/login-status';

Vue.use(Router);
// 解决重复路由错误 开始
// 获取原型对象上的push函数
const originalPush = Router.prototype.push;
// 修改原型对象中的push方法
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
// 解决重复路由错误 结束


export default function createRouter() {
  const router = new Router({
    mode: 'history',
    routes: [
      {
        path: config.publicPath,
        name: 'root',
        component: ContentView,
        children: [
          {
            path: 'venue-list',
            name: 'basicData',
            component: () => import('../views/basic-data'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-list'],
            },
          },
          {
            path: 'venue-list-category',
            name: 'venueListCategory',
            component: () => import('../views/basic-data-category/index.vue'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-list'],
            },
          },
          {
            path: 'venue-calendar',
            name: 'venue-calendar',
            component: () => import('../views/venue-calendar'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-calendar'],
            },
          },
          {
            path: 'venue-time-list',
            name: 'venue-time-list',
            component: () => import('../views/venue-time-setting'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-time-list'],
            },
            children: [
              {
                path: '/',
                name: 'venue-time-list.venue-list',
                component: () => import('../views/venue-time-setting/venue-list'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['venue-time-list', 'venue-time-list.venue-list'],
                  keepAlive: true,
                },
              },
              {
                path: 'time-setting',
                name: 'venue-time-list.time-setting',
                component: () => import('../views/venue-time-setting/time-setting'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['venue-time-list', 'venue-time-list.time-setting'],
                },
              },
            ],
          },
          {
            path: 'venue-rule-list',
            name: 'reserveRule',
            component: () => import('../views/reserve-rule'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-rule-list'],
              keepAlive: true,
            },
          },
          {
            path: 'venue-reservation-list',
            name: 'VenueReservationList',
            component: () => import('../views/reserve-query'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-reservation-list'],
            },
            children: [
              {
                path: '/',
                name: 'ReservationQuery',
                component: () => import('../views/reserve-query/reservation-query'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['venue-reservation-list'],
                  keepAlive: true,
                },
              },
              {
                path: 'reservation-detail',
                name: 'ReservationDetail',
                component: () => import('../views/reserve-query/reservation-detail'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['venue-reservation-list'],
                },
              },
            ],
          },
          // 预约审批
          {
            path: 'venue-approval-list',
            name: RN.ReserveApprovalList,
            component: () => import('../views/reservation-approval'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-reservation-approval'],
            },
          },
          // 预约审批详情
          {
            path: 'venue-approval-detail',
            name: RN.ReserveApprovalDetail,
            component: () => import('../views/reservation-approval/detail/approval.vue'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-reservation-approval'],
            },
          },

          {
            path: 'special-day',
            name: 'specialDay',
            component: () => import('../views/special-day/index.vue'),
            // component: () => import('../views/integral-management/list/list.vue'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-special-date'],
            },
          },
          {
            path: 'black-list',
            name: 'balckList',
            component: () => import('../views/black-list/index.vue'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-black-list'],
            },
          },
          {
            path: 'black-list-setting',
            name: 'BlackListSetting',
            component: () => import('../views/black-list-setting/index.vue'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-black-list'],
            },
          },
          {
            path: 'venue-signin',
            name: 'reserveRule',
            component: () => import('../views/signin'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-signin'],
            },
          },
          {
            path: 'user-notes',
            name: 'userNotes',
            component: () => import('../views/user-notes'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['user-notes'],
            },
            children: [
              {
                path: '/',
                name: 'userNotesList',
                component: () => import('../views/user-notes/list'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['user-notes'],
                  keepAlive: true,
                },
              },
              {
                path: 'creat',
                name: 'userNotesCreat',
                component: () => import('../views/user-notes/creat'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['user-notes'],
                },
              },
              {
                path: 'announce-creat',
                name: 'userAnnounceCreat',
                component: () => import('../views/user-notes/announce-creat'),
                meta: {
                  requiresAuth: true,
                  sidebarOpenKeys: [],
                  sidebarSelectedKeys: ['user-notes'],
                },
              },
            ],
          },

          // 积分管理
          {
            path: 'integral-management-list',
            name: RN.IntegralManagementList,
            component: () => import('@/views/integral-management/list'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-point-manage'],
            },
          },
          // 积分规则设置
          {
            path: 'integral-rule-setting',
            name: RN.IntegralRuleSetting,
            component: () => import('@/views/integral-management/rules-setting'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-point-manage'],
            },
          },

          // 查看积分详情
          {
            path: ':id/integral-details',
            name: RN.IntegralDetails,
            component: () => import('@/views/integral-management/detail-list'),
            meta: {
              requiresAuth: true,
              sidebarOpenKeys: [],
              sidebarSelectedKeys: ['venue-point-manage'],
            },
          },
        ],
      },
    ],
  });

  const isClient = process.env.VUE_ENV !== 'server';
  const store = useLoginStatusStore();

  router.beforeEach((to, from, next) => {
    if (isClient) {
      // 如果在路由配置里 meta.requiresAuth 为 false，则为当前路由可不通过认证访问，反之变然
      // meta.requiresAuth 不配置或配置为 true，则为需要认证
      // 当前用户的认证状态保存于 useLoginStatusStore
      // 本逻辑只处理客户端，不处理服务器端
      // 当由不需要认证的界面，进入需要认证的界面，则进行跳转，跳转后由服务器端进行身份认证
      if (to.meta.requiresAuth !== false
        && store.loggedIn === false) {
        next(false);
        const { href } = router.resolve(to);
        window.location.href = href;
      }
    }

    next();
  });

  return router;
}
