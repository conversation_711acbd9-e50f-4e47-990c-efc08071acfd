// 配置table columns
export default function getColumns(vm) {
  return [
    {
      title: vm.$t('reserveQuery.columns.number'),
      scopedSlots: { customRender: 'sn' },
    },
    {
      title: vm.$t('reserveQuery.columns.venue'),
      dataIndex: 'venueName',
    },
    {
      title: vm.$t('reserveQuery.columns.orderTime'),
      scopedSlots: { customRender: 'orderTime' },
    },
    {
      title: vm.$t('reserveQuery.columns.integralUsed'),
      dataIndex: 'costPoints',
    },
    {
      title: vm.$t('reserveQuery.columns.orderName'),
      dataIndex: 'userName',
    },
    {
      title: vm.$t('reserveQuery.columns.no'),
      dataIndex: 'userNo',
    },
    {
      title: vm.$t('reserveQuery.columns.phone'),
      dataIndex: 'userPhone',
    },
    {
      title: vm.$t('reserveQuery.columns.applyTime'),
      scopedSlots: { customRender: 'createTime' },
    },
    {
      title: vm.$t('reserveQuery.columns.status'),
      scopedSlots: { customRender: 'approvalStatus' },
    },
    {
      title: vm.$t('reserveQuery.columns.operate'),
      scopedSlots: { customRender: 'action' },
    },
  ];
}
