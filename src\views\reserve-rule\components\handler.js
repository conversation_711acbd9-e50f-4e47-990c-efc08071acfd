import {
  RuleStatusMap,
  OrderTypeMap,
  IsEnbaleMap,
  SelectScopeType,
  SELECT_TYPE,
  AUTO_APPROVAL_TYPE,
} from '@/constants/venue';

const RuleStatusI18nMap = {
  [RuleStatusMap.TRUE]: 'reserveRule.status.true',
  [RuleStatusMap.FALSE]: 'reserveRule.status.false',
};

export function getRuleStatusText(vm, progress) {
  return vm.$t(RuleStatusI18nMap[progress]);
}
/** 预约类型 */

const OrderTypeI18Map = {
  [OrderTypeMap.ACTIVITY]: 'reserveRule.orderType.activity',
  [OrderTypeMap.COMMON]: 'reserveRule.orderType.common',
};

export function getOrderTypeMapOpts(vm, withAll = false) {
  const opts = Object.entries(OrderTypeI18Map).map(([k, v]) => ({
    key: k,
    value: k,
    title: vm.$t(v),
    label: vm.$t(v),
  }));
  return withAll
    ? [
      {
        key: 'all',
        value: '',
        title: vm.$t('common.all'),
        label: vm.$t('common.all'),
      },
      ...opts,
    ]
    : [...opts];
}

// 是否状态
const isTrueStatusMapI18Map = {
  [IsEnbaleMap.TRUE]: 'reserveRule.isTrueStatus.TRUE',
  [IsEnbaleMap.FALSE]: 'reserveRule.isTrueStatus.FALSE',
};

export function getIsTrueStatusText(vm, progress) {
  return vm.$t(isTrueStatusMapI18Map[progress]);
}


// 选人组件类型
const SelectScopeTypeMap = {
  [SelectScopeType.ALL]: SELECT_TYPE.SELECT_ALL,
  [SelectScopeType.DEPT]: SELECT_TYPE.SELECT_DEPARTMENT,
  [SelectScopeType.USER]: SELECT_TYPE.SELECT_PERSONAL,
};

export function getSelectScopeType(progress) {
  return SelectScopeTypeMap[progress];
}


// 自动审批机制
const autoApprovalTypeI18Map = {
  [AUTO_APPROVAL_TYPE.AUTO_REJECT]: 'reserveRule.autoApprovalType.autoReject',
  [AUTO_APPROVAL_TYPE.AUTO_PASS]: 'reserveRule.autoApprovalType.autoPass',
};

export function getAutoApprovalTypeText(progress) {
  return autoApprovalTypeI18Map[progress];
}
