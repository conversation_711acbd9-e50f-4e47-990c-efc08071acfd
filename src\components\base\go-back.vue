<template>
  <div class="goback-icon">
    <span class="left" />
  </div>
</template>

<script>
export default {
  name: 'GoBack',
};
</script>

<style lang="less" scoped>
.goback-icon {
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  justify-content: center;
  border: 1px solid #d0d0d0;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-right: 10px;
  transition: border-color 200ms ease;
  &:hover {
    border-color: #bdbdbd;
    .left {
      border-color: #bdbdbd;
    }
  }

  .left {
    width: 5px;
    height: 5px;
    border-top: 1px solid #d0d0d0;
    border-left: 1px solid #d0d0d0;
    transform: rotate(-45deg);
    transition: border-color 200ms ease;
  }
}
</style>
