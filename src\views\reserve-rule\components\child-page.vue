<template>
  <div class="child-box">
    <div class="child-content">
      <!-- 规则名称 -->
      <div
        class="rule-label"
      >
        {{ t('title.timePeriod') }}
      </div>
      <TimePicker
        v-model="periodSetting.startTime"
        :disabled="!isEditable"
        class="time-picker"
      />
      <span>—</span>
      <TimePicker
        v-model="periodSetting.endTime"
        :disabled="!isEditable"
        class="time-picker"
      />
    </div>

    <div class="child-content">
      <!-- 需用积分 -->
      <div
        class="integral-label"
      >
        {{ t('title.integralNeeded') }}
      </div>

      <a-input-number
        v-model="periodSetting.integralNeeded"
        :disabled="!isEditable"
        :min="0"
        :max="99999"
        :precision="0"
        :step="1"
        class="integral-input"
      />
    </div>


    <div class="child-button">
      <!-- 规则名称 -->
      <div
        class="rule-label"
      >
        {{ ' ' }}
      </div>
      <!-- 添加时间 -->
      <a-icon
        v-if="isEditable"
        type="check"
        class="icon-button"
        @click="postAddItem"
      />
      <!-- 编辑时间 -->
      <a-icon
        v-else
        type="edit"
        class="icon-button"
        @click="handleEditItem"
      />
      <a-switch
        v-model="periodSetting.isEnable"
        class="enable-switch"
        @click="postEnableItem"
      />
      <img
        v-if=" initPeriodArrays.length > 1 "
        src="@assets/img/del.png"
        class="handle-icon"
        @click="deleteItem"
      >
      <img
        v-if="index === initPeriodArrays.length - 1 "
        src="@assets/img/add.png"
        class="handle-icon"
        @click="addItem"
      >
    </div>
  </div>
</template>

<script>
import { nsI18n } from '@/mixins/ns-i18n';
import TimePicker from './time-picker.vue';

export default {
  name: 'ChildPage',
  components: {
    TimePicker,
  },
  mixins: [
    nsI18n('t', 'reserveRule'),
  ],
  props: {
    index: {
      type: Number,
      required: true,
    },
    initPeriodArrays: {
      type: Array,
      default: Array,
    },
  },
  data() {
    return {
      isEditable: false,
      periodSetting: {
        startTime: '',
        endTime: '',
        integralNeeded: '',
        isEnable: false,
      },
      inputValue: '',
      inputValue2: '',
      emptFlag: false,
    };
  },
  computed: {
  },
  watch: {
    periodSetting: {
      handler(newV) {
        const tempData = newV;
        this.$emit('update-data', { index: this.index, data: tempData });
        return null;
      },
      deep: true,
    },
  },
  mounted() {
    this.inputValue = this.periodSetting.startTime;
    this.inputValue2 = this.periodSetting.endTime;
    this.periodSetting = { ...(this.initPeriodArrays)[this.index] };
  },
  methods: {
    handleEditItem() {
      this.isEditable = !this.isEditable;
    },
    deleteItem() {
      this.$emit('post-delete-item', this.index);
    },
    addItem() {
      this.$emit('add-index');
    },
    postAddItem() {
      this.isEditable = !this.isEditable;
      this.$emit('post-add-item', this.index);
    },
    postEnableItem() {
      this.$emit('post-enable-item', this.index);
    },
  },
};
</script>
<style lang="less" scoped>
/**  时段标签 */
.rule-label,
.integral-label {
  display: none;
}
.child-box:first-of-type {
  .rule-label,
  .integral-label {
    display: block;
    width: 110px;
    height: 20px;
    margin-bottom: 12px;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
  }
}

.child-box {
  display: flex;
  align-items: center;
  margin-left: 100px;
  margin-bottom: 16px;
  .child-content {
    margin-right: 16px;
    .time-picker {
      width:130px !important;
    }

    .integral-input{
      width: 110px;
    }
  }
  .child-button {
    margin-left: 21px;
    .icon-button {
      font-size: 22px;
      margin-right: 37px;
    }
    .enable-switch {
      margin-right: 40px;
    }
    .handle-icon {
      width: 29px;
      margin-right: 21px;
      cursor: pointer;
    }
  }
}
</style>
