import Qs from 'qs';
import httpRequest from './request';
// 获取预约审批列表
// eslint-disable-next-line import/prefer-default-export
export function getReservationApprovalListApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/reservation-receivers',
      method: 'get',
      paramsSerializer() {
        return Qs.stringify(params, { indices: false });
      },
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
