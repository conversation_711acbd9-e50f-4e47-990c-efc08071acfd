<template>
  <div class="drawer-bd">
    <a-spin :spinning="submiting">
      <a-form
        :form="form"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 12 }"
        :colon="false"
        @submit="handleSubmit"
      >
        <!-- 中文标题 -->
        <a-form-item
          :label="tf('title')"
        >
          <a-input
            v-decorator="['title', {
              initialValue: initData.title,
              rules: [
                { required: true, message: tf('titleTip') },
                { max: 50, message: $t('form.maxLength', {num: 50}) },
              ]
            }]"
          />
        </a-form-item>
        <!-- 英文标题 -->
        <a-form-item
          :label="tf('enTitle')"
        >
          <a-input
            v-decorator="['enTitle', {
              initialValue: initData.enTitle,
              rules: [
                { max: 200, message: $t('form.maxLength', {num: 200}) },
              ]
            }]"
          />
        </a-form-item>

        <!-- 中文内容 -->
        <a-form-item
          field-decorator-id="content"
          :field-decorator-options="{
            initialValue: initData.content,
            rules: [
            ],
          }"
          :label="tf('content')"
          :required="true"
        >
          <u-editor
            v-if="hasLoad"
            v-model="initData.content"
            :locale-serviceurl="serverConfig"
          />
          <div
            v-if="contentTip"
            class="ant-form-explain has-error"
          >
            {{ tf('contentTip') }}
          </div>
        </a-form-item>

        <!-- 英文内容 -->
        <a-form-item
          field-decorator-id="enContent"
          :field-decorator-options="{
            initialValue: initData.enContent,
            rules: [
            ],
          }"
          :label="tf('enContent')"
        >
          <u-editor
            v-if="hasLoad"
            v-model="initData.enContent"
            :locale-serviceurl="serverConfig"
          />
        </a-form-item>

        <!-- 是否发布 -->
        <a-form-item
          :label="tf('isEnable')"
        >
          <a-radio-group
            v-decorator="['isEnable', {
              initialValue: true,
              rules: [
                { required: true, message: tf('isEnable') },
              ]
            }]"
            @change="() => { showDatePicker = !showDatePicker}"
          >
            <a-radio :value="VenueStatus.ENABLED">
              {{ getIsTrueStatusText(VenueStatus.ENABLED) }}
            </a-radio>
            <a-radio :value="VenueStatus.DISABLED">
              {{ getIsTrueStatusText(VenueStatus.DISABLED) }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- 发布时间 -->
        <a-form-item
          v-if="showDatePicker"
          :label="tf('publishTime')"
        >
          <a-date-picker
            v-decorator="['startTime', {
              initialValue: initData.startTime,
              rules: [
                { required: false, message: tf('sortOrderTip') },
              ]
            }]"
            :disabled-date="disabledDate"
            :disabled-time="disabledDateTime"
            show-time
            :get-calendar-container="(triggerNode) => triggerNode.parentNode"
            format="YYYY-MM-DD HH:mm"
          />
          <span class="explain-class">{{ tf('publishTimeNotice') }}</span>
        </a-form-item>
        <!-- 公告到期时间 -->
        <a-form-item
          v-if="showDatePicker"
          :label="tf('expirationTime')"
          class="date-picker"
        >
          <a-date-picker
            v-decorator="['endTime', {
              initialValue: initData.endTime,
              rules: [
                { required: false, message: tf('sortOrderTip') },
              ]
            }]"
            :get-calendar-container="(triggerNode) => triggerNode.parentNode"
            :disabled-date="disabledDate"
            :disabled-time="disabledDateTime"
            show-time
            format="YYYY-MM-DD HH:mm"
          />
          <span class="explain-class">{{ tf('expirationTimeNotice') }}</span>
        </a-form-item>
      </a-form>
    </a-spin>
    <slot />
  </div>
</template>

<script>
import moment from 'moment';
import { formatDate } from '@utils/dateformat';
import { mapState } from 'vuex';
import { VenueStatus } from '@/constants/venue';
import { nsI18n } from '@/mixins/ns-i18n';
import UEditor from '@components/ueditor.vue';
import { getIsTrueStatusText } from './handler';


export default {
  name: 'AddAnnounce',
  components: {
    UEditor,
  },
  mixins: [
    nsI18n('tf', 'userNotes.publishAnnounce.form'),
  ],

  props: {
    submiting: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      showDatePicker: true,
      // 多选组件 开始
      fetching: false,
      defaultPersonalKeys: [],
      selectedKeys: [],
      selectedUsers: [],
      changedVenueOptions: [],
      initData: {},
      // 多选组件 结束
      VenueStatus,
      saving: false,
      formModel: this.createFormModel(),
      contentTip: false,

      uploading: false,
      fileList: [],
      fileSize: 10,
      hasLoad: false,
    };
  },
  computed: {
    ...mapState({
      serverConfig: (state) => state.userNotes.serverConfig,
    }),
  },
  watch: {
    initData: {
      handler(newVal) {
        if (newVal.content) {
          this.contentTip = false;
        }
      },
      deep: true,
    },
  },
  beforeMount() {
    this.$store.dispatch('userNotes/fetchLoginUser').then(({ model }) => {
      this.$store.dispatch('userNotes/fetchToken', model.username)
        .then(() => {
          this.hasLoad = true;
        });
    });
  },
  mounted() {
  },

  methods: {
    range(start, end) {
      const result = [];
      // eslint-disable-next-line no-plusplus
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    },
    disabledDateTime(current) {
      const currentDate = moment(current).unix();
      const momentDate = moment().unix();
      const tempFlag = currentDate <= momentDate;
      return {
        disabledHours: () => (tempFlag ? this.range(0, 24).splice(0, moment().hours()) : []),
        disabledMinutes: () => (tempFlag ? this.range(0, moment().minutes() + 1) : []),
        disabledSeconds: () => [],
      };
    },
    disabledDate(current) {
      // Can not select days before today and today
      return current && current < moment().add(-1, 'day');
    },
    // 判断是否为大于等于0的整数
    isNumber(rule, value, cb) {
      if (value) {
        const reg = /^[0-9]*$/;
        if (!reg.test(value)) {
          return cb(this.tf('isNumber'));
        }
        if (value < 0) {
          return cb(this.tf('isNumber'));
        }
      }
      return cb();
    },
    // 显示是否
    getIsTrueStatusText(process) {
      return getIsTrueStatusText(this, process);
    },
    createFormModel() {
      return {
        title: '',
        enTitle: '',
        content: '',
        enContent: '',
        attachmentIds: [],
        sortOrder: '',
        isEnable: '',
      };
    },
    handleSubmit() {
      this.contentTip = !this.initData.content;
      this.form.validateFields((err, values) => {
        if (!err && !this.contentTip) {
          const { startTime = null, endTime = null } = values;
          const payload = {
            ...values,
            content: this.initData.content,
            enContent: this.initData.enContent,
          };
          if (startTime) {
            payload.startTime = formatDate(startTime, 'middle');
          }
          if (endTime) {
            payload.endTime = formatDate(endTime, 'middle');
          }
          // 不发布时不保存时间
          if (!payload.isEnable) {
            delete payload.startTime;
            delete payload.endTime;
          }
          this.$emit('handle-add-submit', payload);
        }
      });
    },
    handleCancel() {
      this.$emit('handle-cancel-submit');
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
}

.explain-class {
    font-size: 14px;
    font-weight: 300;
    color: rgba(0, 0, 0, 0.65);
    margin-left: 12px;
}
.date-picker {
  ::v-deep .ant-form-item-control-wrapper {
    width: 70%;
  }
}
::v-deep .ant-calendar-time-picker-select:first-child {
  width: 50%;
}

::v-deep .ant-calendar-time-picker-select:nth-child(2) {
  width: 50%;
}
::v-deep .ant-calendar-time-picker-select:last-child {
  display: none;
}
</style>
