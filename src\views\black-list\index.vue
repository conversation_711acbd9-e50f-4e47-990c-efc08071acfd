<template>
  <div
    v-if="hasP(P => P.BlackList.View)"
    class="page-panel clearfix"
  >
    <div class="content-panel">
      <a-page-header
        :ghost="false"
      >
        <a-form
          :colon="false"
          :form="form"
          layout="inline"
        >
          <a-form-item
            self-update
          >
            <a-input
              v-decorator="['keyword', {
                initialValue:''
              }]"
              :placeholder="$t('blacklist.form.keywordsPlace')"
              style="width: 300px"
              allow-clear
              @pressEnter="search('search')"
              @change="debounce(onInputChange, 1000)()"
            >
              <span
                slot="prefix"
                class="iconfont icon-all_sousuo"
                @click="search"
              />
            </a-input>
          </a-form-item>
        </a-form>
        <template #extra>
          <a-button
            v-if="hasP(P => P.BlackList.Setting)"
            type="primary"
            size="small"
            @click="handleBlackListSetting"
          >
            {{ $t('blacklist.action.setting') }}
          </a-button>
          <a-button
            v-if="hasP(P => P.BlackList.Add)"
            type="primary"
            size="small"
            @click="handleAddBlackList"
          >
            {{ $t('blacklist.action.add') }}
          </a-button>
        </template>
      </a-page-header>
      <div class="panel-body">
        <a-tabs
          class="pima-tabs"
          :default-active-key="TAB_NAMES.BLACK_LIST"
        >
          <a-tab-pane
            :key="TAB_NAMES.BLACK_LIST"
            :tab="t(`tabs.${TAB_NAMES.BLACK_LIST}`)"
          >
            <!-- {{ canViewRemoveList }} -->
            <TableList
              :data-source="dataSource"
              :total-size="totalSize"
              :is-loading="isLoading"
              :page-info.sync="queryInfo"
              @pageChange="onPageChange"
              @edit="handleEditblacklist"
              @on-remove="onShowModalRemove"
              @update="handleTableUpdated"
            />
          </a-tab-pane>
          <a-tab-pane
            v-if="hasP((P)=>P.BlackList.ViewRemoveList)"
            :key="TAB_NAMES.REMOVE_BLACK_LIST"
            :tab="t(`tabs.${TAB_NAMES.REMOVE_BLACK_LIST}`)"
          >
            <RemoveTableList
              :scroll="tableScroll"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <a-modal
      v-model="visibles"
      :title="modalTitle"
      :width="940"
      :footer="null"
      :mask-closable="false"
      :destroy-on-close="true"
      :dialog-style="{ left: '10px', top: '20px' }"
      :wrap-class-name="'modal-wrap'"
    >
      <BlackListForm
        :id="record.id"
        :type="type"
        @update="handleTableUpdated"
        @close="handleCloseModal"
      />
    </a-modal>

    <ModalRemove
      ref="modalRemoveRef"
      :submitting="submitting"
      @on-submit="handleRemoveSubmit"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';

import { delBlackListApi } from '@/api/black-list-api';
import { namespaceT } from '@/helps/namespace-t';

import OpreationMixin from '@/mixins/operation';

import TableList from './table-list.vue';
import BlackListForm from './black-list-form.vue';
import ModalRemove from './components/modal-remove.vue';
import RemoveTableList from './components/remove-table-list.vue';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, ...args);
    }, wait);
  };
}


const TAB_NAMES = Object.freeze({
  BLACK_LIST: 'blackList',
  REMOVE_BLACK_LIST: 'removeBlackList',
});

export default {
  name: 'BlackList',

  components: {
    TableList,
    BlackListForm,
    ModalRemove,
    RemoveTableList,
  },

  mixins: [OpreationMixin],

  data() {
    return {
      form: this.$form.createForm(this),
      queryInfo: {
        limit: 10,
        page: 1,
      },
      type: 'add',
      visibles: false,
      record: {},
      submitting: false,
      TAB_NAMES,
      t: namespaceT('blacklist'),
    };
  },

  computed: {
    ...mapState({
      dataSource: (state) => state.blacklist.list,
      totalSize: (state) => state.blacklist.total,
      isLoading: (state) => state.blacklist.loading,
    }),
    modalTitle() {
      return this.type === 'add'
        ? this.$t('blacklist.title.add')
        : this.$t('blacklist.title.edit');
    },

    tableScroll() {
      return { y: 'calc(100vh - 214px)' };
    },

    canViewRemoveList() {
      return this.hasP((P) => P.BlackList.ViewRemoveList);
    },
  },

  watch: {
    canViewRemoveList: {
      handler(val) {
        if (val) {
          this.fetchRemoveBlackList();
        }
      },
      immediate: true,
    },
  },

  mounted() {
    this.search('search');
  },

  methods: {
    ...mapActions({
      fetchRemoveBlackList: 'blacklist/fetchRemoveBlackList',
    }),

    debounce,
    onInputChange() {
      const self = this;
      this.$nextTick(() => {
        this.search('search');

        this.removeSearch({
          keyword: self.form.getFieldValue('keyword'),
        });
      });
    },

    removeSearch(payload) {
      if (this.canViewRemoveList) {
        this.fetchRemoveBlackList(payload);
      }
    },

    onPageChange(page, limit) {
      this.queryInfo.page = page;
      this.queryInfo.limit = limit;
      this.search('changePage');
    },
    search(isChangePage) {
      if (isChangePage === 'search') {
        this.queryInfo.page = 1;
      }
      const { form } = this;
      form.validateFields((err) => {
        if (!err) {
          const values = form.getFieldsValue();
          this.$store.dispatch('blacklist/fetchBlackList', {
            ...values,
            ...this.queryInfo,
          });
        }
      });
    },
    // 新增黑名单
    handleAddBlackList() {
      this.type = 'add';
      this.visibles = true;
    },
    // 编辑黑名单
    handleEditblacklist(record) {
      this.record = record;
      this.type = 'edit';
      this.visibles = true;
    },
    handleCloseModal() {
      this.visibles = false;
    },
    handleTableUpdated(formType) {
      if (formType === 'delete') {
        /* eslint-disable-next-line */
        this.queryInfo.page = this.queryInfo.page > 1
          ? ((this.queryInfo.page - 1) * this.queryInfo.limit < this.totalSize - 1
            ? this.queryInfo.page
            : this.queryInfo.page - 1)
          : 1;
      }
      this.search();
    },
    handleBlackListSetting() {
      this.$router.push({
        name: 'BlackListSetting',
      });
    },
    onShowModalRemove(payload) {
      this.$refs.modalRemoveRef.setModel(payload);
      this.$refs.modalRemoveRef.onToggleVisible(true);
    },

    async handleRemoveSubmit(payload) {
      try {
        this.submitting = true;
        await delBlackListApi(payload);

        this.$message.success(this.$t('blacklist.msg.delSucc'));
        this.$refs.modalRemoveRef.onToggleVisible(false);
        this.search('search');
        this.removeSearch();
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-content {
  position: absolute;
  top: 7px;
  padding-top: 0;
  overflow: visible;
}
</style>
