<template>
  <div class="high-search">
    <a-form
      :form="form"
      :label-col="{span: 8}"
      :wrapper-col="{span:16}"
      :layout="'horizontal'"
      label-align="right"
      :colon="false"
      class="high-search-form"
      @submit="handleSearch"
    >
      <a-row>
        <!-- 状态 -->
        <a-col :span="8">
          <a-form-item
            :label="$t('reserveQuery.form.advancedSearch.status')"
          >
            <a-select
              v-decorator="['statusList', {
                initialValue: filter.statusList,
              }]"
              mode="multiple"
              :get-popup-container="
                triggerNode => {
                  return triggerNode.parentNode || document.body;
                }
              "
              :placeholder="$t('reserveQuery.form.advancedSearch.selectPlaceholder')"
              :options="reserveStatusOpts"
              style="width: 250px"
            />
          </a-form-item>
        </a-col>
        <!-- 申请单号 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.number')">
            <a-input
              v-decorator="['sn', {
                initialValue: filter.sn,
              }]"
              style="width: 250px"
            />
          </a-form-item>
        </a-col>
        <!-- 预约场馆名称 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.venue')">
            <a-select
              :value="selectedUsers.map((item) => item['id'])"
              :default-value="defaultPersonalKeys"
              :filter-option="false"
              :not-found-content="fetching ? undefined : null"
              style="width: 250px"
              mode="multiple"
              :placeholder="$t('reserveQuery.form.advancedSearch.selectPlaceholder')"
              @change="(keys) => changeSelect(keys, 'id')"
              @search="(value) => debounce(fetchUsers)(value)"
              @dropdownVisibleChange="changeDropdownVisible"
            >
              <a-spin
                v-if="fetching"
                slot="notFoundContent"
                size="small"
              />
              <a-select-option
                v-for="item1 in persons"
                :key="item1.id"
              >
                {{ item1.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <!-- 预约人 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.orderName')">
            <a-input
              v-decorator="['userKeyword', {
                initialValue: filter.userKeyword,
              }]"
              :placeholder="$t('reserveQuery.form.advancedSearch.orderNamePlaceholder')"
              style="width: 250px"
            />
          </a-form-item>
        </a-col>
        <!-- 所在组织 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.belongTo')">
            <a-select
              v-decorator="['deptId', {
                initialValue: filter.deptId,
              }]"
              :options="depts"
              :get-popup-container="
                triggerNode => {
                  return triggerNode.parentNode || document.body;
                }
              "
              style="width: 250px"
            />
          </a-form-item>
        </a-col>
        <!-- 活动名称 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.activityName')">
            <a-input
              v-decorator="['activityName', {
                initialValue: filter.activityName,
              }]"
              style="width: 250px"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <!-- 预约时间范围 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.orderTime')">
            <a-range-picker
              v-decorator="['reserveTimeRange', {
                initialValue: [filter.reservationStartDate, filter.reservationEndDate],
              }]"
              allow-clear
              style="width: 250px;"
              @change="onChangeOrderTimeRange"
            />
          </a-form-item>
        </a-col>
        <!-- 申请时间范围 -->
        <a-col :span="8">
          <a-form-item :label="$t('reserveQuery.form.advancedSearch.applyTime')">
            <a-range-picker
              v-decorator="['approvalTimeRange', {
                initialValue: [filter.applyStartDate, filter.applyEndDate],
              }]"
              allow-clear
              style="width: 250px;"
              @change="onChangeApplyTimeRange"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row
        type="flex"
        justify="center"
      >
        <a-button
          html-type="submit"
          type="primary"
          @click="handleSearch"
        >
          {{ $t('action.search') }}
        </a-button>
        <a-button
          style="margin-left: 10px"
          @click="resetSearch"
        >
          {{ $t('action.reset') }}
        </a-button>

        <a-button
          style="margin-left: 10px"
          type="link"
          @click="toggleAdvancedSearch(false)"
        >
          {{ $t('action.cancle') }}
        </a-button>
      </a-row>
    </a-form>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { hasOwn } from '@utils/core';
import { searchVenuesListApi } from '@api/basic-data-api';
import { getreserveStatusI18Options } from '../helps/handler';

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, args);
    }, wait);
  };
}

/**
 * 获取选中的节点
 * @param personalData: Array<{key, value, title}>
 * @param selectedKeys: Array<string|number>
 * @param key: String
 * @return {*[]}
 */
const getSelectedPersonals = (personalData, selectedKeys, keyword = 'key') => {
  let results = [];
  if (Array.isArray(selectedKeys)) {
    // eslint-disable-next-line no-restricted-syntax
    for (const key of selectedKeys) {
      results = results.concat(...personalData.filter((Item) => Item[keyword] === key));
    }
    return results;
  }
  return results;
};

export default {
  name: 'SimpleSearch',
  props: {
    depts: {
      type: Array,
      default: () => [],
    },
    venueOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    this.form = this.$form.createForm(this);
    return {
      // 多选组件 开始
      personalData: [],
      fetching: false,
      defaultPersonalKeys: [],
      persons: [],
      selectedKeys: [],
      selectedUsers: [],
      changedVenueOptions: [],
      // 多选组件 结束
      filter: {
        // 状态
        statusList: [],
        // 单号
        sn: '',
        // 预约场馆名称
        venueIdList: [],
        // 预约人
        userKeyword: '',
        // 所在组织
        deptId: '',
        // 活动名称
        activityName: '',
        // 预约时间
        reservationStartDate: '',
        reservationEndDate: '',
        // 申请时间
        applyStartDate: '',
        applyEndDate: '',
      },
    };
  },
  computed: {
    ...mapState({
    }),
    reserveStatusOpts() {
      return getreserveStatusI18Options(this, false);
    },
  },
  mounted() {
    this.changedVenueOptions = this.venueOptions;
    // 获取全部场馆数据
    searchVenuesListApi('')
      .then(({ data }) => {
        this.personalData = data;
        this.persons = data;
      })
      .catch((err) => {
        this.$message.error(err.errorList.message);
      });
  },
  methods: {
    debounce,
    // 动态获取选择器选项
    fetchUsers(name) {
      // if (!name || !name.length) return;
      this.fetching = true;
      searchVenuesListApi(name)
        .then(({ data }) => {
          this.persons = data;
          this.fetching = false;
        })
        .catch((err) => {
          this.fetching = false;
          this.$message.error(err.errorList.message);
        });
    },
    // 动态存入选中的keyArray
    /**
     * select 改变选择
     * @param keys: {Array} 已经选中的人员的key
     * @param keyword: {string} select key
     */
    changeSelect(keys, keyword) {
      // this.persons = [];
      this.selectedKeys = keys;
      this.selectedUsers = [...getSelectedPersonals(this.personalData, this.selectedKeys, keyword)];
    },
    changeDropdownVisible(open) {
      if (!open) {
        this.persons = [];
      }
    },
    // 预约时间
    onChangeOrderTimeRange(date, dateString) {
      const [start, end] = dateString;
      this.filter.reservationStartDate = start;
      this.filter.reservationEndDate = end;
    },
    // 申请时间
    onChangeApplyTimeRange(date, dateString) {
      const [start, end] = dateString;
      this.filter.applyStartDate = start;
      this.filter.applyEndDate = end;
    },
    resetSearch() {
      this.form.resetFields();
      // 重置多选组件
      this.persons = this.personalData;
      this.selectedKeys = [];
      this.selectedUsers = [];
      Object.keys(this.filter).forEach((k) => {
        if (k === 'venueIdList' || k === 'statusList') {
          this.filter[k] = [];
        } else {
          this.filter[k] = '';
        }
      });
      this.$emit('reset-search', {
        page: 1,
        pageSize: 10,
        filter: {
          keyword: '',
          ...this.filter,
        },
      });
    },
    handleSearch(e) {
      e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          Object.keys(values).forEach((k) => {
            if (hasOwn(this.filter, k)) {
              // 特殊处理预约场馆名称 venueIdList
              this.filter[k] = values[k];
              this.filter.venueIdList = this.selectedKeys;
            }
          });
          const payload = {
            ...values,
            ...this.filter,
            statusList: values.statusList,
          };
          // 删除多余的参数
          delete payload.approvalTimeRange;
          delete payload.reserveTimeRange;
          // 时间类型
          payload.timeRangeType = 'advancedSearch';
          this.$emit('handle-search', {
            page: 1,
            filter: payload,
          });
        }
      });
    },
    toggleAdvancedSearch() {
      this.resetSearch();
      this.$emit('toggle-advanced-search', false);
    },
  },
};
</script>
