<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :pagination="pagination"
    :scroll="{ y: 'calc(100vh - 212px)' }"
  >
    <!-- 适用场地 -->
    <template
      slot="venueList"
      slot-scope="text"
    >
      <div v-if="text && text.length">
        <span
          v-for="(item, index) in text"
          :key="index"
        >
          <span>{{ item.name }}</span>
          <span v-if="index < text.length - 1">、</span>
        </span>
      </div>
    </template>
    <!-- 最后操作人/操作时间 -->
    <template
      slot="time"
      slot-scope="text, row"
    >
      <div>
        {{ row.updateUserName }}
      </div>
      <div>
        {{ formatDate(row.updateTime, 'middle') }}
      </div>
    </template>
    <!-- 状态 -->
    <template
      slot="status"
      slot-scope="text"
    >
      <span :class="text?'status_success':'status_error'">
        {{ getRuleStatusText(text) }}
      </span>
    </template>
    <!-- 操作 -->
    <template
      slot="operate"
      slot-scope="text, row"
    >
      <a
        v-if="row.isNeedReserve && hasP(P => P.ReserveRule.PeriodSetting)"
        class="action_link nowrap"
        @click="goPeriodSetting(row)"
      >
        {{ $t('reserveRule.action.time') }}
      </a>
      <a
        v-if="hasP(P => P.ReserveRule.Edit)"
        class="action_link nowrap"
        @click="goEditRule(row)"
      >
        {{ $t('reserveRule.action.edit') }}
      </a>
      <a
        v-if="hasP(P => P.ReserveRule.Delete)"
        class="action_link nowrap"
        @click="goDeleteRule(row)"
      >
        {{ $t('reserveRule.action.delete') }}
      </a>
    </template>
  </a-table>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { formatDate } from '@utils/dateformat';
import operation from '@mixins/operation';
import getColumns from './table-columns';
import {
  getRuleStatusText,
} from './handler';

export default {
  name: 'TableList',
  mixins: [operation],
  props: {
    imgs: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    slideIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    this.columns = getColumns(this);
    return {
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.reserveRule.dataSource,
      page: (state) => state.reserveRule.page,
      pageSize: (state) => state.reserveRule.pageSize,
      total: (state) => state.reserveRule.total,
      loading: (state) => state.reserveRule.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < 10) {
        return false;
      }
      return {
        current: this.page,
        showQuickJumper: true,
        showSizeChanger: true,
        defaultPageSize: this.pageSize,
        pageSize: this.pageSize,
        total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchReservationRulesList({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchReservationRulesList({
            page: current,
            pageSize: size,
          });
        },
      };
    },
  },
  methods: {
    ...mapActions({
      fetchReservationRulesList: 'reserveRule/fetchReservationRulesList',
    }),
    getRuleStatusText(progress) {
      return getRuleStatusText(this, progress);
    },
    formatDate,
    goPeriodSetting(row) {
      this.$emit('period-setting', row);
    },
    goEditRule(row) {
      this.$emit('edit-rule', row);
    },
    goDeleteRule(row) {
      this.$emit('delete-rule', row);
    },
  },
};
</script>
