import httpRequest from './request';
// 查询场馆列表
export function getVenuesListApi() {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/venues',
      method: 'get',
      params: {
        page: 1,
        limit: -1,
        isAll: true,
      },
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
// 获取规则列表
export function getReservationRulesApi(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/reservation-rules',
      method: 'get',
      params,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 新增规则
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function addReservationRulesApi(data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: '/reservation-rules',
      method: 'post',
      data,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 获取规则详情
 * @param userId: number
 * @return {Promise<unknown>}
 */
export function getReservationRulesDetailApi(userId) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${userId}`,
      method: 'get',
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 编辑规则
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function updateReservationRulesApi(id, data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${id}`,
      method: 'post',
      data,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 删除规则
 * @param userId: number
 * @return {Promise<unknown>}
 */
export function deleteReservationRulesApi(userId) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${userId}/remove`,
      method: 'post',
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 更新规则时段
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function updateReservationRulesTimeApi(id, data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${id}/time`,
      method: 'post',
      data,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 查看规则时段列表
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function getReservationRulesTimesApi(userId) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${userId}/times`,
      method: 'get',
    })
      .then((res) => {
        const { data } = res;
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 更新规则时段
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function updateReservationRulesTimesApi(id, data) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${id}/time`,
      method: 'post',
      data,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * 删除规则时段
 * @param data: {name, depId, isEnable }
 * @return {Promise<unknown>}
 */
export function deleteReservationRulesTimesApi(data) {
  const { id = '', reservationRuleTimeId = '' } = data;
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/reservation-rules/${id}/time/remove`,
      method: 'post',
      params: { reservationRuleTimeId },
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
