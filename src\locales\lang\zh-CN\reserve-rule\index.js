export default {
  title: {
    title: '预约规则',
    add: '新增',
    addTitle: '新增规则',
    editTitle: '编辑规则',
    deleteTitle: '删除规则',
    setting: '时段设置',
    timePeriod: '时段',
    integralNeeded: '需用积分',
  },
  columns: {
    name: '规则名称',
    venueList: '适用场地',
    time: '最后操作人/操作时间',
    status: '状态',
    operate: '操作',
  },
  addRule: {
    remarkPlaceHolder: '不超过200字',
    chRuleName: '规则名称',
    enRuleName: '规则英文名称',
    appointmentType: '预约类型',
    appointmentRequirements: {
      label: '预约要求',
      lessDay: '至少需要提前',
      lessDayOrder: '天进行过预约',
      lessDayOrderExplain: '（默认为7天，填0则不作限制）',
      moreDay: '最多可提前',
      moreDayOrder: '天进行预约',
      moreDayOrderExplain: '（默认为14天，填0则不作限制）',
      hour: '个人同一人同一天对同一场馆累计预约时长限制为',
      hourOrder: '小时',
      hourOrderExpalain: '（默认为1小时，填0则不作限制）',
      timeOrder: '可进行预约操作的时间',
      weekDay: '每周一至周五',
      holiday: '每周六周日',
      range: '具体时段',
    },
    scopeAll: '全部',
    whoCanAppointment: '谁可预约',
    canAppointmentPerson: '可预约人',
    administrator: '是否需要审批',
    isNeedReserve: '是否需要预约',
    approvalIsRequired: '预约是否需要核销',
    approvalSetting: '自动审批机制',
    submitApprovalTime: '每天',
    approvalTime: '前还未审批的申请则由系统自动退回',
    approvalPassTime: '前还未审批，则系统自动审批通过',
    submitApprovalHours: '提交预约',
    approvalHours: '小时内未审批的申请则由系统自动退回',
    approvalPassHours: '小时内未审批，则系统自动通过',
    approvalHoursExplain: '（填0则不会自动退回）',
    approvalPassHoursExplain: '（填0则不会自动审批）',
    pushSettings: '原因',
    pushSettingLabel: '推送设置',
    pushSettingBefore: '预约开始前',
    pushSettingOrder: '分钟 给预约人推送微信通知',
    pushSettingOrderExplain: '（默认为30min，填0不会发送通知）',
    applicableVenues: '适用场馆',
    remarks: '备注',
    isEnable: '是否启用',
    tooltip: '即用户可以在此时间内进行预约的操作',
    signinTip: [
      '预约类型不同则核销方式也会不同。',
      '普通类的核销方式：预约人通过刷开场馆门禁。',
      '学生活动类的核销方式：物业人员进行签到核销。',
    ],
    signinValidTime: '核销有效时间',
    signinValidTimeTip: [
      '预约开始前',
      '分钟 至 预约开始后',
      '分钟内 未签到核销属爽约。(若都填0则预约当天都可以签到核销)',
    ],
  },
  form: {
    keyword: '规则名称',
    chRuleName: '请填写规则名称',
    enRuleName: '请填写规则英文名称',
    appointmentType: '请选择预约类型',
    appointmentRequirements: '请填写预约要求',
    whoCanAppointment: '请选择谁可预约',
    administrator: '请选择是否需要审批',
    autoApprovalType: '请选择自动审批机制',
    approvalSetting: '请填写设置审核时限',
    approvalIsRequired: '请选择预约是否需要核销',
    pushSettings: '请选择取消原因',
    isEnable: '请选择是否启用',
    lessDay: '请填写至少提前预约天数',
    moreDay: '请填写最多提前预约天数',
    hourDay: '请填写预约时长限制',
    operateTime: '请选择预约操作时间',
    range: '请选择具体时段',
    number: '请输入大于等0的整数',
    fieldRequired: '此项必填',
  },
  action: {
    advancedSearch: '高级搜索',
    view: '查看',
    cancel: '取消',
    edit: '编辑',
    time: '时段设置',
    delete: '删除',
    addPerson: '选择人员',
  },
  msg: {
    success: '操作成功',
    submitSuccess: '提交成功',
    cancelSuccess: '取消成功',
    confirmAdd: '请设置此条规则的“时间配置”',
    assignSucc: '指派成功',
    handleSucc: '处理成功',
    saveSucc: '保存成功',
    imgType: '只允许上传 {types} 类型图片',
  },
  status: {
    true: '启用',
    false: '禁用',
  },
  deleteRule: {
    haveOrder: '{场馆} 在 {日期} {开始时间}~{结束时间} 时段已有人预约，需要取消预约后才能进行操作！',
    noOrder: '是否确认删除此条规则呢？',
    deleteNotice: '此规则删除后需给<span class="name">{name}</span>设置新的规则，否则用户将不能预约<span class="name">{name}</span>，确认是否删除呢？',
  },
  orderType: {
    common: '普通类',
    activity: '学生活动类',
  },
  isTrueStatus: {
    TRUE: '是',
    FALSE: '否',
  },
  autoApprovalType: {
    autoReject: '自动退回',
    autoPass: '自动通过',
  },
};
