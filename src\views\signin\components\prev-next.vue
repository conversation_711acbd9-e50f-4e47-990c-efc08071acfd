<template>
  <div class="prev-next">
    <div
      class="arrow-button"
      :class="{
        'button-disable': !dateAfter
      }"
      @click="onClickPrev"
    >
      <i class="ico-left" />
    </div>
    <a-button
      class="today-button"
      @click="goToday()"
    >
      {{ value | formatDateDay }}
    </a-button>
    <div
      class="arrow-button"
      @click="onClickNext"
    >
      <i class="ico-right" />
    </div>
  </div>
</template>


<script>
import moment from 'moment';
import FormatDateMixin from '@/mixins/formatDate';

export default {
  name: 'PrevNext',

  mixins: [
    FormatDateMixin,
  ],

  props: {
    value: {
      type: Date,
      required: true,
    },
  },
  data() {
    return {
      dateAfter: null,
    };
  },

  watch: {
    value(val) {
      this.dateAfter = moment(moment(val).format('yyyy-MM-DD')).isAfter(moment(new Date()).format('yyyy-MM-DD'));
    },
  },

  mounted() {
  },

  methods: {
    offsetDay(offset) {
      const date = moment(this.value).add(offset, 'days');
      return date;
    },

    onClickPrev() {
      if (this.dateAfter) {
        const date = this.offsetDay(-1);
        this.$emit('prev', date.toDate());
      }
    },

    onClickNext() {
      const date = this.offsetDay(1);
      this.$emit('next', date.toDate());
    },

    goToday() {
    },
  },
};
</script>


<style lang="less" scoped>
  .prev-next {
    display: flex;
    align-items: center;
    padding: 1px 0;

    .button-disable {
      opacity: 0.1;
    }
    .arrow-button {
      display: flex;
      align-items: center;
      justify-content: center;

      width: 32px;
      height: 32px;
      font-size: 22px;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        .ico-left {
          background-image: url('~@assets/img/calendar-backward-active.png');
        }

        .ico-right {
          background-image: url('~@assets/img/calendar-forward-active.png');
        }
      }

      .ico-left,
      .ico-right {
        display: block;
        width: 10px;
        height: 18px;
      }

      .ico-left {
        background: url('~@assets/img/calendar-backward.png') no-repeat center center / auto 100%;
      }

      .ico-right {
        background: url('~@assets/img/calendar-forward.png') no-repeat center center / auto 100%;
      }
    }

    .today-button {
      padding: 0 10px;
      border: none;
      color: fadeout(#000, 15%);
      font-size: 16px;
      font-weight: bold;
    }
  }

  .normal {
    color: #4A4A4A ;
    background: white;
  }

  .button {
    font-size: 14px;
  }

  .middle {
    &:extend(.normal);
    cursor: unset;

    &:hover {
      &:extend(.normal);
    }
  }
</style>
