
@mainColor: #8D0306;
.personal-top {
  height: 300px;

  .radio-wrap{
    padding-bottom: 10px;
  }

  //已选择的人员展示
  .select-list-wrap{
    padding: 10px 0;
    display: flex;
    .select-list{
      flex: 1;
      .list-item{
        border: 1px solid #D0D0D0;
        border-radius: 2px;
        background: #FFFFFF;
        font-size: 12px;
        color: rgba(0, 0, 0, .65);
        line-height: 20px;
        padding: 0 8px;
        padding-right: 20px;
        position: relative;
        margin-right: 10px;
        margin-bottom: 8px;
        display: inline-block;

        .delete-person-btn{
          position: absolute;
          width: 18px;
          height: 20px;
          right: 0;
          top: 1px;
          background-image: url("../../../assets/img/close-icon.png");
          background-size: 5px 6px;
          background-position: center center;
          background-repeat: no-repeat;
          cursor: pointer;
          transition: all 200ms ease;

          &:hover {
            background-color: #F5F5F5;
            background-image: url("../../../assets/img/close-icon-hover.png");
          }
        }
      }
    }

    .list-action{
      width: 50px;
      text-align: right;
      span{
        color: @mainColor;
        cursor: pointer;
        font-size: 14px;
      }
    }
  }
}

.personal-files-wrap{
  .files-header{
    font-size: 14px;
    color: rgba(0, 0, 0, .85);
    font-weight: 500;
    line-height: 20px;
    padding: 6px 0;
    border-bottom: 2px solid #e2e2e2;
  }
  .files-list{
    max-height: 210px;
    overflow-y: auto;
  }
  .file-item{
    height: 40px;
    border-bottom: 1px solid #e2e2e2;
    display: flex;
    align-items: center;
    font-size: 12px;

    .filename{
      margin: 0 10px;
    }
    .delete-btn {
      color: @mainColor;
      cursor: pointer;
    }
  }
}

.pima-ui-import{
  top: 200px !important;
}

/** 选人组件选中颜色*/
.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon, .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon {
  display: inline-block;
  color: @mainColor;
}