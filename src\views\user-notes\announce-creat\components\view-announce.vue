<template>
  <div class="drawer-bd">
    <a-spin
      :spinning="submiting"
      style="margin-bottom: 60px;"
    >
      <a-form
        :form="form"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :colon="false"
        label-align="left"
        @submit="handleSubmit"
      >
        <!-- title -->
        <div class="form-title">
          {{ valueByLocale(initData.title, initData.enTitle) }}
        </div>
        <!-- 状态区 -->
        <div class="form-operate">
          <div class="operate-content">
            <span class="content-label">{{ tf('lastOperator') }}</span>
            <span class="content-value">{{ initData.updateUserName }}</span>
            <span class="content-label">{{ tf('lastOperateTime') }}</span>
            <span class="content-value">{{ formatDate(initData.updateTime, 'middle') }}</span>
          </div>
          <div
            :class="[initData.isEffect ? 'status-effect' : 'status-uneffect']"
          >
            {{ getIsTrueEffectText(initData.isEffect) }}
          </div>
        </div>
        <!-- content -->
        <div
          class="main-content content-container"
          v-html="valueByLocale(initData.content, initData.enContent)"
        />

        <!-- 是否发布 -->
        <a-form-item
          :label="tf('isEnable')"
        >
          <span class="form-value">{{ getIsTrueStatusText(initData.isEnable) }}</span>
        </a-form-item>
        <!-- 发布时间 -->
        <a-form-item
          v-if="initData.startTime"
          :label="tf('publishTime')"
        >
          <span class="form-value">{{ formatDate(initData.startTime, 'middle') }}</span>
        </a-form-item>
        <!-- 公告到期时间 -->
        <a-form-item
          v-if="initData.endTime"
          :label="tf('expirationTime')"
        >
          <span class="form-value">{{ formatDate(initData.endTime, 'middle') }}</span>
        </a-form-item>
      </a-form>
    </a-spin>
    <slot />
  </div>
</template>

<script>
import { nsI18n } from '@/mixins/ns-i18n';
import { valueByLocale } from '@utils/locale';
import { formatDate } from '@utils/dateformat';
import { getIsTrueStatusText, getIsTrueEffectText } from './handler';


export default {
  name: 'ViewAnnounce',
  mixins: [
    nsI18n('tf', 'userNotes.publishAnnounce.form'),
  ],

  props: {
    initData: {
      type: Object,
      default: () => {},
    },
    submiting: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
    };
  },

  methods: {
    formatDate,
    valueByLocale,
    // 显示是否
    getIsTrueStatusText(process) {
      return getIsTrueStatusText(this, process);
    },
    // 显示是否可见
    getIsTrueEffectText(process) {
      return getIsTrueEffectText(this, process);
    },
    handleSubmit() {
    },
    handleCancel() {
      this.$emit('handle-cancel-submit');
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
  margin-left: 80px;
  position: relative;
  width: 910px;
  min-height: 100%;
  padding: 40px 93px 0 112px;
  margin: 10px auto;
  border: 1px solid #E6E6E6;
  border-radius: 2px;
  .form-title {
    font-size: 22px;
    font-weight: 500;
    color: #000000;
    line-height: 30px;
    margin: 20px 0;
  }
  .form-operate {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #979797;
    padding-bottom: 40px;
    margin-bottom: 40px;
    .operate-content {
      .content-label {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0,0,0,0.65);
        margin-right: 8px;
      }
      .content-value {
        font-size: 14px;
        font-weight: 500;
        color: #222222;
        line-height: 20px;
        margin-right: 60px;
      }
    }
    .status-uneffect {
      font-size: 16px;
      font-weight: 500;
      color: #9B0000;
    }
    .status-uneffect:before {
      content: '';
      left: 16px;
      border: 1px solid #9B0000;
      background-color: #9B0000;
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 20px;
      position: relative;
      bottom: 2px;
    }
    .status-effect {
      font-size: 16px;
      font-weight: 500;
      color: #009944;
    }
    .status-effect:before {
      content: '';
      left: 16px;
      border: 1px solid #009944;
      background-color: #009944;
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 20px;
      position: relative;
      bottom: 2px;
    }
  }
  .main-content {
      margin-bottom: 40px;
  }
  .form-value {
    font-size: 14px;
    font-weight: 500;
    color: #222222;
    line-height: 20px;
    margin-right: 60px;
  }
}
::v-deep img {
  max-width: 700px;
}
</style>
