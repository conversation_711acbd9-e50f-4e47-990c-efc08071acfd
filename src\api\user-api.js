import httpRequest, { blobDownload, blobDownloadAtt } from './request';
import config from '../config';

export function clearCookies() {
  return httpRequest({
    baseURL: '',
    url: `${config.publicPath}api/user/logout`,
    method: 'post',
  });
}

export function downloadUserImportTemplate(fileName) {
  return new Promise((resolve, reject) => {
    httpRequest({
      baseURL: config.authApiBaseUrl,
      url: 'data/user_template.xlsx',
      method: 'get',
      responseType: 'blob',
      // headers: {
      //   Authorization: auth,
      // },
    })
      .then((r) => {
        blobDownload(r.data, fileName);
        resolve();
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function downloadUserData(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      baseURL: config.authApiBaseUrl,
      url: 'users/export',
      method: 'get',
      params,
      responseType: 'blob',
    })
      .then((r) => {
        blobDownload(r.data, params.fileName);
        resolve();
      })
      .catch((e) => {
        reject(e);
      });
  });
}

// 附件下载
export function attDownload({ fileName, url }) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url,
      method: 'get',
      responseType: 'blob',
    })
      .then((r) => {
        blobDownloadAtt(r.data, fileName);
        resolve(r);
      })
      .catch((e) => reject(e));
  });
}

// 附件下载
export function attDownloadAll(params) {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `seals/download-file/${params.id}`,
      method: 'get',
      params,
      responseType: 'blob',
    })
      .then((r) => {
        blobDownloadAtt(r.data, params.fileName);
        resolve(r);
      })
      .catch((e) => reject(e));
  });
}
