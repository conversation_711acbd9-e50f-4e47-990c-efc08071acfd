import httpRequest from './service-request';
// 查询数据字典数据
export function getVenueCategoryApi() {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/services/${process.env.SERVICE_CODE}/service-dict-type/venue_category/service-dicts`,
      method: 'get',
      params: {
        page: 1,
        limit: -1,
        isAll: true,
      },
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
export function getReservationTypeApi() {
  return new Promise((resolve, reject) => {
    httpRequest({
      url: `/services/${process.env.SERVICE_CODE}/service-dict-type/reservation_type/service-dicts`,
      method: 'get',
      params: {
        page: 1,
        limit: -1,
        isAll: true,
      },
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
