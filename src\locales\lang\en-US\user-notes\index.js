export default {
  title: {
    title: 'Usage Description',
    add: 'Add',
    addTitle: 'Add Usage Description',
    editTitle: 'Edit Usage Description',
    publishAnnounce: '公告管理',
    publishAnnounceEdit: '修改公告',
    announceInfo: '公告信息',
  },
  columns: {
    title: 'Title of usage description',
    sortOrder: 'Queue number',
    status: 'Do you wish to issue',
    updateTime: 'Operator/Operating time',
    operate: 'Operation',
  },
  form: {
    title: 'Title',
    titleTip: 'Please key in title',
    enTitle: 'Title in English',
    content: 'Text in Chinese',
    contentTip: 'Please key in text in Chinese',
    enContent: 'Text in English',
    attachmentIds: 'Attachment',
    attachmentIdsTip: 'Only supports documents in xls, xls, pdf, doc, docx format',
    sortOrder: 'Queue number',
    sortOrderPlace: 'Please key in numbers, smaller numbers are in front',
    sortOrderTip: 'Please key in the queue number',
    isEnable: 'Do you wish to issue',
    keywordPlace: 'Title of usage description',
    isNumber: '只允许输入数字',
  },
  action: {
    view: 'Check',
    cancel: 'Cancel',
    edit: 'Edit',
    submit: '提交',
    save: '保存',
    change: '修改',
  },
  msg: {
    success: 'Operation is successful',
    submitSuccess: 'Successfully submitted',
    cancelSuccess: 'Successfully cancelled',
    confirmCancel: 'Do you confirm you wish to cancel application for repair?',
    assignSucc: 'Successfully assigned',
    handleSucc: 'Successfully completed',
    saveSucc: 'Successfully saved',
    delSucc: 'Successfully deleted',
    imgType: 'Only {types}  of images can be uploaded',
    delTip: 'Do you confirm you wish to delete?',
  },
  status: {
    TRUE: 'Enable',
    FALSE: 'Disable',
  },
  isTrueStatus: {
    TRUE: 'Yes',
    FALSE: 'No',
  },
  publishAnnounce: {
    msg: {
      success: '操作成功',
      submitSuccess: '保存成功',
      cancelSuccess: '取消成功',
      confirmCancel: '确定取消报修申请吗？',
      assignSucc: '指派成功',
      handleSucc: '处理成功',
      saveSucc: '保存成功',
      delSucc: '删除成功',
      imgType: '只允许上传 {types} 类型图片',
      delTip: '确认是否删除？',
    },
    form: {
      title: '中文标题',
      enTitle: '英文标题',
      titleTip: '请输入标题',
      content: '中文内容',
      enContent: '英文内容',
      contentTip: '请输入中文内容',
      isEnable: '是否发布',
      publishTime: '发布时间',
      publishTimeNotice: '(若未设置保存后即刻生效）',
      expirationTime: '公告到期时间',
      expirationTimeNotice: '(若未设置则会一直生效；若超过设置的时间后，公告失效，用户界面将不可见公告信息）',
      lastOperator: '最后修改人',
      lastOperateTime: '最后修改时间',
      effect: '用户可见',
      unEffect: '用户不可见',
    },
  },
  enabLStatus: {
    TRUE: '发布',
    FALSE: '未发布',
  },
};
