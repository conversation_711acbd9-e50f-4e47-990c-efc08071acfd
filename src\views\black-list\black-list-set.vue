<template>
  <div class="drawer-bd">
    <a-form
      :form="form"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      :colon="false"
    >
      <a-form-item
        :label="label.isAutoBlacklist"
      >
        <a-radio-group
          v-decorator="['IS_AUTO_BLACKLIST', {
            initialValue: isAutoBlacklist,
          }]"
          @change="isAutoBlacklistChange"
        >
          <a-radio value="true">
            {{ $t('common.YES') }}
          </a-radio>
          <a-radio value="false">
            {{ $t('common.NO') }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="formData.isAutoBlacklist === 'true'"
        :label="label.rule"
      >
        <a-input-number
          v-decorator="['MONTH', {
            initialValue: systemMonth,
            rules: [
              {required: true, message: $t('blacklist.place.systemMonth')}
            ],
          }]"
          :min="1"
          :max="12"
          :step="1"
          :precision="0"
        />
        {{ $t('blacklist.msg.autoTip[0]') }}
        <a-input-number
          v-decorator="['COUNT', {
            initialValue: systemCount,
            rules: [
              {required: true, message: $t('blacklist.place.systemCount')}
            ],
          }]"
          :min="1"
          :step="1"
          :precision="0"
        />
        {{ $t('blacklist.msg.autoTip[1]') }}
      </a-form-item>
      <a-form-item
        :label="label.isCancelBlacklist"
      >
        <a-radio-group
          v-decorator="['IS_CANCEL_BLACKLIST', {
            initialValue: isCancelBlacklist,
          }]"
          @change="isCancelBlacklistChange"
        >
          <a-radio value="true">
            {{ $t('common.YES') }}
          </a-radio>
          <a-radio value="false">
            {{ $t('common.NO') }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="formData.isCancelBlacklist === 'true'"
        :label="label.rule"
        :extra="$t('blacklist.msg.setTip')"
      >
        {{ $t('blacklist.msg.cancelTip[0]') }}
        <a-input-number
          v-decorator="['DAY', {
            initialValue: systemDay,
            rules: [
              {required: true, message: $t('blacklist.place.systemDay')}
            ],
          }]"
          :min="1"
          :step="1"
          :precision="0"
        />
        {{ $t('blacklist.msg.cancelTip[1]') }}
      </a-form-item>
    </a-form>
    <div class="drawer_footer">
      <a-button
        type="link"
        @click="onClose"
      >
        {{ $t('action.cancle') }}
      </a-button>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ $t('action.ok') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
// import debounce from 'lodash/debounce';

export default {
  name: 'BlackListForm',

  props: {
    type: {
      type: String,
      default: 'add',
    },
    id: {
      type: Number,
      default: 0,
    },
  },

  data() {
    // this.lastFetchId = 0;
    // this.fetchUser = debounce(this.fetchUser, 800);
    return {
      form: this.$form.createForm(this),
      label: {
        isAutoBlacklist: this.$t('blacklist.form.isAutoBlacklist'),
        isCancelBlacklist: this.$t('blacklist.form.isCancelBlacklist'),
        rule: this.$t('blacklist.form.rule'),
      },
      formData: {
        isAutoBlacklist: 'false',
        isCancelBlacklist: 'false',
        systemMonth: '',
        systemCount: '',
        systemDay: '',
      },
      fetching: false,
      data: [],
      value: [],
    };
  },

  computed: {
    ...mapState({
      loading: (state) => state.blacklist.saving,
      systemRulesMaps: (state) => state.service.systemRulesMaps,
    }),
    systemDay() {
      return this.systemRulesMaps && this.systemRulesMaps.DAY
        ? this.systemRulesMaps.DAY.value : '1';
    },
    systemCount() {
      return this.systemRulesMaps && this.systemRulesMaps.COUNT
        ? this.systemRulesMaps.COUNT.value : '1';
    },
    systemMonth() {
      return this.systemRulesMaps && this.systemRulesMaps.MONTH
        ? this.systemRulesMaps.MONTH.value : '1';
    },
    isCancelBlacklist() {
      return this.systemRulesMaps && this.systemRulesMaps.IS_CANCEL_BLACKLIST
        ? this.systemRulesMaps.IS_CANCEL_BLACKLIST.value : 'false';
    },
    isAutoBlacklist() {
      return this.systemRulesMaps && this.systemRulesMaps.IS_AUTO_BLACKLIST
        ? this.systemRulesMaps.IS_AUTO_BLACKLIST.value : 'false';
    },
  },

  mounted() {
    this.$store.dispatch('service/fetchSystemParams').then(() => {
      this.formData = {
        systemDay: this.systemDay,
        systemCount: this.systemCount,
        systemMonth: this.systemMonth,
        isCancelBlacklist: this.isCancelBlacklist,
        isAutoBlacklist: this.isAutoBlacklist,
      };
    });
  },

  methods: {
    onClose() {
      this.$emit('close');
    },
    handleSubmit() {
      const self = this;
      const { form } = this;
      form.validateFields((err, values) => {
        if (!err) {
          const r = Object.keys(values);
          const paramList = [];
          r.forEach((item) => {
            paramList.push({
              code: item,
              value: values[item],
            });
          });

          self.$store.dispatch('service/updateSystemParams', {
            paramList,
          })
            .then(() => {
              self.$message.success(self.$t('blacklist.msg.editSucc'));
              self.$emit('refresh');
              self.onClose();
            })
            .catch((e) => {
              self.$message.error(e.message);
            });
        }
      });
    },
    isAutoBlacklistChange(e) {
      this.formData.isAutoBlacklist = e.target.value;
    },
    isCancelBlacklistChange(e) {
      this.formData.isCancelBlacklist = e.target.value;
    },
  },
};
</script>

<style lang="less" scoped>
  ::v-deep .ant-form-extra {
    font-size: 12px;
    color: #8D0306;
  }
</style>
