import URI from 'urijs';
import serverConfig from '@server/config';
import config from '@/config';
import {
  getNotesListApi,
  getLoginUserInfo,
  getTokens,
  getNoticeApi,
} from '@api/user-notes-api';

const initState = {
  dataSource: [],
  noticeData: {},
  loading: false,
  page: 1,
  pageSize: 10,
  total: 0,
  filter: {
    // 是否查询全部
    isAll: false,
    // 场馆类别
    category: '',
    // 关键字
    keyword: '',
  },
  // 登录信息
  loginInfo: {},
  // 编辑器配置
  serverConfig: {
    serverUrl: '',
    UEDITOR_HOME_URL: `${config.publicPath}static/UEditor/`,
    // UEDITOR_HOME_URL: '/bdc-client/static/UEditor/',
    // 配置说明文档 https://fex.baidu.com/ueditor/#start-config
    autoHeightEnabled: true,
    initialFrameHeight: 200,
    // scaleEnabled: true,
    initialFrameWidth: '100%',
    enableAutoSave: false,
    enablePasteUpload: true,
    toolbars: [
      [
        'source',
        '|',
        'undo',
        'redo',
        '|',
        'bold',
        'italic',
        'strikethrough',
        'pasteplain',
        '|',
        'fontsize',
        'forecolor',
        'backcolor',
        'insertorderedlist',
        'insertunorderedlist',
        '|',
        'justifyleft',
        'justifycenter',
        'justifyright',
        '|',
        'link',
        'unlink',
        '|',
        'simpleupload',
        'insertword',
        '|',
        'inserttable',
        'horizontal',
      ],
    ],
    wordCount: false,
  },
};

const getters = {};

const mutations = {
  setDataSource(state, payload) {
    state.dataSource = payload;
  },
  setLoading(state, payload) {
    state.loading = payload;
  },
  setNoticeData(state, payload) {
    state.noticeData = payload;
  },
  setPage(state, payload) {
    if (!payload) return;
    state.page = Number(payload);
  },
  setPageSize(state, payload) {
    if (!payload) return;
    state.pageSize = Number(payload);
  },
  setTotal(state, payload = 0) {
    state.total = payload;
  },
  setFilter(state, payload) {
    Object.keys(payload).forEach((key) => {
      if (payload[key] !== undefined && Object.prototype.hasOwnProperty.call(state.filter, key)) {
        state.filter[key] = payload[key];
      }
    });
  },
  uploadServerConfig(state, payload) {
    state.serverConfig.serverUrl = payload;
  },
  updateLoginUser(state, payload) {
    state.loginInfo = payload;
  },
};

const actions = {
  // 获取当前用户信息
  fetchLoginUser({ commit }) {
    return new Promise((resolve, reject) => {
      getLoginUserInfo()
        .then((r) => {
          resolve(r);
          console.log('%c [ r ]-121', 'font-size:13px; background:pink; color:#bf2c9f;', r.model);
          commit('updateLoginUser', r.model);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  // 获取token
  fetchToken({ commit }, name) {
    return new Promise((resolve, reject) => {
      getTokens({ username: name }).then(({ accessToken = '' }) => {
        const u = URI(serverConfig.LOCAL_UPLOAD_IMAGE_API_URL);
        u.setQuery('Authorization', `bearer ${accessToken}`);
        const s = u.href();
        commit('uploadServerConfig', s);
        resolve(s);
      }).catch((err) => {
        reject(err);
      });
    });
  },

  fetchNotesListApi({ commit, state }, payload) {
    const { filter, page, pageSize } = payload;
    commit('setFilter', filter || {});
    commit('setPage', page);
    commit('setPageSize', pageSize);
    commit('setLoading', true);

    const params = {
      page: state.page,
      limit: state.pageSize,
    };

    Object.entries(state.filter).forEach(([k, v]) => {
      if (v !== '') params[k] = v;
    });

    return new Promise((resolve, reject) => {
      getNotesListApi(params)
        .then((res) => {
          console.log('@@fetchNotesListApi', res);
          commit('setDataSource', res.data);
          commit('setTotal', res.total);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
  fetchNoticeApi({ commit }) {
    commit('setLoading', true);
    return new Promise((resolve, reject) => {
      getNoticeApi()
        .then((res) => {
          commit('setNoticeData', res.model);
          commit('setLoading', false);
          resolve(res);
        })
        .catch((e) => {
          commit('setLoading', false);
          reject(e);
        });
    });
  },
};

export default {
  namespaced: true,
  state: initState,
  getters,
  mutations,
  actions,
};
