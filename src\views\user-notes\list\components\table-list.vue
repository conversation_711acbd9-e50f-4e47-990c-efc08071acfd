<template>
  <a-table
    key="templateTable"
    :row-key="row => row.id"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    :pagination="pagination"
    :scroll="{ y: 'calc(100vh - 212px)' }"
  >
    <!-- 场馆类别 -->
    <template
      slot="category"
      slot-scope="text, row"
    >
      <span>{{ getVenueCategoryI18Text(row.category) }}</span>
    </template>
    <!-- 场馆管理员 -->
    <template
      slot="administrator"
      slot-scope="text, row"
    >
      <span
        v-for="(item, index) in row.keeperList"
        :key="index"
      >
        <span>{{ item.name }}</span>
        <span>({{ item.phone }})</span>
        <span v-if="index < row.keeperList.length - 1">,</span>
      </span>
    </template>
    <!-- 最后操作人时间 -->
    <template
      slot="updateTime"
      slot-scope="text, row"
    >
      <div> {{ row.updateUserName }}</div>
      <div> {{ row.updateTime }}</div>
    </template>
    <!-- 状态 -->
    <template
      slot="status"
      slot-scope="text, row"
    >
      {{ getIsEnbaleMapText(row.isEnable) }}
    </template>
    <!-- 操作 -->
    <template
      slot="action"
      slot-scope="text, row"
    >
      <a
        v-if="hasP(P => P.UserNotes.Edit)"
        class="action_link nowrap"
        @click="goEdit(row)"
      >
        {{ $t('action.edit') }}
      </a>
      <a
        v-if="hasP(P => P.UserNotes.Delete)"
        class="action_link nowrap"
        @click="goDelete(row)"
      >
        {{ $t('action.del') }}
      </a>
    </template>
  </a-table>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { formatDate } from '@utils/dateformat';
import operation from '@mixins/operation';
import getColumns from './table-columns';
import {
  getIsEnbaleMapText,
  getVenueCategoryI18Text,
} from './handler';

export default {
  name: 'TableList',
  mixins: [operation],
  props: {
    imgs: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    slideIndex: {
      type: Number,
      default: 0,
    },
    venueCategoryMap: {
      type: Map,
      default: () => {},
    },
  },
  data() {
    this.columns = getColumns(this);
    return {
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.userNotes.dataSource,
      page: (state) => state.userNotes.page,
      pageSize: (state) => state.userNotes.pageSize,
      total: (state) => state.userNotes.total,
      loading: (state) => state.userNotes.loading,
    }),
    pagination() {
      const self = this;
      if (this.total < 10) {
        return false;
      }
      return {
        current: this.page,
        showQuickJumper: true,
        showSizeChanger: true,
        defaultPageSize: this.pageSize,
        pageSize: this.pageSize,
        total: this.total,
        showTotal(total) {
          const totalPage = Math.ceil(total / this.pageSize);
          return this.$t('pagination.totalLong', { totalPage, total });
        },
        pageSizeOptions: ['10', '20', '40', '80'],
        onChange(page, pageSize) {
          self.fetchNotesListApi({
            page,
            pageSize,
          });
        },
        onShowSizeChange(current, size) {
          self.fetchNotesListApi({
            page: current,
            pageSize: size,
          });
        },
      };
    },
  },
  methods: {
    ...mapActions({
      fetchNotesListApi: 'userNotes/fetchNotesListApi',
    }),
    getIsEnbaleMapText(progress) {
      return getIsEnbaleMapText(this, progress);
    },
    getVenueCategoryI18Text(progress) {
      return getVenueCategoryI18Text(progress, this.venueCategoryMap);
    },
    formatDate,
    goEdit(row) {
      this.$emit('edit-notes', row);
    },
    goDelete(row) {
      this.$emit('delete-notes', row);
    },
  },
};
</script>
