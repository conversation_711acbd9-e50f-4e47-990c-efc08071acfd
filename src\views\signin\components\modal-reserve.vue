<template>
  <div>
    <PopModal
      :title="t('titleView')"
      :visible.sync="realValue"
      @close="onClose"
    >
      <a-spin :spinning="isLoadingCurrentUserInfo">
        <div class="drawer-bd">
          <div class="border-content">
            <!-- 当前状态 -->
            <div>
              <span
                class="status-icon"
                :class="{
                  'pending': ['pending', 'approving', 'pending_sign'].includes(approvalStatus),
                  'pass': ['pass', 'auto-pass', 'signed'].includes(approvalStatus),
                  'reject': ['reject'].includes(approvalStatus),
                  'cancel': ['cancel', 'off'].includes(approvalStatus),
                }"
              />
              <span
                class="approve-status"
                :class="{
                  'pending': ['pending', 'approving', 'pending_sign'].includes(approvalStatus),
                  'pass': ['pass', 'auto-pass', 'signed'].includes(approvalStatus),
                  'reject': ['reject'].includes(approvalStatus),
                  'cancel': ['cancel', 'off'].includes(approvalStatus),
                }"
              >
                {{
                  payload && (payload.cancelType === ReserveStatus.OFF
                    ? $t('reserveQuery.status.OFF')
                    : getreserveStatusI18Text(approvalStatus))
                }}
                <!-- <span v-if="approvalStatus===ReserveStatus.OFF">
                  {{ t('offTime', { count: initData.offCount }) }}
                </span> -->
              </span>
            </div>
          </div>
          <div class="head-info">
            <template v-if="approvalStatus === ReserveStatus.SIGNED">
              <div>
                <span>{{ tl('signinTime') }}：</span>
                <span>{{ formatDate(payload.updateTime,'middle') }}</span>
              </div>
            </template>
            <template v-else-if="approvalStatus === ReserveStatus.CANCEL">
              <div>
                <span>{{ tl('cancelTime') }}：</span>
                <span>
                  {{ formatDate(payload.updateTime,'middle') }}
                </span>
              </div>
              <div>
                <span>{{ tl('remark') }}：</span>
                <span>{{ payload.cancelReason }}</span>
              </div>
            </template>
          </div>

          <a-form-model
            ref="formRef"
            layout="vertical"
            :colon="false"
            :rules="rules"
            class="form-info"
          >
            <a-form-item :label="tl('sn')">
              {{ payload.sn }}
            </a-form-item>

            <a-form-item :label="tl('createTime')">
              {{ formatDate(payload.createTime,'middle') }}
            </a-form-item>

            <a-form-item :label="tl('venueName')">
              {{ payload.venueName }}
            </a-form-item>

            <a-form-item :label="tl('reservationDate')">
              {{ `${payload.reservationDate} ${payload.startTime}-${payload.endTime}` }}
            </a-form-item>

            <a-form-item :label="tl('integralUsed')">
              {{ payload.costPoints }}
            </a-form-item>

            <a-form-item :label="tl('userName')">
              {{ payload.userName }} ({{ payload.userNo }})
            </a-form-item>

            <a-form-item :label="tl('userPhone')">
              {{ payload.userPhone }}
            </a-form-item>
          </a-form-model>

          <div class="clearfix drawer-ft">
            <a-button
              v-if="approvalStatus === ReserveStatus.SIGNED || approvalStatus === ReserveStatus.CANCEL"
              @click="onClose"
            >
              {{ $t('action.close') }}
            </a-button>
            <template v-else>
              <a-button
                v-if="payload.isCancel && hasP(P => P.VerifyCalendar.Cancel)"
                @click="onCancel"
              >
                {{ $t('reserveQuery.reserveDetail.cancel') }}
              </a-button>
              <a-button
                type="primary"
                :loading="isSubmitingReserve"
                @click="onSubmit"
              >
                {{ $t('action.verify') }}
              </a-button>
            </template>
          </div>
        </div>
      </a-spin>

      <!-- 取消预约Modal -->
      <CancelModal
        :id="payload.id"
        ref="CancelModal"
        v-model="isShowCancelModal"
        :submiting="formLoading"
        @handle-cancel-submit="handleCancelSubmit"
      />
    </PopModal>
  </div>
</template>


<script>
import _ from 'lodash';
import { mapState } from 'vuex';
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';
import operation from '@mixins/operation';
import { VenueStatus, ReserveStatus } from '@/constants/venue';
import {
  postCancelReservationApi,
} from '@api/reserve-query-api';
import { formatDate } from '@utils/dateformat';

import CancelModal from '../../reserve-query/reservation-query/components/cancel-modal.vue';
import { getreserveStatusI18Text } from '../../reserve-query/reservation-query/components/handler';


export default {
  components: {
    PopModal,
    CancelModal,
  },

  mixins: [
    nsI18n('t', 'venueCalendar.modalReserve'),
    nsI18n('tl', 'venueCalendar.modalReserve.form.label'),
    nsI18n('tf', 'basicData.form'),
    operation,
  ],

  inject: ['reloadCalendar'],

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    payload: {
      type: Object,
      default() {
        return null;
      },
    },
  },

  data() {
    return {
      VenueStatus,
      ReserveStatus,

      realValue: this.value,
      detailModel: this.createDetailModel(),
      formModel: this.createFormModel(),

      isShowCancelModal: false,
      formLoading: false,
    };
  },

  computed: {
    ...mapState({
      isLoadingCurrentUserInfo: (state) => state.verify.isLoadingCurrentUserInfo,
      currentUserInfo: (state) => state.verify.currentUserInfo,
      userDepartments: (state) => state.verify.userDepartments,
      isSubmitingReserve: (state) => state.verify.isSubmitingReserve,
    }),

    rules() {
      return {
        userPhone: [
          {
            required: true,
            message: this.$t('hint.required'),
          },
        ],
      };
    },

    approvalStatus() {
      // if (this.payload.approvalStatus === 'cancel' && this.payload.cancelType === 'off') {
      //   return this.payload.cancelType;
      // }
      let status = '';
      if (this.payload) {
        status = this.payload.approvalStatus;
      }
      return status;
    },
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }

      if (val) {
        this.initData();
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },

    isLoadingCurrentUserInfo(value, oldValue) {
      if (value === false && oldValue === true) {
        this.initData();
      }
    },
  },

  methods: {
    formatDate,
    getreserveStatusI18Text(progress) {
      return getreserveStatusI18Text(this, progress);
    },
    createDetailModel(value) {
      return {
        venueChnName: '',
        venueEngName: '',
        appointerChnName: '',
        appointerEngName: '',
        appointerNumber: '',
        ...value,
      };
    },

    createFormModel() {
      return {
        deptId: null,
        userPhone: '',
        notes: '',
      };
    },

    initData() {
      this.formModel = this.createFormModel();

      const { venueChnName, venueEngName } = this.payload;
      const name = _.get(this.currentUserInfo, 'name', '');
      const enName = _.get(this.currentUserInfo, 'enName', '');
      const userNo = _.get(this.currentUserInfo, 'userNo', '');
      const mobile = _.get(this.currentUserInfo, 'mobile', '');
      const depts = _.get(this.currentUserInfo, 'mainDeptIds', []);
      const deptId = depts.length > 0 ? depts[0].id : 0;
      this.detailModel = this.createDetailModel({
        venueChnName,
        venueEngName,
        appointerChnName: name,
        appointerEngName: enName,
        appointerNumber: userNo,
      });

      Object.assign(this.formModel, {
        deptId,
        userPhone: mobile,
      });
    },

    onClose() {
      this.realValue = false;
    },
    onSubmit() {
      const self = this;
      this.$confirm({
        title: '是否确认签到核销呢？',
        class: 'pima-confrim',
        onOk() {
          self.submitForm();
        },
      });
    },
    async submitForm() {
      try {
        const payload = {
          id: this.payload.id,
        };
        await this.$store.dispatch('verify/reserveVenue', payload);
        this.onClose();
        this.$message.success(this.$t('hint.dataSaved'));
        this.reloadCalendar();
      } catch (error) {
        this.$message.error(error.message);
      }
    },


    onCancel() {
      this.isShowCancelModal = true;
    },
    handleCancelSubmit(formData) {
      this.formLoading = true;
      postCancelReservationApi(formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.$t('reserve-query.msg.submitSuccess'));
          this.onCloseCancelVerify();
          this.onClose();
          // 刷新页面
          // this.fetchMyReservationsList({});
          this.reloadCalendar();
        })
        .catch(() => {
          this.formLoading = false;
        });
    },
    onCloseCancelVerify() {
      this.isShowCancelModal = false;
    },
  },
};
</script>

<style lang="less">
.pima-confrim {
  .ant-modal-wrap .ant-modal {
    vertical-align: top;
    top: 200px;
  }
}
</style>
<style lang="less" scoped>
.border-content {
  height: 40px;
  display: flex;
  justify-content: space-between;
  padding: 0 36px 0 21px;
  line-height: 40px;
  background-color: #FCFCFC;
  .status-icon {
    display:inline-block;
    width: 22px;
    height: 22px;
    background-size: 100%;
    vertical-align: middle;
    background: url('@assets/img/ico_status_pending.png') no-repeat;
    &.pending {
      background: url('@assets/img/ico_status_pending.png') no-repeat;
    }
    &.pass {
      background: url('@assets/img/ico_status_pass.png') no-repeat;
    }
    &.reject {
      background: url('@assets/img/ico_status_reject.png') no-repeat;
    }
    &.cancel {
      background: url('@assets/img/ico_status_cancel.png') no-repeat;
    }
  }
  .approve-status {
    font-size: 16px;
    font-weight: 600;
    color: #9B0000;
    vertical-align: middle;
    &.pending {
      color: #F49B00;
    }
    &.pass {
      color: #009944;
    }
    &.reject {
      color: #9B0000;
    }
    &.cancel {
      color: rgba(0,0,0,0.45);
    }
  }
  .approve-createtime {
    font-size: 12px;
    color: #555555;
  }
}

.head-info {
  padding: 10px 21px;
}

.item-title{
  font-size: 15px;
  font-weight: 300;
  color: rgba(0,0,0,0.65);
  line-height: 21px;
}
.item-value{
  font-size: 15px;
  font-weight: 400;
  color: #222222;
  line-height: 21px;
}
.form-info {
  padding: 20px 105px 0;
  .title {
    font-size: 18px;
    font-weight: 500;
    color: #9B0000;
    line-height: 25px;
    margin-bottom: 10px;
  }
  ::v-deep .ant-form-item{
    border-bottom: 1px solid #EDEDED;
    padding: 14px 20px;
    display: flex;
    margin-bottom: 0;
    &:last-child {
      border: none;
    }
    .ant-form-item-label {
      width: 200px;
      text-align: left;
      line-height: 1;
      padding: 0;
    }
    .ant-form-item-control-wrapper {
      flex: 1;
      text-align: right;
      line-height: 1;
    }
  }
  .block-content {
    display: block;
    ::v-deep .ant-form-item-control-wrapper {
      flex: none;
      text-align: left;
      margin-top: 8px;
    }
  }
}.panel-container {
  display: flex;
  margin-left: 15%;
}
.card {
  // float: left;
  margin-top: 10px;
  // margin-left: 15%;
  width: 850px;
  /deep/ .ant-card-head-title {
    height: 85px;
    line-height: 85px;
    font-size: 24px;
    text-align: center;
  }
}
 .panel-body .footer_btns {
  text-align: center;
  text-align: left;
  padding-left: calc(15% + 425px);
  >div {
    display: inline-block;
    transform: translate(-50%);
  }
}
.form-info-PC {
  padding: 20px 105px 0;
}
.mobile-submit {
  width: 100%;
  height: 40px;
}
@media (max-width: 1200px) {
  .panel-body {
    padding-bottom: 80px !important;
  }
  .panel-container {
    display: block;
    margin-left: 0;
    .card {
      width: 100%;
      padding-top: 10px;
      .form-info, .form-info-PC {
        padding: 20px 22px 0;
      }
    }
    .operation-log {
      margin-left: 0;
      margin-top: 10px;
      background-color: #fff;
      padding: 0 23px;
      .title {
        font-size: 16px;
      }
      /deep/ .ant-steps {
        margin-left: 0;
        width: 100%;
      }
    }
  }
   .panel-body .footer_btns {
    padding-left: 0;
    text-align: center;
  }
}
@media (max-width: 768px) {
  .panel-container {
    .card {
      border: 0;
      margin: 0;
      padding: 0;
      .border-content {
        background: #FCFCFC;
        height: 50px;
        line-height: 50px;
        padding: 0 21px 0 21px;
      }
      /deep/ .ant-card-head {
        display: none;
      }
    }
    .operation-log {
      border: 0;
    }
  }
   .panel-body .footer_btns {
    left: 0;
    height: 100px;
    padding-top: 20px;
    >div {
      transform: none;
      .ant-btn:first-child {
        margin-right: 10px;
      }
    }
    .ant-btn {
      width: 157px;
      height: 50px;
    }
  }
}
@media (max-width: 340px) {
  .border-content {
    padding: 0 15px;
  }
   .panel-body .footer_btns {
    .ant-btn {
      width: 140px;
    }
  }
}
.panel-body {
  padding-bottom: 80px !important;
  .operation-log {
  margin-left: 30px;
  margin-top: 95px;
  padding: 0 13px;
  border: 1px solid #f6f6f6;
  border-radius: 3px;
  }
}
</style>
