<template>
  <div
    class="status-container"
    :class="{'hasSelected': hasSelected}"
  >
    <div
      v-if="currentVenue && currentPeriod"
      class="status"
      :class="className"
      @click="onClickReserve"
    >
      <div class="status-wrap">
        <template v-if="PeriodProcessStatus.RESERVED === currentPeriod.status">
          <template
            v-if="currentPeriod.activityName"
          >
            <span class="t14 block">{{ currentPeriod.activityName }}</span>
          </template>
          <span class="t12 block">{{ currentPeriod.reservationUserName }}</span>
          <template v-if="currentPeriod.fixedVenueName">
            <span class="t12 block">{{ currentPeriod.fixedVenueName }}</span>
          </template>
          <span class="t12 block">
            {{ costPointsText }}
          </span>
          <ModalReserve
            v-model="isShowModalReserve"
            :payload="payload"
            :is-edit="isEdit"
            :data-source="currentPeriod"
          />
        </template>
        <template v-else-if="PeriodProcessStatus.AVAILABLE === currentPeriod.status">
          <div class="btn-reserve">
            {{ currentPeriod.status | periodProcessStatusI18n }}
            <br>
            {{ costPointsText }}
          </div>

          <!-- <ModalReserve
            v-model="isShowModalReserve"
            :payload="payload"
          /> -->
        </template>
        <template v-else>
          {{ currentPeriod.status | periodProcessStatusI18n }}
          <br>
          {{ costPointsText }}
        </template>
      </div>
      <div
        class="selected-icon"
        :class="{'hasSelected': hasSelected}"
      />
    </div>
    <div
      v-else
      class="empty-holder"
    />
  </div>
</template>


<script>
import moment from 'moment';
import { periodProcessStatusI18n } from '@/filters/venue';
import { PeriodProcessStatus, OrderTypeMap } from '@/constants/venue';
import { namespaceT } from '@/helps/namespace-t';
import ModalReserve from './modal-reserve.vue';

export default {
  components: {
    ModalReserve,
  },

  filters: {
    periodProcessStatusI18n,
  },

  inject: ['canReserve'],

  props: {
    value: {
      type: Object,
      default() {
        return null;
      },
    },

    selectedTimeList: {
      type: Array,
      default() {
        return [];
      },
    },
    rowIndex: {
      type: Number,
      default: 0,
    },

    columnIndex: {
      type: Number,
      default: 0,
    },

    venues: {
      type: Array,
      default() {
        return [];
      },
    },

    selectedDate: {
      type: Date,
      default() {
        return null;
      },
    },
  },

  data() {
    return {
      OrderTypeMap,
      PeriodProcessStatus,
      isShowModalReserve: false,
      isEdit: false,
      t: namespaceT('venueCalendar'),
    };
  },

  computed: {
    currentVenue() {
      const idx = Math.max(0, this.columnIndex - 1);
      return this.venues[idx];
    },

    currentPeriod() {
      if (this.currentVenue && Array.isArray(this.currentVenue.venueCalendarVenueTimeCellVoList)) {
        let currPeriod = null;
        this.currentVenue.venueCalendarVenueTimeCellVoList.forEach((period) => {
          if (period.startTime === this.value.startTime && period.endTime === this.value.endTime) {
            currPeriod = period;
          }
        });
        return currPeriod;
      }

      return null;
    },

    costPointsText() {
      return `（${this.t('tableList.text.integrals', { integrals: this.currentPeriod.costPoints })}）`;
    },

    className() {
      const map = new Map([
        [PeriodProcessStatus.FIXED_VENUE, 'status-fixed-venue'],
        [PeriodProcessStatus.AVAILABLE, 'status-available'],
        [PeriodProcessStatus.RESERVED, this.canModify ? 'status-reserved can-modify' : 'status-reserved'],
        [PeriodProcessStatus.EXPIRED, 'status-expired'],
        [PeriodProcessStatus.CLOSED, 'status-closed'],
      ]);
      return map.get(this.currentPeriod.status);
    },

    payload() {
      return {
        venueId: this.currentVenue.venueId,
        venueChnName: this.currentVenue.name,
        venueEngName: this.currentVenue.enName,
        reservationType: this.currentVenue.reservationType,
        selectedDate: moment(this.selectedDate).format('yyyy-MM-DD'),
        startTime: this.currentPeriod.startTime,
        endTime: this.currentPeriod.endTime,
        venueReservationTimeId: [this.currentPeriod.venueReservationTimeId],
        ruleTimeId: [this.currentPeriod.reservationRuleTimeId],
      };
    },
    canModify() {
      return PeriodProcessStatus.RESERVED === this.currentPeriod.status
        && this.currentPeriod.isAdmin
        && this.currentPeriod.isBelong;
    },
    hasSelected() {
      const selectItem = this.selectedTimeList.filter(
        (item) => item.venueReservationTimeId === (this.currentPeriod || {}).venueReservationTimeId
          && (this.currentPeriod || {}).reservationRuleTimeId === item.reservationRuleTimeId,
      );
      return selectItem.length > 0;
    },
  },

  methods: {
    onClickReserve() {
      if (this.canReserve()) {
        if (this.canModify) {
          this.isEdit = true;
          this.isShowModalReserve = true;
        } else if (PeriodProcessStatus.AVAILABLE === this.currentPeriod.status) {
          this.isEdit = false;
          // this.isShowModalReserve = true;
          const payload = {
            time: this.currentPeriod,
            venue: this.venues[this.columnIndex - 1],
            hasSelected: this.hasSelected,
          };
          this.$eventBus.$emit('venue-reserve-select', payload);
        }
      } else {
        this.$message.error(this.$t('venueCalendar.modalReserve.authError'));
      }
    },
  },
};
</script>


<style lang="less" scoped>
.status-container {
  width: 100%;
  height: 100%;
  border: 2px solid #fff;
  &.hasSelected .status-available {
      border: 1px solid #8d0306;
  }
}
.status {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  color: fadeout(#000, 55%);
  font-size: 14px;
  font-weight: 300;

  &.status-available {
    // background: fadeout(#179e00, 90%);
    color: #4fad4e;
    font-weight: 400;
    cursor: pointer;
    .hasSelected.selected-icon {
      visibility: visible;
    }
    &:hover {
      border: 1px solid #8d0306;
      background-color: #fff;
      // .selected-icon {
      //   visibility: visible;
      // }
    }
  }

  &.status-reserved {
    background: fadeout(#8d0306, 90%);
    color: #8d0306;
    font-weight: 400;

    &.can-modify:hover {
      border: 1px solid #8d0306;
      cursor: pointer;
      // .selected-icon {
      //   visibility: visible;
      // }
    }
  }

  &.status-closed, &.status-expired, &.status-fixed-venue {
    background: rgba(0, 0, 0, 0.03);
    color: rgba(0,0,0, 0.65);
    font-weight: 400;
  }

  .status-wrap {
    display: -webkit-box;
    width: 100%;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    box-orient: vertical;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    span {
      display: -webkit-box;
      width: 100%;
      overflow: hidden;
      text-align: center;
      text-overflow: ellipsis;
      box-orient: vertical;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
}

.btn-reserve {
  color: #4fad4e;
}

.empty-holder {
  position: absolute;
  top: 2px;
  left: 2px;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  background: url("~@/assets/img/empty-cell.png") repeat center center / auto auto;
}
</style>
