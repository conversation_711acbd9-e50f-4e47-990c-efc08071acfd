@import './variables.less';
@import './antdv.less';


// @font-face {
//   font-family: 'iconfont';
//   src: url('../assets/font_2/iconfont.woff2?t=1621332884650') format('woff2'),
//        url('../assets/font_2/iconfont.woff?t=1621332884650') format('woff'),
//        url('../assets/font_2/iconfont.ttf?t=1621332884650') format('truetype');
// }
// .iconfont {
//   font-family: "iconfont" !important;
//   font-size: 16px;
//   font-style: normal;
//   -webkit-font-smoothing: antialiased;
//   -moz-osx-font-smoothing: grayscale;
// }

#app{
  overflow: hidden;
  word-break: break-word;
}

.blue{
  color: @mainColor;
}

.layout-header{
  height: @headerHeight;
  background-color: @mainColor;
  padding: 0;
  position: relative;
  //z-index: 199;
}

.page-layout-sider{
  background-color: #F9F9F9;
  border-right: 1px solid @defaultBorderColor;
}

.page-layout-sider {
  max-height: calc(100vh - 40px);
  /deep/ .menu{
    max-height: calc(100vh - 120px);
    overflow-x: hidden;
    overflow-y: auto;
    margin-bottom: 0;
    .ant-menu-item {
      padding-right: 0;
    }
    .title-label {
      vertical-align: middle;
      display: inline-block;
      max-width: calc(100% - 86px);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.page-panel {
  overflow: hidden;
}
.tree-panel {
  min-height: calc(100vh - @headerHeight);
  background-color: #fff;
  overflow: hidden;

  .page-header {
    border-bottom: 1px solid @defaultBorderColor;
    margin-right: -5px;
    margin-bottom: 0;
  }
}

.data-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.tree-panel--body {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - @headerHeight - @pageHeaderHeight);
  // min-height: 200px;
  .ant-tree {
    overflow: hidden;
    padding-right: 10px;
  }
}

.content-panel {
  height: calc(100vh - @headerHeight);
  overflow: auto;

  .panel-body {
    background-color: #fff;
    height: calc(100vh - @headerHeight - @pageHeaderHeight);
    overflow: auto;
    padding: 0 24px;
  }
  .footer_btns {
    position: fixed;
    bottom: 0;
    left: @sideBarWidth;
    width: 100%;
    height: 75px;
    padding-left: 38%;
    line-height: 75px;
    background: #fff;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);
    z-index: 9;
    .ant-btn {
      height: @bigBtnHeight;
      line-height: @bigBtnHeight;
      font-size: 14px;
    }
  }
}

.content-side{
  border-right: 1px solid @defaultBorderColor;
}

.content-tip{
  padding: 20px 0;
}

.info-list{
}
.info-list .info-list--items{
  display: block;
}
.info-list li{
  list-style: none;
  padding: 12px 0;
}
.info-list--label{
  text-align: right;
  color: @titleColor;
}

.pima-confrim{
  // .ant-modal{
  //   width: 288px !important;
  // }
  // .ant-modal-confirm-body{
  //   text-align: center;
  //   > .anticon{
  //     float: none;
  //     display: block;
  //     margin: 0 auto 12px;

  //     line-height: 1;
  //     font-family: "iconfont" !important;
  //     font-size: 44px;
  //     font-style: normal;
  //     color: #f2890f;
  //     &::after {
  //       content: "\e63c";
  //     }
  //     svg{
  //       display: none;
  //     }
  //     &+.ant-modal-confirm-title{
  //       &+ .ant-modal-confirm-content{
  //         margin-left: 0;
  //       }
  //     }
  //   }
  //   .ant-modal-confirm-title{
  //     font-size: 16px;
  //     font-weight: normal;
  //     white-space: pre-line;
  //   }
  //   .ant-modal-confirm-content{
  //     font-size: 12px;
  //   }
  // }
  // .ant-modal-confirm-btns{
  //   float: none;
  //   text-align: center;
  //   .ant-btn:first-child{
  //     position: absolute;
  //     right: 5px;
  //     top: 0;
  //     border: none;
  //     background: url(../assets/img/ico_close.png) no-repeat 50% 50%;
  //     box-shadow: none;
  //     padding: 0 15px;
  //     span{
  //       display: none;
  //     }
  //   }
  //   .ant-btn-primary{
  //     width: 90px;
  //     height: @btnHeight;
  //     background-color: #F2890F;
  //     border-color: #F2890F;
  //     font-size: @btnFontSize;
  //   }
  // }

  // &.ant-modal-success{
  //   .ant-modal-confirm-body{
  //     > .anticon{
  //       color: @mainColor;
  //       &::after {
  //         content: "\e64c";
  //       }

  //     }
  //   }
  //   .ant-modal-confirm-btns{
  //     .ant-btn:first-child{
  //       width: auto;
  //     }
  //   }
  // }
  // &.ant-modal-error{
  //   .ant-modal-confirm-body{
  //     > .anticon{
  //       color: #B13F3F;
  //       &::after {
  //         content: "\e63d";
  //       }

  //     }
  //   }
  //   .ant-modal-confirm-btns{
  //     .ant-btn:first-child{
  //       width: auto;
  //     }
  //   }
  // }


  /* start---垂直居中展示 Modal */
  .ant-modal-wrap  {
    text-align: center;
  }
  .ant-modal-wrap::before {
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
    content: '';
  }
  .ant-modal-wrap .ant-modal {
    top: 0;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
  }
  /* end---垂直居中展示 Modal */
}


.no-uploadlist{
  margin: 0;
  .ant-upload-list {
    display: none;
  }
}

.status_success{ color: #52C41A;}
.status_warning{}
.status_error{ color: #FF4D4F;}

.action_link{
  padding: 0 2px;
}

.drawer-bd .ant-form{
  padding: 12px 0;
}

.list-filter-2{
  padding: 5px 0;
  font-size: @formLabelFontSize;
  .ant-form-inline .ant-form-item{
    margin-right: 8px;
  }
  .ant-form label{
    font-size: @formLabelFontSize;
  }
  .ant-input{
    height: @btnHeight;
    font-size: @formLabelFontSize;
  }
  .ant-select{
    font-size: @formLabelFontSize;
  }
  .ant-select-selection--single{
    height: @btnHeight;
  }
  .ant-select-selection__rendered{
    line-height: @btnHeight - 4px;
  }
  .submit{
    margin-left: 8px;
  }
}


.drawer-ft {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid @defaultBorderColor;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}
.drawer_footer{
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid @defaultBorderColor;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  border-radius: 0 0 4px 4px;
}

.modal-wrap{
  .ant-modal-close-x{
    width: 36px;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
  }
  .ant-modal-header{
    padding: 7px 16px;
    border-color: @defaultBorderColor;
    .ant-modal-title{
      font-size: @commonTitleFontSize;
      font-weight: bold;
    }
  }
  .ant-modal-body{
    padding: 0;
    // padding-bottom: 44px;
    .drawer-bd{
      height: 100%;
      padding: 16px 16px 74px 16px;
    }
  }
  .drawer-side{
    height: 100%;
    min-height: 100%;
    overflow: auto;
    .ant-tree{
      max-height: 100%;
    }
    .tree-panel--body{
      overflow: hidden;
    }
  }
  .drawer-main{
    height: 100%;
    overflow: auto;
  }
  &.modal-wrap-post-select{
    .ant-modal{
      .transfer-list{
        .box-main{
          min-height: 100px;
          height: calc(100vh - 240px);
        }
      }
      .transfer-target{
        .box-main{
          min-height: 100px;
          height: calc(100vh - 240px);
        }
      }
    }
  }
  &.modal-wrap-height{
    .ant-modal{
      .ant-modal-body{
        min-height: 100px;
        max-height: calc(100vh - 120px);
      }
    }
  }
  &.modal-wrap-scroll{
    .ant-modal-body{
      overflow: auto;
    }
  }
  &.modal-np{
    .ant-modal-body{
      .drawer-bd{
        padding: 0;
      }
    }
  }
}

.font-12{
  &.ant-select{
    font-size: 12px;
  }
  .ant-select-dropdown-menu-item{
    font-size: 12px;
  }
}

.select_in_modal{
  position: relative;
  display: block;
}
.auth-collapse{
  .ant-collapse-content{
    overflow: visible;
  }
}


.high-search {
  margin: 0 0 10px;
  padding: 16px 24px;
  border-bottom: 1px solid @defaultBorderColor;
  background: @grayBg;
  .high-search-form{
    margin: 0 auto;
    max-width: 1200px;
  }
  .subbox{
    margin-top: 8px;
  }
  .ant-btn {
    margin: 0 5px;
  }
}


.mobile-view {
  display: none;
}
@media (max-width: 768px) {
  .PC-view {
    display: none;
  }
  .mobile-view {
    display: block !important;
  }
  .layout-header, .page-layout-sider {
    display: none;
  }
}



/* ant css edit*/
a{
  // color: hsv(@link-hsvhue, @link-hsvsaturation, @link-hsvvalue);
  color: @mainColor;
}
a:hover, a:focus{
  // color: hsv(@link-hsvhue, @link-hsvsaturation, (@link-hsvvalue + @link-hover));
  color: @mainColor;
}
a:active{
  // color: hsv(@link-hsvhue, @link-hsvsaturation, (@link-hsvvalue + @link-active));
  color: @mainColor;
}

td a{
  color: @mainColor;

  &:hover, &:active, &:focus{
    color: @mainColor;
  }
}

.goback {
  display: flex;
  align-items: center;
  .icon {
    display: inline-flex;
    align-items: center;
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    justify-content: center;
    border: 1px solid #d0d0d0;
    margin-right: 12px;
    cursor: pointer;
    .left {
      width: 5px;
      height: 5px;
      border-top: 1px solid #d0d0d0;
      border-left: 1px solid #d0d0d0;
      transform: rotate(-45deg);
    }
  }
}

.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon
,.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon, .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon {
  color: @mainColor;
}
.popover-help.ant-popover {
  .ant-popover-content {
    .ant-popover-inner-content {
      padding-left: 16px;
      padding-right: 16px;
      background-color: #fff;
      line-height: 16px;
      div {
        width: auto !important;
      }
    }
  }
}
.popover-help.ant-popover .ant-popover-arrow {
  background-color: #fff;
}


.approval-status-box {
  padding: 9px 22px;
  margin-top: 1px;
  display: flex;
  justify-content: space-between;
  background-color: #FCFCFC;
  .status-icon {
    display:inline-block;
    width: 22px;
    height: 22px;
    background-size: 100%;
    vertical-align: middle;
    background: url('../assets/img/ico_status_pending.png') no-repeat;
    &.pending {
      background: url('../assets/img/ico_status_pending.png') no-repeat;
    }
    &.pass {
      background: url('../assets/img/ico_status_pass.png') no-repeat;
    }
    &.reject {
      background: url('../assets/img/ico_status_reject.png') no-repeat;
    }
    &.cancel {
      background: url('../assets/img/ico_status_cancel.png') no-repeat;
    }
  }
  .approve-status {
    font-size: 16px;
    font-weight: 600;
    color: #9B0000;
    vertical-align: middle;
    &.pending {
      color: #F49B00;
    }
    &.pass {
      color: #009944;
    }
    &.reject {
      color: #9B0000;
    }
    &.cancel {
      color: rgba(0,0,0,0.45);
    }
  }
  .apply-sn {
    font-size: 12px;
    color: #555555;
    line-height: 25px;
  }
}
// 解决input 输入框 在自动填充 出现背景色的问题
input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px #fff inset !important;
 }