<template>
  <ul
    theme="dark"
    mode="horizontal"
    class="ant-menu ant-menu-horizontal"
    :inline-indent="22"
    :default-selected-keys="defaultSelectedKeys"
    :selected-keys="defaultSelectedKeys"
  >
    <li
      v-for="item in menu"
      :key="item.key"
      :class="item.key === defaultSelectedKeys[0] ? 'ant-menu-item-selected ant-menu-item' : 'ant-menu-item'"
      :selectable="false"
    >
      <a-badge :dot="!!item.badge">
        <a
          :href="item.url"
          :target="item.target || '_self'"
          @click="handleClick($event, item)"
        >
          {{ item.title }}
        </a>
      </a-badge>
    </li>
  </ul>
</template>

<script>
export default {
  name: 'AppHeaderNavigator',
  components: {
  },
  props: {
    // 菜单内容
    menu: {
      type: Array,
      default() {
        return [];
      },
    },
    // 默认选中项
    defaultSelectedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    // 菜单应用项键值
    applicationKey: {
      type: String,
      default: '',
    },
  },
  methods: {
    handleClick(ev, { key }) {
      // ev.preventDefault();

      if (key === this.applicationKey) {
        this.$emit('click-application');
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="less" scoped>
.menu {
  line-height: 39px;
  font-size: 16px;
  border-bottom: none;
  overflow: hidden;

  .ant-menu {
    line-height: 39px;
    font-size: 14px;
    list-style: none;
    background: none;
    display: flex;
    justify-content: flex-start;
    border-bottom: none;

    ::v-deep li {
      min-width: 80px;
      text-align: center;
      margin: 0;
      padding: 0 20px;
      height: 39px;
      white-space: nowrap;
      a {
        color: #fff;
      }
    }
  }
}

.menu .ant-menu ::v-deep li {
  &.ant-menu-item-selected {
    position: relative;
    // background-color: #2a6bde;

    &::after {
      content: ' ';
      width: 100%;
      height: 2px;
      background-color: #1F65E0;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
}
/deep/ .ant-badge-dot {
  right: -4px;
  box-shadow: 0 0 0 0px #fff;
}
/deep/ .ant-badge-count {
  height: 11px;
  line-height: 10px;
  min-width: 10px;
  right: -10px;
  box-shadow: none;
  padding: 0 3px;
}
</style>
