import request from './request';

// 特殊日期列表
export function specialDayListApi(params) {
  return new Promise((resolve, reject) => {
    request({
      url: 'special-dates',
      method: 'get',
      params,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 新增
export function addSpecialDayApi(data) {
  return new Promise((resolve, reject) => {
    request({
      url: 'special-dates',
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 修改
export function editSpecialDayApi({ id, data }) {
  return new Promise((resolve, reject) => {
    request({
      url: `special-dates/${id}`,
      method: 'post',
      data,
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 删除
export function delSpecialDayApi({ id }) {
  return new Promise((resolve, reject) => {
    request({
      url: `special-dates/delete/${id}`,
      method: 'post',
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}

// 详情
export function specialDayDetailApi({ id }) {
  return new Promise((resolve, reject) => {
    request({
      url: `special-dates/${id}`,
      method: 'get',
    }).then((resp) => {
      resolve(resp);
    }).catch((err) => {
      reject(err);
    });
  });
}
