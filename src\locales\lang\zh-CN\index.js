import appHeader from './app-header';
import applicationList from './application-list';
import constants from './constants';
import user from './user';
import basicData from './basic-data';
import reserveQuery from './reserve-query';
import reserveRule from './reserve-rule';
import venueCalendar from './venue-calendar';
import venueTimeSetting from './venue-time-setting';
import special from './special';
import blacklist from './blacklist';
import userNotes from './user-notes';
import sign from './sign';
import integralManagement from './integral-management';

export default {
  title: '场馆预约',
  enableJavaScriptTips: '很抱歉，如果不启用JavaScript，网站将无法正常运行。请开启后继续。',
  forbiddenTips: '很抱歉，您无权访问此应用',
  common: {
    YES: '是',
    NO: '否',
    all: '全部',
  },

  action: {
    close: '关闭',
    ok: '确认',
    cancle: '取消',
    edit: '编辑',
    del: '删除',
    add: '添加',
    search: '搜索',
    clear: '清空',
    import: '导入',
    export: '导出',
    view: '查看',
    save: '保存',
    reset: '重置',
    highSearch: '高级搜索',
    upload: '上传',
    submit: '提交',
    copy: '复制',
    set: '设置',
    moveout: '移出',
    verify: '签到核销',
    modify: '修改',
  },
  pagination: {
    total: '共{total}项',
    totalLong: '共有{totalPage}页，{total}条记录',
    totalTips: '共有{totalNumberOfPages}页，{totalNumberOfRecords}条记录',
    prev: '上一页',
    next: '下一页',
  },
  msg: {
    operateSuccess: '操作成功',
    uploading: '上传中...',
    loading: '处理中...',
    textTip: '点击左侧菜单可查看详情',
    permanentDel: '此操作将永久删除，是否继续？',
    delSucc: '删除成功',
    startTime: '开始时间应早于结束时间',
    endTime: '结束时间应晚于开始时间',
    imgType: '上传图片应为jpeg,jpg,png格式',
    imgSize: '图片需小于2MB!',
    confirmDel: '确认是否删除？',
    imgSizeFive: '图片需小于5MB!',
    fileTypeMsg: '文件应为 {types} 格式!',
    fileSizeMsg: '文件大小需小于{size}MB!',
    contentSoLongWarn: '内容过长...',
    emtryData: '数据为空',
  },
  hint: {
    required: '此项为必填',
    dataSaved: '保存成功',
    savingFailed: '保存失败',
  },

  pimaUI: {
    appHeader,
    applicationList,
  },
  form: {
    maxLength: '不能超过{num}个字符',
  },
  constants,
  user,
  basicData,
  reserveRule,
  reserveQuery,
  venueCalendar,
  venueTimeSetting,
  special,
  blacklist,
  userNotes,
  sign,
  integralManagement,
};
