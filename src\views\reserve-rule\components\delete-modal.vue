<template>
  <PopModal
    :title="t('title.deleteTitle')"
    :width="450"
    :visible.sync="realValue"
    @close="onClose"
  >
    <div class="drawer-bd">
      <p v-html="label" />
      <div class="clearfix drawer-ft">
        <a-button @click="onClose">
          {{ $t('action.close') }}
        </a-button>
        <a-button
          type="primary"
          :loading="submiting"
          @click="onSubmit"
        >
          {{ $t('action.ok') }}
        </a-button>
      </div>
    </div>
  </PopModal>
</template>


<script>
import PopModal from '@/components/base/pop-modal.vue';
import { nsI18n } from '@/mixins/ns-i18n';


export default {
  name: 'ModalCancel',
  components: {
    PopModal,
  },

  mixins: [
    nsI18n('t', 'reserveRule'),
  ],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    submiting: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
  },

  data() {
    this.form = this.$form.createForm(this);
    return {
      realValue: this.value,
    };
  },

  watch: {
    async value(val) {
      if (val !== this.realValue) {
        this.realValue = val;
      }
    },

    realValue(val) {
      this.$emit('input', val);
    },
  },

  methods: {
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            ...values,
          };
          this.$emit('handle-delete-submit', payload);
        }
      });
    },

    onClose() {
      this.realValue = false;
    },
  },
};
</script>
<style scoped>
.name {
}
</style>
