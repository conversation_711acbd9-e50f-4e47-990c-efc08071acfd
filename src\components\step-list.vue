<template>
  <div>
    <h2 class="title">
      {{ t('stepList') }}
    </h2>
    <a-steps
      v-if="processSteps && processSteps.logList?.length"
      direction="vertical"
      progress-dot
      :current="processSteps.logList?.length"
      class="process-steps"
    >
      <a-step
        v-for="(item, index) in processSteps.logList"
        :key="index"
      >
        <template #title>
          <!-- 操作人 -->
          <span class="operation-title">
            {{ item.operatorName }}
          </span>
        </template>
        <template #description>
          <!-- 操作 -->
          <span
            v-if="item.action"
            class="operation-name"
          >
            <span v-if="OrderTypeMap.COMMON===type && item.action==='signed'">
              {{ t('arrived') }}
            </span>
            <span v-else>
              {{ t(actionMapper(item.action)) }}
            </span>
          </span>
          <!-- 时间 -->
          <span
            class="operation-time"
          >
            {{ formatDate(item.operationTime, 'middle') }}
          </span>
          <br>
          <!-- 备注 -->
          <span
            v-if="item.remark"
            class="operation-remark"
          >
            {{ t('remark') }}
            {{ item.remark }}
          </span>
        </template>
      </a-step>

      <!-- 即将审批 -->
      <a-step v-if="nextApproverName">
        <template #title>
          <span class="operation-title">
            {{ t('nextApprover') }}
          </span>
        </template>
        <template #description>
          <span class="approver-name">
            {{ nextApproverName }}
          </span>
        </template>
      </a-step>
    </a-steps>
  </div>
</template>

<script>
import { formatDate } from '@utils/dateformat';
import { nsI18n } from '@/mixins/ns-i18n';
import { OrderTypeMap } from '@/constants/venue';

export default {
  name: 'StepList',
  mixins: [
    nsI18n('t', 'reserveQuery.reserveDetail'),
  ],
  props: {
    processSteps: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      OrderTypeMap,
    };
  },
  computed: {
    nextApproverName() {
      if (this.processSteps
      && Array.isArray(this.processSteps.nextApproverDTO)
      && this.processSteps.nextApproverDTO.length) {
        return this.processSteps.nextApproverDTO.map((item) => item.name).join('、');
      }

      return null;
    },
  },
  methods: {
    formatDate,

    actionMapper(key) {
      const mapper = new Map([
        ['auto-pass', 'autoPass'],
      ]);
      return mapper.get(key) || key;
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 42px;
}

.process-steps {
  margin-left: 50px;
  width:280px;
}

::v-deep .ant-steps-dot .ant-steps-item-content {
  width: auto;
}
.operation-title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  line-height: 25px;
}
.operation-name {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  line-height: 20px;
}
.operation-time {
  font-size: 12px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.45);
  line-height: 20px;
  margin-left: 10px;
}
.operation-remark,
.approver-name {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  line-height: 20px;
}
</style>
