<template>
  <div>
    <div class="personal-top">
      <div
        v-if="showPersonalType === PERSONAL_TYPES.ACCOUNT"
        class="account-select"
      >
        <a-select
          :key="PERSONAL_TYPES.ACCOUNT"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          :options="persons"
          :get-popup-container="(triggerNode) => triggerNode.parentNode"
          label-in-value
          style="width: 100%"
          mode="multiple"
          placeholder="你可以输入工号、姓名、系统账号"
          @change="(keys) => changeSelect(keys, 'id')"
          @search="(value) => debounce(fetchUsers)(value)"
          @dropdownVisibleChange="changeDropdownVisible"
        >
          <a-spin
            v-if="fetching"
            slot="notFoundContent"
            size="small"
          />
        </a-select>
      </div>
      <div class="select-list-wrap">
        <div class="select-list">
          <span
            v-for="(item, index) in selectedUsers"
            :key="item.key + index"
            class="list-item"
          >
            {{ item.label }}
            <span
              class="delete-person-btn"
              @click="removeOneUser(item)"
            />
          </span>
        </div>
        <div class="list-action">
          <span
            v-if="selectedUsers.length"
            @click="clearUsers"
          >
            清空
          </span>
        </div>
      </div>
    </div>
    <div>
      <div
        v-if="taskIds.length"
        class="personal-files-wrap"
      >
        <div class="files-header">
          已导入名单
        </div>
        <div class="files-list">
          <div
            v-for="(item, index) in taskIds"
            :key="item.filename + index + Math.random() * 7 + 3"
            class="file-item"
          >
            <div class="icon iconfont .icon-wenjian" />
            <div class="filename">
              {{ item.fileName }}
            </div>
            <span
              class="delete-btn"
              @click="removeTaskId(item.id)"
            >
              删除
            </span>
          </div>
        </div>
      </div>
    </div>

    <pima-ui-import
      :config="config"
      :import-url="importUrl"
      :template-url="templateUrl"
      :template-file-name="templateFileName"
      :base-url="config.appApiBaseUrl"
      :allowed-types="allowedTypes"
      :initial-notices="initialNotices"
      :visible="visibleImport"
      @update:visible="(val) => $emit('update:visibleImport', val)"
      @success="handleImportSuccess"
      @error="handleImportError"
      @warn="handleImportWarn"
    />
  </div>
</template>

<script>
import Select from 'ant-design-vue/lib/select';
import Radio, { Group } from 'ant-design-vue/lib/radio';
import { searchUserApi } from '../../../api/unidata-api';

// 个人选项类型
const PERSONAL_TYPES = {
  ACCOUNT: 'ACCOUNT',
  STU: 'STU',
};

let timeout;
/**
 * 事件防抖
 * @param func: Function 执行函数
 * @param wait?: Number 事件间隔
 * @return {(function(): void)|*}
 */
function debounce(func, wait = 500) {
  /* eslint-disable-next-line */
  return function (...args) {
    const ctx = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(ctx, args);
    }, wait);
  };
}

function mergeSelectedUser(oldUserList = [], newUserList = []) {
  if (!oldUserList.length) return [...newUserList];
  if (!newUserList.length) return [...oldUserList];
  const rs = [...oldUserList];
  newUserList.forEach((n) => {
    const [hasExisted] = oldUserList.filter((o) => o.key === n.key);
    if (!hasExisted) rs.push(n);
  });
  return rs;
}

export default {
  name: 'SelectPersonal',

  components: {
    [Select.name]: Select,
    [Select.Option.name]: Select.Option,
    [Group.name]: Radio.Group,
    [Radio.name]: Radio,
  },

  props: {
    checked: {
      type: Object,
      default: () => ({
        users: [],
        taskIds: [],
      }),
    },
    personalData: {
      type: Array,
      default: () => [],
    },
    defaultPersonalKeys: {
      type: Array,
      default: () => [],
    },
    visibleImport: {
      type: Boolean,
      default: false,
    },
    importUrl: {
      type: String,
      default: '',
    },
    templateUrl: {
      type: String,
      default: '',
    },
    templateFileName: {
      type: String,
      default: '',
    },
    allowedTypes: {
      type: Array,
      default: () => ['xls', 'xlsx'],
    },
    initialNotices: {
      type: Array,
      default: () => [
        '下载导入模板，填写用户信息，批量新建用户',
        '上传填好的文件，仅支持xls、xlsx格式文件',
      ],
    },
    config: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      PERSONAL_TYPES,
      showPersonalType: PERSONAL_TYPES.ACCOUNT,
      persons: [],
      taskIds: [], // 上传的名单id s => {id, fileName}
      selectedKeys: [],
      selectedUsers: [],
      fetching: false,
    };
  },

  mounted() {
    this.selectedUsers.push(...this.defaultPersonalKeys);
    this.emitSyncPersons();
  },

  methods: {
    debounce,
    fetchUsers(value) {
      if (!value || !value.trim()) return;
      this.fetching = true;
      searchUserApi(value)
        .then(({ model }) => {
          this.persons = (model || []).map((m) => ({
            label: m.name,
            key: m.id,
          }));
          this.fetching = false;
        })
        .catch((err) => {
          this.fetching = false;
          this.$message.error(err.errorList.message);
          // this.$error({
          //   title: err.errorList.message,
          //   class: 'pima-confrim pima-confrim-error',
          // });
        });
    },
    changeDropdownVisible(open) {
      if (!open) {
        this.persons = [];
      }
    },
    /**
     * select 改变选择
     * @param newUsers
     */
    changeSelect(newUsers) {
      // this.persons = [];
      this.selectedKeys = newUsers;
      this.selectedUsers = [...mergeSelectedUser(this.selectedUsers, newUsers)];
      this.emitSyncPersons();
    },
    /**
     * 删除某个已选中的用户
     * @param user: {object}
     */
    removeOneUser(user) {
      this.selectedUsers = this.selectedUsers.filter((item) => item !== user);
      this.emitSyncPersons();
    },
    /**
     * 清空选中的pesonal select
     */
    clearUsers() {
      this.selectedUsers = [];
      this.emitSyncPersons();
    },
    handleImportSuccess(status, res, file) {
      const { model } = res;
      if (model) {
        this.taskIds.push({
          id: model,
          fileName: file.name,
        });
      }
      this.emitSyncPersons();
      this.showImport = false;
    },
    handleImportError(status, res) {
      this.$message.error(res.errorList.message);
      // this.$error({
      //   title: '500',
      //   content: res.errorList.message,
      //   class: 'pima-confrim pima-confrim-error',
      // });
    },
    handleImportWarn(msg) {
      this.$confirm({
        title: msg,
        class: 'pima-confrim',
        onOk() {},
      });
    },
    /**
     * 改变person选择类型 Radio
     * @param e
     */
    changePersonalType(e) {
      this.selectedKeys = [];
      this.showPersonalType = e.target.value;
    },
    /**
     * 删除上传的名单
     * @param id: {number} 名单ID
     */
    removeTaskId(id) {
      this.taskIds.splice(this.taskIds.map((item) => item.id).indexOf(id), 1);
      this.emitSyncPersons();
    },
    /**
     * 过滤搜索 select自定义
     * @param input: {string} 用户输入内容
     * @param option: {Object} 节点数据
     * @param keywords: {string} 筛选目标
     * @return {boolean}
     */
    filterOptionsFn(input, option, keywords) {
      if (!input || !input.length) return true;
      const field = option.data[keywords];
      return field.toLowerCase().indexOf(input.toLowerCase()) > -1;
    },
    /**
     * 同步选中的Personal数据
     */
    emitSyncPersons() {
      this.$emit('update:checked', {
        users: [...this.selectedUsers.map((u) => ({
          key: u.key,
          id: u.key,
          label: u.label,
          name: u.label,
        }))],
        taskIds: this.taskIds.map((item) => item.id),
      });
    },
  },
};
</script>

<style>
@import './index.less';
</style>
