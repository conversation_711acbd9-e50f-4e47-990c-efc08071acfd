<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.IntegralManagement.View)"
      class="content-panel"
    >
      <a-page-header
        :ghost="false"
      >
        <SimpleSearch
          @on-search="onSearch"
        />
        <template
          v-if="hasP(P => P.IntegralManagement.Setting)"
          #extra
        >
          <a-button
            type="primary"
            size="small"
            @click="toPage"
          >
            {{ t('action.ruleSetting') }}
          </a-button>
        </template>
      </a-page-header>

      <div class="panel-body">
        <TableList
          :scroll="tableScroll"
          @on-detail="onDetail"
          @on-adjust="onAdjust"
        />
      </div>
    </div>

    <AdjustModal
      ref="adjustModalRef"
      :payload="payload"
      :submitting="adjustModel.loading"
      @on-submit="onAdjustSubmit"
    />
  </div>
</template>


<script>
import { mapState, mapActions } from 'vuex';
import OperationMixin from '@/mixins/operation';

import { adjustIntegralApi } from '@/api/integral-management';
import { namespaceT } from '@/helps/namespace-t';
import { RouteName as RN } from '@/constants/route';
import { createModelForAdjust } from './hooks/model';


import SimpleSearch from './components/simple-search.vue';
import TableList from './components/table-list.vue';
import AdjustModal from './components/adjust-modal.vue';

export default {
  name: 'IntegralManagementList',

  components: {
    SimpleSearch,
    TableList,
    AdjustModal,
  },

  mixins: [OperationMixin],
  data() {
    return {
      t: namespaceT('integralManagement'),
      adjustModel: this.createModelForAdjust(),
    };
  },
  computed: {
    ...mapState({
      dataSource: (state) => state.integralMgmt.dataSource,
    }),
    tableScroll() {
      return { y: 'calc(100vh - 214px)' };
    },

    payload() {
      if (this.adjustModel.id) {
        return this.dataSource.find((item) => item.id === this.adjustModel.id);
      }

      return {};
    },
  },

  mounted() {
    this.fetchIntegralManagementList();
  },

  methods: {
    createModelForAdjust,
    ...mapActions({
      fetchIntegralManagementList: 'integralMgmt/fetchIntegralManagementList',
    }),

    onDetail(row) {
      this.$router.push({
        name: RN.IntegralDetails,
        params: {
          id: row.id,
        },
      });
    },

    onAdjust(row) {
      this.adjustModel.id = row.id;
      this.$refs.adjustModalRef.onToggleVisible(true);
    },

    onSearch(payload) {
      this.fetchIntegralManagementList(payload);
    },

    async onAdjustSubmit(payload) {
      try {
        this.adjustModel.loading = true;

        await adjustIntegralApi(payload);

        this.$refs.adjustModalRef.onToggleVisible(false);
        this.$message.success(this.t('hint.saveSuccessfully'));
        this.onSearch();
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.adjustModel.loading = false;
      }
    },

    toPage() {
      this.$router.push({
        name: RN.IntegralRuleSetting,
      });
    },
  },

};
</script>


<style lang="less" scoped>
:deep(.ant-page-header) {
  min-height: 50px;
  height: auto;
}

:deep(.ant-page-header-content) {
  padding-top: 0;
  overflow: visible;
  padding-right: 120px;
}

:deep(.ant-page-header-heading) {
  position: absolute;
  right: 0;
  padding-right: 24px;
}

:deep(.ant-form-inline .ant-form-item) {
  margin: 7px 8px 7px 0;
}

</style>
