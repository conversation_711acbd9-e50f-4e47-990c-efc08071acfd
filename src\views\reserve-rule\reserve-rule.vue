<template>
  <div class="page-panel">
    <div
      v-if="hasP(P => P.ReserveRule.View)"
      class="content-panel"
    >
      <!--头部-->
      <a-page-header
        :ghost="false"
      >
        <template #title>
          <!--查看详情-->
          <template
            v-if="contentType !== SHOW_CONTENT_TYPE.VIEW"
          >
            <GoBack
              @click.native="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
            />
            {{ headerTitle }}
          </template>
        </template>

        <SimpleSearch
          v-show="contentType === SHOW_CONTENT_TYPE.VIEW"
          ref="SimpleSearch"
          @reset-search="resetSearch"
          @handle-search="handleSearch"
        />

        <template
          #extra
        >
          <!--基础数据头部-->
          <template v-if="contentType === SHOW_CONTENT_TYPE.VIEW">
            <a-button
              v-if="hasP(P => P.ReserveRule.Add)"
              type="primary"
              size="small"
              @click="handleShowContentType(SHOW_CONTENT_TYPE.ADD)"
            >
              {{ $t('reserveRule.title.add') }}
            </a-button>
          </template>

          <!--新增场馆头部-->
          <!-- <template v-if="contentType === SHOW_CONTENT_TYPE.ADD">
            <a-button
              type="primary"
              size="small"
              @click="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
            >
              {{ $t('reserveRule.action.cancel') }}
            </a-button>
            <a-button
              type="primary"
              size="small"
              @click="$refs.AddRule.handleSubmit()"
            >
              {{ $t('action.submit') }}
            </a-button>
          </template> -->

          <!--编辑场馆头部-->
          <!-- <template v-if="contentType === SHOW_CONTENT_TYPE.EDIT">
            <a-button
              type="primary"
              size="small"
              @click="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
            >
              {{ $t('reserveRule.action.cancel') }}
            </a-button>
            <a-button
              type="primary"
              size="small"
              @click="$refs.EditRule.handleSubmit()"
            >
              {{ $t('action.submit') }}
            </a-button>
          </template> -->
        </template>
      </a-page-header>

      <div
        class="panel-body"
        :class="contentType === SHOW_CONTENT_TYPE.VIEW?'':'pb-80'"
      >
        <!--基础数据查看-->
        <template
          v-if="contentType === SHOW_CONTENT_TYPE.VIEW"
        >
          <TableList
            @edit-rule="onEditRule"
            @delete-rule="onDeleteRule"
            @period-setting="onPeriodSetting"
          />
        </template>
        <!-- 新增规则页面 -->
        <template v-if="contentType === SHOW_CONTENT_TYPE.ADD">
          <AddRule
            ref="AddRule"
            :submiting="formLoading"
            :init-data="{}"
            :applicable-venues="applicableVenues"
            @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
            @handle-add-submit="handleAddSubmit"
          />
          <div class="footer_btns">
            <div class="align-center-btns">
              <a-button
                type="default"
                @click="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
              >
                {{ $t('reserveRule.action.cancel') }}
              </a-button>
              <a-button
                type="primary"
                @click="$refs.AddRule.handleSubmit()"
              >
                {{ $t('action.submit') }}
              </a-button>
            </div>
          </div>
        </template>
        <!-- 编辑规则页面 -->
        <template v-if="contentType === SHOW_CONTENT_TYPE.EDIT">
          <EditRule
            ref="EditRule"
            :row-id="rowId"
            :applicable-venues="applicableVenues"
            @handle-cancel-submit="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
            @handle-edit-submit="handleEditSubmit"
          />
          <div class="footer_btns">
            <div class="align-center-btns">
              <a-button
                type="default"
                @click="handleShowContentType(SHOW_CONTENT_TYPE.VIEW)"
              >
                {{ $t('reserveRule.action.cancel') }}
              </a-button>
              <a-button
                type="primary"
                @click="$refs.EditRule.handleSubmit()"
              >
                {{ $t('action.submit') }}
              </a-button>
            </div>
          </div>
        </template>
        <!-- 时段设置页面 -->
        <PeriodSetting
          ref="periodSetting"
          v-model="isShowSettingModal"
          :row-id="rowId"
        />
        <!-- 删除Modal -->
        <DeleteModal
          ref="CancelModal"
          v-model="isShowDeleteModal"
          :label="deleteLabel"
          :submiting="formLoading"
          @handle-delete-submit="handleDeleteRuleSubmit"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { hasOwn } from '@utils/core';
import { nsI18n } from '@/mixins/ns-i18n';
import operation from '@mixins/operation';
import {
  getVenuesListApi,
  addReservationRulesApi,
  updateReservationRulesApi,
  getReservationRulesDetailApi,
  deleteReservationRulesApi,
} from '@api/reserve-rule-api';
import GoBack from '@components/base/go-back.vue';

import SimpleSearch from './components/simple-search.vue';
import TableList from './components/table-list.vue';
import AddRule from './components/add-rule.vue';
import EditRule from './components/edit-rule.vue';
import PeriodSetting from './components/period-setting.vue';
import DeleteModal from './components/delete-modal.vue';

const SHOW_CONTENT_TYPE = {
  ADD: 'ADD', // 新增
  VIEW: 'VIEW', // 基础数据查看页
  EDIT: 'EDIT', // 编辑
  SETTING: 'SETTING', // 时段设置
};

export default {
  name: 'BasicData',

  components: {
    AddRule,
    EditRule,
    PeriodSetting,
    DeleteModal,
    GoBack,
    TableList,
    SimpleSearch,
  },
  mixins: [
    nsI18n('t', 'reserveRule'),
    nsI18n('td', 'reserveRule.deleteRule'),
    operation,
  ],

  data() {
    this.SHOW_CONTENT_TYPE = SHOW_CONTENT_TYPE;
    return {
      deleteLabel: '',
      isShowSettingModal: false,
      filter: {
        keyword: '',
        status: '',
      },
      isShowDeleteModal: false,
      // 动态适用场馆列表,
      applicableVenues: [],
      // 编辑规则初始数据
      initData: {},
      isAdvancedSearch: false,
      record: {},
      contentType: SHOW_CONTENT_TYPE.VIEW,
      modalType: '',
      rowId: 0,
      formLoading: false,
      activeRowRecord: {},
    };
  },

  computed: {
    ...mapState({
    }),
    headerTitle() {
      let title;
      switch (this.contentType) {
        case SHOW_CONTENT_TYPE.VIEW:
          title = this.$t('reserveRule.title.title');
          break;
        case SHOW_CONTENT_TYPE.ADD:
          title = this.$t('reserveRule.title.addTitle');
          break;
        case SHOW_CONTENT_TYPE.EDIT:
          title = this.$t('reserveRule.title.editTitle');
          break;
        case SHOW_CONTENT_TYPE.SETTING:
          title = this.$t('reserveRule.title.setting');
          break;
        default:
          title = this.$t('reserveRule.title.title');
          break;
      }
      return title;
    },
  },

  beforeMount() {
    // 获取选人组件数据
    this.fetchUserList({
      limit: -1,
    });
    this.fetchDepartmentTree({ treeApi: '' });
    // 获取场馆列表
    this.fetchVenuesListApi();
  },
  mounted() {
    // 重置菜单列表
    this.$eventBus.$on('reset-table', this.resetTable);
    const urlQuery = this.$route.query;
    Object.keys(urlQuery).forEach((k) => {
      if (hasOwn(this.filter, k)) this.filter[k] = urlQuery[k];
    });
    // 获取规则列表
    this.fetchReservationRulesList({
      page: urlQuery.page || 1,
      pageSize: urlQuery.pageSize || 10,
      filter: {
        ...this.filter,
      },
    });
  },

  methods: {
    resetTable() {
      this.handleShowContentType(SHOW_CONTENT_TYPE.VIEW);
      // 重新获取预约规则列表
      if (this.$refs.SimpleSearch) {
        this.$refs.SimpleSearch.resetSearch();
      }
    },
    ...mapActions({
      fetchReservationRulesList: 'reserveRule/fetchReservationRulesList',
      fetchDepartmentTree: 'uniData/fetchTreeData',
      fetchUserList: 'uniData/fetchUserList',
    }),
    resetSearch(filter) {
      this.fetchReservationRulesList(filter);
    },
    handleSearch(filter) {
      this.fetchReservationRulesList(filter);
    },
    // 提交新增表单
    handleAddSubmit(formData) {
      this.formLoading = true;
      addReservationRulesApi(formData)
        .then(() => {
          this.formLoading = false;
          if (formData.isNeedReserve) {
            this.$message.success(this.$t('reserveRule.msg.confirmAdd'));
          } else {
            this.$message.success(this.$t('reserveRule.msg.submitSuccess'));
          }
          this.closeModal();
          // 重新获取预约规则列表
          this.fetchReservationRulesList({});
          // 重新获取场馆列表
          this.fetchVenuesListApi();
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.formLoading = false;
        });
    },
    // 获取适用场馆列表
    fetchVenuesListApi() {
      getVenuesListApi()
        .then((res) => {
          this.applicableVenues = res.data;
        })
        .catch((error) => {
          this.$message.error(error.message);
        });
    },
    // 提交编辑表单
    handleEditSubmit(formData) {
      this.formLoading = true;
      updateReservationRulesApi(this.rowId, formData)
        .then(() => {
          this.formLoading = false;
          this.$message.success(this.$t('reserveRule.msg.submitSuccess'));
          this.closeModal();
          // 重新获取预约规则列表
          this.fetchReservationRulesList({});
          // 重新获取场馆列表
          this.fetchVenuesListApi();
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.formLoading = false;
        });
    },
    // 修改显示类型
    handleShowContentType(type, cb) {
      new Promise((resolve) => {
        const res = cb ? cb(this) : null;
        resolve(res);
      })
        .then(() => {
          this.contentType = type;
          this.formLoading = false;
        })
        .catch((error) => {
          this.$message.error(error.message);
          this.formLoading = false;
        });
    },
    // 监听编辑事件
    onEditRule(row) {
      this.formLoading = true;
      this.handleShowContentType(SHOW_CONTENT_TYPE.EDIT);
      const { id } = row;
      this.rowId = id;
    },
    // 监听打开删除modal
    onDeleteRule(row) {
      const { id } = row;
      this.rowId = id;
      getReservationRulesDetailApi(this.rowId)
        .then(({ model = {} }) => {
          if (model.venueList && model.venueList.length) {
            const tempVenueList = (model.venueList.map((item) => item.name)).join('、');
            this.deleteLabel = this.td('deleteNotice', { name: tempVenueList });
            this.isShowDeleteModal = true;
          } else {
            this.deleteLabel = this.td('noOrder');
            this.isShowDeleteModal = true;
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
          this.loading = false;
        });
    },
    // 监听打开时段设置
    onPeriodSetting(row) {
      this.$refs.periodSetting.submiting = true;
      const { id } = row;
      this.rowId = id;
      this.$refs.periodSetting.initRowId = id;
      this.isShowSettingModal = true;
    },
    // 监听删除规则
    handleDeleteRuleSubmit() {
      this.formLoading = true;
      const id = this.rowId;
      deleteReservationRulesApi(id)
        .then(() => {
          this.isShowDeleteModal = false;
          this.formLoading = false;
          this.$message.success(this.t('msg.success'));
          // 重新获取预约规则列表
          this.fetchReservationRulesList({});
          // 重新获取场馆列表
          this.fetchVenuesListApi();
        })
        .catch((err) => {
          this.$message.error(err.response.data.errorMsg);
          this.isShowDeleteModal = false;
          this.formLoading = false;
        });
    },
    // 关闭表单
    closeModal() {
      this.modalType = '';
      this.rowId = 0;
      this.activeRowRecord = {};
      this.formLoading = false;
      this.contentType = SHOW_CONTENT_TYPE.VIEW;
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ant-page-header-heading-title {
  display: flex;
  align-items: center;
}
::v-deep .ant-page-header-content {
  position: absolute;
  top: 7px;
  padding-top: 0;
  overflow: visible;
}
.panel-body.pb-80 {
  padding-bottom: 80px;
}
</style>
