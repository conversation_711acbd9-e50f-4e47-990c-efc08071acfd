// import fs from 'fs';
import path from 'path';
import Express from 'express';
import morgan from 'morgan';
import bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';
import redis from 'redis';
import connectRedis from 'connect-redis';
import session from 'express-session';
import Redlock from 'redlock';
import {
  CASAuthentication, 
  createCasTicketMiddlewares, 
  createTokenMiddlewares, 
  createUnlessMiddleware,
  createPublicPathFixMiddleware,
  createLoginStatusMiddleware,
} from '@doocom/pima-cas-middleware';
import createPimaProxyMiddlewares from '@doocom/pima-proxy-middleware';
import createPimaLocaleMiddleware from '@doocom/pima-locale-middleware';
import pimaVueSSRMiddleware from '@doocom/pima-vue-ssr-middleware';
import webpackClientConfig from '../build/webpack.client.config';
import webpackServerConfig from '../build/webpack.server.config';
import '../setup-dotenv';
import config from './config';
const cors = require('cors');
const request = require('request');
const saveTokenToCookie = require('./middlewares/save-token-to-cookie');
import { xXssProtectionMiddleware, setXssProtectionHeader } from './middlewares/x-xss-protection-header';

const isProd = process.env.NODE_ENV === 'production';
const resolve = (...args) => path.resolve(__dirname, ...args);
const app = new Express();


// Runtime日志
app.use(morgan('combined'));

// 如果使用HTTPS，需要设置X-Forwarded-Proto头，防止express-session中的Cookie解密失败
if (config.cookieSecure) {
  app.set('trust proxy', 1); // trust first proxy
  app.use((req, res, next) => {
    // eslint-disable-next-line no-param-reassign
    req.headers['x-forwarded-proto'] = 'https';
    next();
  });
}


const setXssProtectionHeaderOptions = { setHeaders: setXssProtectionHeader };
if (isProd) {
  app.use(`${config.publicPath}static/`, Express.static(resolve('../dist/static/'), {
    maxAge: config.staticMaxAge,
    ...setXssProtectionHeaderOptions
  }));
} else {
  app.use(Express.static(resolve('../src/assets'), setXssProtectionHeaderOptions));
}


// Cookie/Body parse
app.use(cookieParser());
app.use(bodyParser.urlencoded({ limit: '100mb', extended: false }));
app.use(bodyParser.json({ limit: '100mb' }));

app.get('*', xXssProtectionMiddleware);

app.use(cors());

// 修复根问题路径问题，如果访问地址为不带 / 的，自动重定向至带 / 的地址
const publicPathFixMiddleware = createPublicPathFixMiddleware({
  originPublicPath: process.env.PUBLIC_PATH || '/',
});
app.get('*', publicPathFixMiddleware);

const RedisStore = connectRedis(session);
const redisClientOptions = {
  host: config.redisHost,
};
if (config.redisPort) {
  Object.assign(redisClientOptions, { port: config.redisPort });
}
if (config.redisDB) {
  Object.assign(redisClientOptions, { db: config.redisDB });
}
if (config.redisPassword) {
  Object.assign(redisClientOptions, { password: config.redisPassword });
}
if (config.redisPrefix) {
  Object.assign(redisClientOptions, { prefix: config.redisPrefix });
}
const redisClient = redis.createClient(redisClientOptions);
redisClient.on('error', (err) => {
  // eslint-disable-next-line no-console
  console.error('Redis client error', err);
});

const redisStore = new RedisStore({
  client: redisClient,
  ttl: config.redisTTL, // Session有效期
});

// Set up an Express session, which is required for CASAuthentication.
const sessionOptions = {
  store: redisStore,
  secret: config.sessionSecret,
  resave: false,
  saveUninitialized: false,
  name: config.sessionIdCookieName,
};
app.use(session(sessionOptions));

// Create a new instance of CASAuthentication
const cas = new CASAuthentication({
  cas_url: config.casServiceBaseUrl,
  // cas_version: config.casServiceVersion,
  service_url: config.serviceUrl,
  session_info: 'cas_attributes',
});


// 在CAS认证之前，把Ticket与SessionID做对照，在CAS SLO时，执行删除
const casTicketMiddlewareOptions = {
  redisClient,
  casUserSessionName: cas.session_name,
};
const { casTicketSessionMapperMiddleware, casSloMiddleware } = createCasTicketMiddlewares(casTicketMiddlewareOptions);
app.use(casTicketSessionMapperMiddleware);
app.post('*', casSloMiddleware);

// 设置通过不执行中间件逻辑
const pass = (req) => {
  const whitelist = [
    `^${config.publicPath}favicon.ico$`, // favicon图标，浏览器会自动访问该地址
    `^${config.publicPath}static/\\S+$`, // 静态文件目录
    '^/app.js$', // DEV生成文件
    '^/\\S+.hot-update.js(on)?$', // 热更新文件
    '^/__webpack_hmr$', // 热更新文件
  ];
  let flag = 0;
  whitelist.forEach((rule) => {
    const reg = new RegExp(rule, 'g');
    if (reg.test(req.url)) {
      flag += 1;
    }
  });
  return flag > 0;
};

// 分布式锁
const redlock = new Redlock(
  // you should have one client for each independent redis node
  // or cluster
  [redisClient],
  {
    // the expected clock drift; for more details
    // see http://redis.io/topics/distlock
    driftFactor: 0.01, // multiplied by lock ttl to determine drift time

    // the max number of times Redlock will attempt
    // to lock a resource before erroring
    retryCount: 10,

    // the time in ms between attempts
    retryDelay: 200, // time in ms

    // the max time in ms randomly added to retries
    // to improve performance under high contention
    // see https://www.awsarchitectureblog.com/2015/03/backoff.html
    retryJitter: 200, // time in ms
  },
);

// Token中间件
const tokenMiddlewareOptions = {
  cookieSecure: config.cookieSecure,
  clientId: config.clientId,
  clientSecret: config.clientSecret,
  baseUrl: config.bdcCoreApiBaseUrl,
  casUserSessionName: cas.session_name,
  accessTokenCookieName: config.accessTokenCookieName,
  redisPrefix: config.redisPrefix,
  cookiePath: config.publicPath,
  redlock,
};
const {
  blockTokenMiddleware,
  renewTokenMiddleware,
  replaceRequestTokenMiddleware,
  removeTokenMiddleware,
} = createTokenMiddlewares(tokenMiddlewareOptions);
app.get(`${config.publicPath}logout`, removeTokenMiddleware, cas.logout);
app.use(createUnlessMiddleware(pass, blockTokenMiddleware)); // 当用户CAS身份失效时执行，返回401状态码
app.use(createUnlessMiddleware(pass, cas.bounce));
app.use(createUnlessMiddleware(pass, renewTokenMiddleware)); // 刷新Token
app.use(createUnlessMiddleware(pass, replaceRequestTokenMiddleware)); // 替换AJAX请求Token

// cas登录时保存token到cookie
const saveTokenToCookieMiddlewareOptions = {
  casUserSessionName: cas.session_name,
  accessTokenCookieKey: config.accessTokenCookieName,
  cookiePath: config.publicPath,
};
const saveTokenToCookieMiddleware = saveTokenToCookie(saveTokenToCookieMiddlewareOptions);
app.use(saveTokenToCookieMiddleware);

// 登录状态中间件
const loginStatusMiddleware = createLoginStatusMiddleware({
  casUserSessionName: cas.session_name,
  loggedInKey: config.loginStatusLoggedInKey,
  usernameKey: config.loginStatusUsernameKey,
});
app.use(createUnlessMiddleware(pass, loginStatusMiddleware));

// HTTP代理中间件
const proxyMiddlewareOptions = { proxyTable: config.proxyTable };
const proxyMiddlewares = createPimaProxyMiddlewares(proxyMiddlewareOptions);
if (proxyMiddlewares.length > 0) {
  app.use(proxyMiddlewares);
}

// 语言中间件
const localeMiddlewareOptions = {
  debug: !isProd,
  cookieDomain: config.cookieDomain,
  cookieSecure: config.cookieSecure,
  localeCookieName: config.localeCookieName,
  fallbackLocale: config.fallbackLocale,
};
const localeMiddleware = createPimaLocaleMiddleware(localeMiddlewareOptions);
app.use(localeMiddleware);

app.use((req, res, next) => {
  if (res.locals.appLocale) {
    if (res.locals.appLocale.indexOf('_') !== -1) {
      res.locals.appLocale = res.locals.appLocale.replace('_', '-');
    }
  }

  next();
});

// 密码过期校验 start
const getUserVerify = (tokenType, accessToken) => new Promise((rs, rj) => {
  request({
    url: config.userPreVerifyUrl,
    method: 'GET',
    json: true,
    headers: {
      Authorization: `${tokenType} ${accessToken}`,
    },
  }, (error, response, body) => {
    if (!error && response.statusCode === 200) {
      rs(body);
    } else {
      rj(error);
    }
  });
});
app.get('*', async (req, res, next) => {
  if (!req.xhr && config.userPreVerifyUrl && req.session.appToken) {
    const { tokenType, accessToken } = req.session.appToken;
    const localUrl = encodeURIComponent(`${config.serviceUrl}${req.originalUrl}`);
    if (accessToken) {
      try {
        const data = await getUserVerify(tokenType, accessToken);
        if (data.model?.errorCode === 488) {
          res.redirect(`${data.model.url}?service=${localUrl}`);
          return;
        }
      } catch (error) {
        console.log('@@getUserVerify.error', error);
      }
    }
  }
  next();
});
// 密码过期校验 end


// SSR泻染
const vueSSRMiddleware = pimaVueSSRMiddleware(app, {
  publicPath: config.publicPath,
  hotClientWebSocketPort: config.hotClientWebSocketPort,
  webpackClientConfig,
  webpackServerConfig,
  webpackOutputPath: resolve('../dist'),
  sourceAssetsPath: resolve('../src/assets'),
  templatePath: resolve('../index.template.html'),
  vueSSRServerBundlePath: resolve('../dist/vue-ssr-server-bundle.json'),
  vueSSRClientManifestPath: resolve('../dist/vue-ssr-client-manifest.json'),
  faviconUrl: config.faviconUrl,
  staticResourcesUrl: config.staticResourcesUrl,
});
app.use(createUnlessMiddleware(pass, vueSSRMiddleware));

// eslint-disable-next-line
// app.use((err, req, res, next) => {
//   res.sendStatus(err.status || 500).end();
// });

// app.use((err, req, res, next) => {
//   res.status(err.status || 500);
//   if (err.status) {
//     switch (err.status) {
//       case 401:
//         // 状态码必须为 301或302 才会跳转，默认302
//         res.redirect(`${config.errorPage404}?path=${config.serviceUrl}${config.publicPath}`);
//         break;
//       case 404:
//         res.redirect(`${config.errorPage404}?path=${config.serviceUrl}${config.publicPath}`);
//         break;
//       default: {
//         res.redirect(`${config.errorPage500}?path=${config.serviceUrl}${config.publicPath}`);
//         break;
//       }
//     }
//   }
//   next(err);
// });

// 错误处理
app.use((error, req, res, next) => {
  // eslint-disable-next-line no-console
  console.log(new Date().toString(), 'errorHandler', error);

  // 以下三种情况不需要处理
  // 1、没有出错
  // 2、AJAX请求
  // 3、不是GET请求
  if (!error
    || req.xhr
    || req.method.toLowerCase() !== 'get') {
    next();
    return;
  }

  switch (error.code) {
    case 'ROUTE_NOT_FOUND':
      res.redirect(`${config.errorPage404}?path=${config.serviceUrl}${config.publicPath}`);
      break;

    case 'LOGIN_REQUIRED':
    case 'F_GET_TOKEN_FAILED': // 无法获取Token，可能是CAS认证失败，由Token中间件输出
    case 'F_REFRESH_TOKEN_FAILED': // 无法刷新Token，可能是CAS认证失败，由Token中间件输出
      Object.assign(res.locals, {
        casLoginQuery: {
          locale: res.locals.appLocale,
        },
      });
      // CAS认证由这里发起
      // eslint-disable-next-line no-underscore-dangle
      cas._login(req, res, next);
      break;

    default:
      // eslint-disable-next-line no-console
      console.log('Internal server error', error.stack || '');
      res.redirect(`${config.errorPage500}?path=${config.serviceUrl}${config.publicPath}`);
      break;
  }
});

app.listen(config.expressPort, () => {
  // eslint-disable-next-line no-console
  console.log(`Express server listen in ${config.expressPort}`);
});
