<template>
  <div
    v-if="hasP((P)=>P.IntegralManagement.Detail)"
    class="page-panel"
  >
    <div class="content-panel">
      <GoBack
        :title="t('title.detailForIntegral')"
        @back="handleBack"
      />

      <div class="panel-body pd-80">
        <InfoBar :model="infoModel" />

        <TableList
          :scroll="tableScroll"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';

import operation from '@/mixins/operation';
import GoBack from '@/components/base/go-back-title.vue';
import { namespaceT } from '@/helps/namespace-t';


import TableList from './components/table-list.vue';
import InfoBar from './components/info-bar.vue';

export default {
  name: 'DetailList',

  components: {
    GoBack,
    TableList,
    InfoBar,
  },

  mixins: [operation],

  data() {
    return {
      submitting: false,
      t: namespaceT('integralManagement'),
      ti: namespaceT(),
    };
  },

  computed: {
    ...mapState({
      integralList: (state) => state.integralMgmt.dataSource,
      infoModel: (state) => state.integralMgmt.detail.userInfo,
    }),

    tableScroll() {
      return { y: 'calc(100vh - 264px)' };
    },
  },

  beforeMount() {
    this.fetchIntegralDetailList({
      id: this.$route.params.id,
      page: 1,
      pageSize: 10,
    });
  },
  beforeDestroy() {
    this.$store.commit('integralMgmt/resetDetails');
  },

  methods: {
    ...mapActions({
      fetchIntegralDetailList: 'integralMgmt/fetchIntegralDetailList',
    }),

    handleBack() {
      this.$router.back();
    },
  },
};
</script>


<style lang="less" scoped>
.drawer-bd {
  margin-bottom: 50px;
}
</style>
